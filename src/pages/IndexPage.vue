<template>
  <q-page class="flex flex-center">
    <!-- Grid container using containerStyle -->
    <div class="grid-container" :style="containerStyle">
      <!-- Each video wrapper gets its grid area via getItemStyle -->
      <div
        v-for="(_, index) in streamUrls"
        :key="index"
        class="video-wrapper"
        :style="getItemStyle(index)"
      >
        <video class="video-player" autoplay :muted="mutedState[index]"></video>
        <!-- Channel name overlay -->
        <div v-if="showChannelLabels" class="channel-label">
          {{ getChannelName(index) }}
        </div>
      </div>
    </div>

    <!-- Configuration menu -->
    <ConfigMenu
      v-model="showConfigMenu"
      :currentStreams="streamUrls"
      :currentMutedState="mutedState"
      :currentShowChannelLabels="showChannelLabels"
      @updateConfig="updateConfig"
      @updateChannelLabelsVisibility="updateChannelLabelsVisibility"
    />
  </q-page>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch, nextTick, shallowRef, readonly } from 'vue'
import Hls from 'hls.js'
import ConfigMenu from 'components/ConfigMenu.vue'

export default {
  name: 'IndexPage',
  components: { ConfigMenu },
  setup() {
    const DEFAULT_URL =
      'https://vs-hls-push-ww-live.akamaized.net/x=4/i=urn:bbc:pips:service:bbc_news_channel_hd/t=3840/v=pv10/b=1604032/main.m3u8'

    // Reactive state - using shallowRef for better performance with arrays
    const streamUrls = shallowRef(Array(2).fill(DEFAULT_URL))
    const mutedState = shallowRef(Array(2).fill(true)) // Default all streams to muted
    const showConfigMenu = ref(false)
    const showChannelLabels = ref(true) // Default to showing channel labels
    const hlsInstances = shallowRef([]) // Track HLS instances for cleanup
    const channelData = shallowRef({}) // Store channel name mappings from API
    const channelNameCache = new Map() // Cache for computed channel names
    const isChannelDataLoaded = ref(false) // Track loading state

    // Grid layout configurations - frozen for performance
    const GRID_LAYOUTS = readonly({
      1: { columns: '1fr', rows: '1fr' },
      2: { columns: 'repeat(2, 1fr)', rows: '1fr' },
      4: { columns: 'repeat(2, 1fr)', rows: 'repeat(2, 1fr)' },
      5: { columns: 'repeat(3, 1fr)', rows: 'repeat(4, 1fr)' },
      6: { columns: 'repeat(3, 1fr)', rows: 'repeat(3, 1fr)' },
      9: { columns: 'repeat(3, 1fr)', rows: 'repeat(3, 1fr)' }
    })

    // Base grid style properties - frozen for performance
    const BASE_GRID_STYLE = readonly({
      display: 'grid',
      gap: '0px',
      width: '100vw',
      height: '100vh'
    })

    // Computed grid container style
    const containerStyle = computed(() => {
      const num = streamUrls.value.length
      const layout = GRID_LAYOUTS[num]

      if (layout) {
        return {
          ...BASE_GRID_STYLE,
          gridTemplateColumns: layout.columns,
          gridTemplateRows: layout.rows
        }
      }

      // Default layout for other numbers
      const cols = Math.ceil(Math.sqrt(num))
      const rows = Math.ceil(num / cols)
      return {
        ...BASE_GRID_STYLE,
        gridTemplateColumns: `repeat(${cols}, 1fr)`,
        gridTemplateRows: `repeat(${rows}, 1fr)`
      }
    })

    // Grid area configurations for specific layouts - frozen for performance
    const GRID_AREAS = readonly({
      1: Object.freeze(['1 / 1 / 2 / 2']),
      2: Object.freeze(['1 / 1 / 2 / 2', '1 / 2 / 2 / 3']),
      4: Object.freeze([
        '1 / 1 / 2 / 2', '1 / 2 / 2 / 3',
        '2 / 1 / 3 / 2', '2 / 2 / 3 / 3'
      ]),
      5: Object.freeze([
        '1 / 1 / 5 / 3', // Large left panel
        '1 / 3 / 2 / 4', '2 / 3 / 3 / 4',
        '3 / 3 / 4 / 4', '4 / 3 / 5 / 4'
      ]),
      6: Object.freeze([
        '1 / 1 / 3 / 3', // Large top-left panel
        '3 / 1 / 4 / 2', '3 / 2 / 4 / 3', '3 / 3 / 4 / 4',
        '2 / 3 / 3 / 4', '1 / 3 / 2 / 4'
      ]),
      9: Object.freeze([
        '1 / 1 / 2 / 2', '1 / 2 / 2 / 3', '1 / 3 / 2 / 4',
        '2 / 1 / 3 / 2', '2 / 2 / 3 / 3', '2 / 3 / 3 / 4',
        '3 / 1 / 4 / 2', '3 / 2 / 4 / 3', '3 / 3 / 4 / 4'
      ])
    })

    // Cache for grid item styles to avoid recalculation
    const gridItemStyleCache = new Map()

    // Get grid area style for a specific stream index with caching
    const getItemStyle = (index) => {
      const num = streamUrls.value.length
      const cacheKey = `${num}-${index}`

      // Check cache first
      if (gridItemStyleCache.has(cacheKey)) {
        return gridItemStyleCache.get(cacheKey)
      }

      const areas = GRID_AREAS[num]
      let style = {}

      if (areas && areas[index]) {
        style = Object.freeze({ gridArea: areas[index] })
      }

      // Cache the result
      gridItemStyleCache.set(cacheKey, style)
      return style
    }

    // Constants for channel API
    const CHANNELS_API_URL = 'https://trleahy.github.io/API-Endpoints/visorChannels/channels.json'

    // Load channel data from API with retry logic and caching
    const loadChannelData = async (retryCount = 0) => {
      if (isChannelDataLoaded.value) return // Already loaded

      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 10000) // 10s timeout

        const response = await fetch(CHANNELS_API_URL, {
          signal: controller.signal,
          cache: 'force-cache' // Use browser cache when available
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        // Create URL to name mapping more efficiently
        const urlToNameMap = Object.create(null) // No prototype for better performance
        for (const key in data) {
          if (data[key]?.url && data[key]?.name) {
            urlToNameMap[data[key].url] = data[key].name
          }
        }

        channelData.value = urlToNameMap
        isChannelDataLoaded.value = true
        channelNameCache.clear() // Clear cache when new data is loaded

      } catch (error) {
        console.error('Failed to load channel data:', error)

        // Retry logic for network errors
        if (retryCount < 2 && (error.name === 'AbortError' || error.name === 'TypeError')) {
          console.log(`Retrying channel data load (attempt ${retryCount + 1})`)
          setTimeout(() => loadChannelData(retryCount + 1), 2000 * (retryCount + 1))
        } else {
          channelData.value = Object.create(null)
          isChannelDataLoaded.value = true // Mark as loaded even if failed
        }
      }
    }

    // Get display name for a channel at given index with caching
    const getChannelName = (index) => {
      const streamUrl = streamUrls.value[index]
      if (!streamUrl) {
        return `Stream ${index + 1}`
      }

      // Check cache first
      if (channelNameCache.has(streamUrl)) {
        return channelNameCache.get(streamUrl)
      }

      let channelName

      // Check if we have a name for this URL from the API
      const apiChannelName = channelData.value[streamUrl]
      if (apiChannelName) {
        channelName = apiChannelName
      } else if (streamUrl !== DEFAULT_URL) {
        // For custom URLs, try to extract a meaningful name
        try {
          const url = new URL(streamUrl)
          const hostname = url.hostname
          // Remove common prefixes and suffixes
          const cleanHostname = hostname
            .replace(/^(?:www\.|m\.|mobile\.)/, '')
            .replace(/\.(?:com|org|net|tv|co\.uk)$/, '')

          if (cleanHostname && cleanHostname !== 'localhost') {
            channelName = cleanHostname.charAt(0).toUpperCase() + cleanHostname.slice(1)
          } else {
            channelName = 'Custom'
          }
        } catch {
          channelName = 'Custom'
        }
      } else {
        // Default stream name
        channelName = `Stream ${index + 1}`
      }

      // Cache the result
      channelNameCache.set(streamUrl, channelName)
      return channelName
    }

    // Clean up existing HLS instances
    const cleanupHlsInstances = () => {
      hlsInstances.value.forEach(hls => {
        if (hls) {
          hls.destroy()
        }
      })
      hlsInstances.value = []
    }

    // Attach HLS streams to video elements with optimized performance
    const attachStreams = () => {
      // Clean up existing instances first
      cleanupHlsInstances()

      // Use requestAnimationFrame to avoid blocking the main thread
      requestAnimationFrame(() => {
        const videos = document.querySelectorAll('.video-player')
        const videoArray = Array.from(videos) // Convert to array once

        videoArray.forEach((video, index) => {
          const streamUrl = streamUrls.value[index] || DEFAULT_URL

          try {
            if (Hls.isSupported()) {
              const hls = new Hls({
                enableWorker: false, // Disable worker for better compatibility
                lowLatencyMode: true,
                maxBufferLength: 30, // Reduce buffer for lower latency
                maxMaxBufferLength: 60,
                startLevel: -1, // Auto quality selection
                capLevelToPlayerSize: true // Optimize quality for player size
              })

              // Optimized error handling with debouncing
              let errorTimeout
              hls.on(Hls.Events.ERROR, (_, data) => {
                clearTimeout(errorTimeout)
                errorTimeout = setTimeout(() => {
                  console.error(`HLS Error for stream ${index}:`, data)
                  if (data.fatal) {
                    // Try to recover from fatal errors
                    switch (data.type) {
                      case Hls.ErrorTypes.NETWORK_ERROR:
                        hls.startLoad()
                        break
                      case Hls.ErrorTypes.MEDIA_ERROR:
                        hls.recoverMediaError()
                        break
                      default:
                        hls.destroy()
                        hlsInstances.value[index] = null
                        break
                    }
                  }
                }, 100) // Debounce errors
              })

              hls.loadSource(streamUrl)
              hls.attachMedia(video)
              hlsInstances.value[index] = hls
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
              // Native HLS support (Safari)
              video.src = streamUrl
            } else {
              console.warn(`HLS not supported for stream ${index}`)
              video.src = streamUrl // Fallback
            }

            // Set muted state efficiently
            const shouldBeMuted = mutedState.value[index] ?? true
            if (video.muted !== shouldBeMuted) {
              video.muted = shouldBeMuted
            }
          } catch (error) {
            console.error(`Error attaching stream ${index}:`, error)
          }
        })
      })
    }

    // Keyboard event handler for opening config menu
    const handleKeyDown = (event) => {
      if (event.key === 'F2') {
        showConfigMenu.value = true
      }
    }

    // Update muted state for all video elements with optimization
    const updateVideoMutedState = () => {
      requestAnimationFrame(() => {
        const videos = document.querySelectorAll('.video-player')
        for (let index = 0; index < videos.length; index++) {
          const video = videos[index]
          const shouldBeMuted = mutedState.value[index]

          if (video && shouldBeMuted !== undefined && video.muted !== shouldBeMuted) {
            video.muted = shouldBeMuted
          }
        }
      })
    }

    // Configuration update handler
    const updateConfig = (config) => {
      if (config.streams && Array.isArray(config.streams)) {
        streamUrls.value = config.streams
      }
      if (config.mutedState && Array.isArray(config.mutedState)) {
        mutedState.value = config.mutedState
      }
      if (config.showChannelLabels !== undefined) {
        showChannelLabels.value = config.showChannelLabels
      }
    }

    // Handle channel labels visibility update
    const updateChannelLabelsVisibility = (visible) => {
      showChannelLabels.value = visible
    }

    // Lifecycle hooks
    onMounted(async () => {
      // Load channel data first
      await loadChannelData()

      nextTick(() => {
        attachStreams()
      })
      window.addEventListener('keydown', handleKeyDown)
    })

    onUnmounted(() => {
      window.removeEventListener('keydown', handleKeyDown)
      cleanupHlsInstances()

      // Clear all timeouts and caches
      clearTimeout(streamUpdateTimeout)
      clearTimeout(mutedUpdateTimeout)
      channelNameCache.clear()
      gridItemStyleCache.clear()
    })

    // Optimized watchers with debouncing
    let streamUpdateTimeout
    watch(streamUrls, () => {
      clearTimeout(streamUpdateTimeout)
      streamUpdateTimeout = setTimeout(() => {
        nextTick(() => {
          attachStreams()
          channelNameCache.clear() // Clear cache when streams change
          gridItemStyleCache.clear() // Clear grid cache when streams change
        })
      }, 100) // Debounce rapid changes
    }, { flush: 'post' }) // Use post flush for better performance

    let mutedUpdateTimeout
    watch(mutedState, () => {
      clearTimeout(mutedUpdateTimeout)
      mutedUpdateTimeout = setTimeout(() => {
        updateVideoMutedState()
      }, 50) // Shorter debounce for mute changes
    }, { flush: 'post' })

    // Watch for channel data changes with minimal overhead
    watch(channelData, () => {
      channelNameCache.clear() // Clear cache when channel data changes
    }, { flush: 'post' })

    return {
      streamUrls,
      mutedState,
      showConfigMenu,
      showChannelLabels,
      containerStyle,
      getItemStyle,
      getChannelName,
      updateConfig,
      updateChannelLabelsVisibility
    }
  },
}
</script>

<style scoped>
.grid-container {
  width: 100vw;
  height: 100vh;
  background: #000;
}

.video-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain; /* letterboxes to maintain aspect ratio */
  object-position: center;
  background: black;
}

.channel-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  font-family: 'Roboto', sans-serif;
  z-index: 10;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: opacity 0.3s ease;
  /* Performance optimizations */
  will-change: opacity;
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.channel-label:hover {
  opacity: 0.9;
  background: rgba(0, 0, 0, 0.85);
}

/* Responsive font sizing */
@media (max-width: 768px) {
  .channel-label {
    font-size: 20px;
    padding: 3px 6px;
  }
}

@media (min-width: 1200px) {
  .channel-label {
    font-size: 28px;
    padding: 6px 10px;
  }
}
</style>

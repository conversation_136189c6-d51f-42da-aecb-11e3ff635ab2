<template>
  <q-dialog v-model="show">
    <q-card class="config-menu">
      <q-card-section>
        <div class="text-h6">Configuration</div>
      </q-card-section>

      <!-- Select number of streams -->
      <q-card-section>
        <q-select
          v-model="streamCount"
          :options="streamCountOptions"
          label="Number of Streams"
          dense
          outlined
        />
      </q-card-section>

      <!-- Channel Name Labels Toggle -->
      <q-card-section>
        <q-toggle
          v-model="showChannelLabels"
          label="Show Channel Name Labels"
          color="primary"
          @update:model-value="updateChannelLabelsVisibility"
        />
      </q-card-section>

      <!-- List of channel selectors -->
      <q-card-section>
        <q-list>
          <q-item v-for="(_, index) in streamCount" :key="index">
            <q-item-section>
              <q-select
                v-model="selectedStreams[index]"
                :options="streamOptions"
                :label="'Select Channel for Stream ' + (index + 1)"
                emit-value
                map-options
                option-label="label"
                option-value="value"
                dense
                outlined
                @update:model-value="updateStream(index)"
              />
              <q-input
                v-if="selectedStreams[index] === 'custom'"
                v-model="customUrls[index]"
                label="Enter Custom URL"
                dense
                filled
              />
              <div class="q-mt-sm">
                <q-toggle
                  v-model="mutedStreams[index]"
                  label="Mute"
                  color="primary"
                />
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <!-- Preset Management -->
      <q-card-section>
        <div class="text-subtitle2 q-mb-sm">Preset Management</div>
        <div class="row q-gutter-sm">
          <q-btn
            label="Save Preset"
            color="secondary"
            outline
            @click="savePreset"
            :loading="savingPreset"
          />
          <q-btn
            label="Load Preset"
            color="secondary"
            outline
            @click="loadPreset"
            :loading="loadingPreset"
          />
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn label="Cancel" color="grey" v-close-popup />
        <q-btn label="Apply" color="primary" @click="applyConfig" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script>
import { ref, watch, onMounted } from 'vue'
import { useQuasar } from 'quasar'

export default {
  name: 'ConfigMenu',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    currentStreams: {
      type: Array,
      default: () => [],
      validator: (value) => Array.isArray(value)
    },
    currentMutedState: {
      type: Array,
      default: () => [],
      validator: (value) => Array.isArray(value)
    },
    currentShowChannelLabels: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:modelValue', 'updateConfig', 'updateChannelLabelsVisibility'],
  setup(props, { emit }) {
    // Quasar instance for notifications
    const $q = useQuasar()

    // Reactive state
    const show = ref(props.modelValue)
    const streamCountOptions = ref([1, 2, 4, 5, 6, 9])
    const streamCount = ref(Math.max(1, props.currentStreams.length || 1))

    const availableStreams = ref([])
    const streamOptions = ref([])
    const selectedStreams = ref([])
    const customUrls = ref([])
    const mutedStreams = ref([])
    const showChannelLabels = ref(props.currentShowChannelLabels)
    const savingPreset = ref(false)
    const loadingPreset = ref(false)

    // Constants
    const CHANNELS_API_URL = 'https://trleahy.github.io/API-Endpoints/visorChannels/channels.json'
    const CUSTOM_OPTION = { label: 'Custom URL', value: 'custom' }

    // Load available channels from the API
    const loadChannels = async () => {
      try {
        const response = await fetch(CHANNELS_API_URL)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        availableStreams.value = Object.keys(data).map((key) => ({
          label: data[key].name,
          value: data[key].url
        }))

        streamOptions.value = [...availableStreams.value, CUSTOM_OPTION]
      } catch (error) {
        console.error('Failed to load channels:', error)
        // Fallback to custom option only
        streamOptions.value = [CUSTOM_OPTION]
      }
    }

    // Initialize component
    onMounted(async () => {
      await loadChannels()
      initializeStreams()
    })

    // Initialize stream arrays based on current configuration
    const initializeStreams = () => {
      const count = streamCount.value
      const defaultStreamValue = streamOptions.value[0]?.value || 'custom'

      // Reset arrays
      selectedStreams.value = []
      customUrls.value = []
      mutedStreams.value = []

      // Initialize each stream
      for (let i = 0; i < count; i++) {
        // Set stream selection (use current stream if available)
        const currentStream = props.currentStreams[i]
        const matchingOption = streamOptions.value.find(option => option.value === currentStream)

        if (matchingOption) {
          selectedStreams.value.push(matchingOption.value)
          customUrls.value.push('')
        } else if (currentStream) {
          selectedStreams.value.push('custom')
          customUrls.value.push(currentStream)
        } else {
          selectedStreams.value.push(defaultStreamValue)
          customUrls.value.push('')
        }

        // Initialize muted state (default to true if not specified)
        mutedStreams.value.push(props.currentMutedState[i] ?? true)
      }
    }

    // Handle stream count changes
    watch(streamCount, (newCount, oldCount) => {
      const defaultStreamValue = streamOptions.value[0]?.value || 'custom'

      if (newCount > oldCount) {
        // Add new streams
        for (let i = oldCount; i < newCount; i++) {
          selectedStreams.value.push(defaultStreamValue)
          customUrls.value.push('')
          mutedStreams.value.push(true) // Default new streams to muted
        }
      } else if (newCount < oldCount) {
        // Remove excess streams
        selectedStreams.value.splice(newCount)
        customUrls.value.splice(newCount)
        mutedStreams.value.splice(newCount)
      }
    })

    // Handle dialog visibility
    watch(
      () => props.modelValue,
      (newVal) => {
        show.value = newVal
        if (newVal) {
          // Reinitialize when dialog opens to sync with current state
          initializeStreams()
          showChannelLabels.value = props.currentShowChannelLabels
        }
      }
    )

    watch(show, (newVal) => {
      emit('update:modelValue', newVal)
    })

    // Watch for changes in the channel labels prop
    watch(
      () => props.currentShowChannelLabels,
      (newVal) => {
        showChannelLabels.value = newVal
      }
    )

    // Clear custom URL when switching away from custom option
    const updateStream = (index) => {
      if (selectedStreams.value[index] !== 'custom') {
        customUrls.value[index] = ''
      }
    }

    // Handle channel labels visibility change
    const updateChannelLabelsVisibility = (visible) => {
      emit('updateChannelLabelsVisibility', visible)
    }

    // Save current configuration as preset
    const savePreset = async () => {
      if (!window.visorAPI) {
        console.error('Visor API not available')
        return
      }

      savingPreset.value = true
      try {
        const presetData = {
          version: '1.0',
          timestamp: new Date().toISOString(),
          config: {
            streamCount: streamCount.value,
            streams: selectedStreams.value.map((stream, index) => {
              if (stream === 'custom') {
                return customUrls.value[index]?.trim() || ''
              }
              return stream
            }),
            mutedState: [...mutedStreams.value],
            showChannelLabels: showChannelLabels.value
          }
        }

        const result = await window.visorAPI.savePreset(presetData)

        if (result.success) {
          $q.notify({
            type: 'positive',
            message: `Preset saved successfully to ${result.filename}`,
            position: 'top',
            timeout: 3000,
            actions: [
              { icon: 'close', color: 'white', round: true, handler: () => {} }
            ]
          })
        } else {
          $q.notify({
            type: 'negative',
            message: `Failed to save preset: ${result.error}`,
            position: 'top',
            timeout: 5000,
            actions: [
              { icon: 'close', color: 'white', round: true, handler: () => {} }
            ]
          })
        }
      } catch (error) {
        $q.notify({
          type: 'negative',
          message: `Failed to save preset: ${error.message}`,
          position: 'top',
          timeout: 5000,
          actions: [
            { icon: 'close', color: 'white', round: true, handler: () => {} }
          ]
        })
      } finally {
        savingPreset.value = false
      }
    }

    // Load preset from file
    const loadPreset = async () => {
      if (!window.visorAPI) {
        console.error('Visor API not available')
        return
      }

      loadingPreset.value = true
      try {
        const result = await window.visorAPI.loadPreset()

        if (result.success && result.data) {
          const config = result.data.config

          if (config) {
            // Update stream count
            if (config.streamCount) {
              streamCount.value = config.streamCount
            }

            // Update channel labels visibility
            if (config.showChannelLabels !== undefined) {
              showChannelLabels.value = config.showChannelLabels
              emit('updateChannelLabelsVisibility', config.showChannelLabels)
            }

            // Wait for stream count change to propagate, then update streams
            await new Promise(resolve => setTimeout(resolve, 100))

            // Update streams and muted state
            if (config.streams && Array.isArray(config.streams)) {
              config.streams.forEach((stream, index) => {
                if (index < selectedStreams.value.length) {
                  const matchingOption = streamOptions.value.find(option => option.value === stream)
                  if (matchingOption) {
                    selectedStreams.value[index] = matchingOption.value
                    customUrls.value[index] = ''
                  } else if (stream) {
                    selectedStreams.value[index] = 'custom'
                    customUrls.value[index] = stream
                  }
                }
              })
            }

            if (config.mutedState && Array.isArray(config.mutedState)) {
              config.mutedState.forEach((muted, index) => {
                if (index < mutedStreams.value.length) {
                  mutedStreams.value[index] = muted
                }
              })
            }

            console.log('Preset loaded successfully')
            // You could show a success notification here
          }
        } else if (!result.canceled) {
          console.error('Failed to load preset:', result.error)
          // You could show an error notification here
        }
      } catch (error) {
        console.error('Error loading preset:', error)
      } finally {
        loadingPreset.value = false
      }
    }

    // Apply configuration changes
    const applyConfig = () => {
      const finalStreams = selectedStreams.value.map((stream, index) => {
        if (stream === 'custom') {
          const customUrl = customUrls.value[index]?.trim()
          return customUrl || ''
        }
        return stream
      })

      // Validate that we have valid streams
      const validStreams = finalStreams.filter(stream => stream && stream.length > 0)
      if (validStreams.length === 0) {
        console.warn('No valid streams configured')
        return
      }

      emit('updateConfig', {
        streams: finalStreams,
        mutedState: [...mutedStreams.value],
        showChannelLabels: showChannelLabels.value
      })
      show.value = false
    }

    return {
      show,
      streamCountOptions,
      streamCount,
      streamOptions,
      selectedStreams,
      customUrls,
      mutedStreams,
      showChannelLabels,
      savingPreset,
      loadingPreset,
      updateStream,
      updateChannelLabelsVisibility,
      savePreset,
      loadPreset,
      applyConfig,
    }
  },
}
</script>

<style scoped>
.config-menu {
  width: 400px;
}
</style>

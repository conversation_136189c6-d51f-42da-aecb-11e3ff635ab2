// src-electron/electron-main.js
import { app, BrowserWindow, ipcMain, dialog } from "electron";
import path from "node:path";
import os from "node:os";
import fs from "node:fs/promises";
import { fileURLToPath } from "node:url";
var platform = process.platform || os.platform();
var currentDir = fileURLToPath(new URL(".", import.meta.url));
var mainWindow;
async function createWindow() {
  const width = 1e3;
  const height = Math.round(width / 1.78);
  mainWindow = new BrowserWindow({
    icon: path.resolve(currentDir, "icons/icon.png"),
    // tray icon
    width,
    height,
    useContentSize: true,
    // frame: false,
    // fullscreen: true,
    webPreferences: {
      sandbox: false,
      contextIsolation: true,
      preload: path.resolve(
        currentDir,
        path.join(
          "/Users/<USER>/GitHub/visor/.quasar/dev-electron/preload",
          "electron-preload.cjs"
        )
      )
    }
  });
  mainWindow.setAspectRatio(16 / 9);
  if (true) {
    await mainWindow.loadURL("http://localhost:9300");
  } else {
    await mainWindow.loadFile("index.html");
  }
  if (true) {
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.webContents.on("devtools-opened", () => {
      mainWindow.webContents.closeDevTools();
    });
  }
  mainWindow.on("closed", () => {
    mainWindow = null;
  });
}
var getPresetDirectory = () => {
  const homeDir = os.homedir();
  return path.join(homeDir, "Documents", "Visor");
};
var ensurePresetDirectory = async () => {
  const presetDir = getPresetDirectory();
  try {
    await fs.access(presetDir);
  } catch (error) {
    if (error.code === "ENOENT") {
      await fs.mkdir(presetDir, { recursive: true });
    } else {
      throw error;
    }
  }
  return presetDir;
};
ipcMain.handle("save-preset", async (event, presetData) => {
  try {
    const presetDir = await ensurePresetDirectory();
    const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-").slice(0, 19);
    const filename = `visor-preset-${timestamp}.json`;
    const filePath = path.join(presetDir, filename);
    await fs.writeFile(filePath, JSON.stringify(presetData, null, 2), "utf8");
    return {
      success: true,
      filePath,
      filename
    };
  } catch (error) {
    console.error("Error saving preset:", error);
    return {
      success: false,
      error: error.message
    };
  }
});
ipcMain.handle("load-preset", async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: "Load Visor Preset",
      defaultPath: getPresetDirectory(),
      filters: [
        { name: "Visor Presets", extensions: ["json"] },
        { name: "All Files", extensions: ["*"] }
      ],
      properties: ["openFile"]
    });
    if (result.canceled || !result.filePaths.length) {
      return { success: false, canceled: true };
    }
    const filePath = result.filePaths[0];
    const fileContent = await fs.readFile(filePath, "utf8");
    const presetData = JSON.parse(fileContent);
    return {
      success: true,
      data: presetData,
      filePath
    };
  } catch (error) {
    console.error("Error loading preset:", error);
    return {
      success: false,
      error: error.message
    };
  }
});
ipcMain.handle("show-save-dialog", async (event, options) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      title: "Save Visor Preset",
      defaultPath: path.join(getPresetDirectory(), "visor-preset.json"),
      filters: [
        { name: "Visor Presets", extensions: ["json"] },
        { name: "All Files", extensions: ["*"] }
      ],
      ...options
    });
    return result;
  } catch (error) {
    console.error("Error showing save dialog:", error);
    return { canceled: true, error: error.message };
  }
});
ipcMain.handle("show-open-dialog", async (event, options) => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: "Load Visor Preset",
      defaultPath: getPresetDirectory(),
      filters: [
        { name: "Visor Presets", extensions: ["json"] },
        { name: "All Files", extensions: ["*"] }
      ],
      properties: ["openFile"],
      ...options
    });
    return result;
  } catch (error) {
    console.error("Error showing open dialog:", error);
    return { canceled: true, error: error.message };
  }
});
app.setAboutPanelOptions({
  applicationName: "Visor",
  applicationVersion: "v1.0.0-alpha",
  version: "Build v1.0.1",
  copyright: "\xA9 2025. All rights reserved."
});
app.whenReady().then(() => {
  createWindow();
});
app.on("window-all-closed", () => {
  if (platform !== "darwin") {
    app.quit();
  }
});
app.on("activate", () => {
  if (mainWindow === null) {
    createWindow();
  }
});
//# sourceMappingURL=data:application/json;base64,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

// src-electron/electron-preload.js
var import_electron = require("electron");
import_electron.contextBridge.exposeInMainWorld("visorAPI", {
  // Save preset to file
  savePreset: (presetData) => import_electron.ipcRenderer.invoke("save-preset", presetData),
  // Load preset from file
  loadPreset: () => import_electron.ipcRenderer.invoke("load-preset"),
  // Show save dialog
  showSaveDialog: (options) => import_electron.ipcRenderer.invoke("show-save-dialog", options),
  // Show open dialog
  showOpenDialog: (options) => import_electron.ipcRenderer.invoke("show-open-dialog", options)
});
//# sourceMappingURL=data:application/json;base64,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

import{app as o,<PERSON><PERSON><PERSON><PERSON><PERSON>ow as r}from"electron";import n from"node:path";import l from"node:os";import{fileURLToPath as a}from"node:url";var c=process.platform||l.platform(),t=a(new URL(".",import.meta.url)),e;async function i(){let s=Math.round(561.7977528089888);e=new r({icon:n.resolve(t,"icons/icon.png"),width:1e3,height:s,useContentSize:!0,webPreferences:{sandbox:!1,contextIsolation:!0,preload:n.resolve(t,n.join("preload","electron-preload.cjs"))}}),e.setAspectRatio(16/9),await e.loadFile("index.html"),e.webContents.on("devtools-opened",()=>{e.webContents.closeDevTools()}),e.on("closed",()=>{e=null})}o.whenReady().then(i);o.on("window-all-closed",()=>{c!=="darwin"&&o.quit()});o.on("activate",()=>{e===null&&i()});

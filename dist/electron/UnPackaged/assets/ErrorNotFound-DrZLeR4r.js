import{i as o}from"./QBtn-B1hLEsCD.js";import{Z as s,t as l,Y as t,x as r}from"./index-Dj-TVJr_.js";import"./dom-DgPyGaJm.js";const a={class:"fullscreen bg-blue text-white text-center q-pa-md flex flex-center"},m={__name:"ErrorNotFound",setup(n){return(i,e)=>(l(),s("div",a,[t("div",null,[e[0]||(e[0]=t("div",{style:{"font-size":"30vh"}}," 404 ",-1)),e[1]||(e[1]=t("div",{class:"text-h2",style:{opacity:".4"}}," Oops. Nothing here... ",-1)),r(o,{class:"q-mt-xl",color:"white","text-color":"blue",unelevated:"",to:"/",label:"Go Home","no-caps":""})])]))}};export{m as default};

import{c as L,g as $,i as G,e as P,l as N,a as g,h as m,p as V,b as J,w as _,o as T,d as H,n as D,f as q,r as y,j as A,k as F,m as x,q as X,s as Y,t as Z,u as k,v as ee,x as M}from"./index-Dj-TVJr_.js";import{h as te,a as ne}from"./dom-DgPyGaJm.js";import{s as oe,g as ie,a as le,b as re,c as R,_ as ae}from"./_plugin-vue_export-helper-LJ0ELJAz.js";const se=L({name:"QPageContainer",setup(e,{slots:p}){const{proxy:{$q:n}}=$(),t=G(N,P);if(t===P)return console.error("QPageContainer needs to be child of QLayout"),P;V(J,!0);const r=g(()=>{const a={};return t.header.space===!0&&(a.paddingTop=`${t.header.size}px`),t.right.space===!0&&(a[`padding${n.lang.rtl===!0?"Left":"Right"}`]=`${t.right.size}px`),t.footer.space===!0&&(a.paddingBottom=`${t.footer.size}px`),t.left.space===!0&&(a[`padding${n.lang.rtl===!0?"Right":"Left"}`]=`${t.left.size}px`),a});return()=>m("div",{class:"q-page-container",style:r.value},te(p.default))}}),{passive:W}=q,ce=["both","horizontal","vertical"],ue=L({name:"QScrollObserver",props:{axis:{type:String,validator:e=>ce.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:oe},emits:["scroll"],setup(e,{emit:p}){const n={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}};let t=null,r,a;_(()=>e.scrollTarget,()=>{l(),v()});function s(){t?.();const c=Math.max(0,le(r)),w=re(r),d={top:c-n.position.top,left:w-n.position.left};if(e.axis==="vertical"&&d.top===0||e.axis==="horizontal"&&d.left===0)return;const z=Math.abs(d.top)>=Math.abs(d.left)?d.top<0?"up":"down":d.left<0?"left":"right";n.position={top:c,left:w},n.directionChanged=n.direction!==z,n.delta=d,n.directionChanged===!0&&(n.direction=z,n.inflectionPoint=n.position),p("scroll",{...n})}function v(){r=ie(a,e.scrollTarget),r.addEventListener("scroll",i,W),i(!0)}function l(){r!==void 0&&(r.removeEventListener("scroll",i,W),r=void 0)}function i(c){if(c===!0||e.debounce===0||e.debounce==="0")s();else if(t===null){const[w,d]=e.debounce?[setTimeout(s,e.debounce),clearTimeout]:[requestAnimationFrame(s),cancelAnimationFrame];t=()=>{d(w),t=null}}}const{proxy:h}=$();return _(()=>h.$q.lang.rtl,s),T(()=>{a=h.$el.parentNode,v()}),H(()=>{t?.(),l()}),Object.assign(h,{trigger:i,getPosition:()=>n}),D}});function de(){const e=y(!A.value);return e.value===!1&&T(()=>{e.value=!0}),{isHydrated:e}}const K=typeof ResizeObserver<"u",j=K===!0?{}:{style:"display:block;position:absolute;top:0;left:0;right:0;bottom:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1;",url:"about:blank"},B=L({name:"QResizeObserver",props:{debounce:{type:[String,Number],default:100}},emits:["resize"],setup(e,{emit:p}){let n=null,t,r={width:-1,height:-1};function a(l){l===!0||e.debounce===0||e.debounce==="0"?s():n===null&&(n=setTimeout(s,e.debounce))}function s(){if(n!==null&&(clearTimeout(n),n=null),t){const{offsetWidth:l,offsetHeight:i}=t;(l!==r.width||i!==r.height)&&(r={width:l,height:i},p("resize",r))}}const{proxy:v}=$();if(v.trigger=a,K===!0){let l;const i=h=>{t=v.$el.parentNode,t?(l=new ResizeObserver(a),l.observe(t),s()):h!==!0&&F(()=>{i(!0)})};return T(()=>{i()}),H(()=>{n!==null&&clearTimeout(n),l!==void 0&&(l.disconnect!==void 0?l.disconnect():t&&l.unobserve(t))}),D}else{let l=function(){n!==null&&(clearTimeout(n),n=null),c!==void 0&&(c.removeEventListener!==void 0&&c.removeEventListener("resize",a,q.passive),c=void 0)},i=function(){l(),t?.contentDocument&&(c=t.contentDocument.defaultView,c.addEventListener("resize",a,q.passive),s())};const{isHydrated:h}=de();let c;return T(()=>{F(()=>{t=v.$el,t&&i()})}),H(l),()=>{if(h.value===!0)return m("object",{class:"q--avoid-card-border",style:j.style,tabindex:-1,type:"text/html",data:j.url,"aria-hidden":"true",onLoad:i})}}}}),fe=L({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(e,{slots:p,emit:n}){const{proxy:{$q:t}}=$(),r=y(null),a=y(t.screen.height),s=y(e.container===!0?0:t.screen.width),v=y({position:0,direction:"down",inflectionPoint:0}),l=y(0),i=y(A.value===!0?0:R()),h=g(()=>"q-layout q-layout--"+(e.container===!0?"containerized":"standard")),c=g(()=>e.container===!1?{minHeight:t.screen.height+"px"}:null),w=g(()=>i.value!==0?{[t.lang.rtl===!0?"left":"right"]:`${i.value}px`}:null),d=g(()=>i.value!==0?{[t.lang.rtl===!0?"right":"left"]:0,[t.lang.rtl===!0?"left":"right"]:`-${i.value}px`,width:`calc(100% + ${i.value}px)`}:null);function z(o){if(e.container===!0||document.qScrollPrevented!==!0){const u={position:o.position.top,direction:o.direction,directionChanged:o.directionChanged,inflectionPoint:o.inflectionPoint.top,delta:o.delta.top};v.value=u,e.onScroll!==void 0&&n("scroll",u)}}function U(o){const{height:u,width:b}=o;let f=!1;a.value!==u&&(f=!0,a.value=u,e.onScrollHeight!==void 0&&n("scrollHeight",u),E()),s.value!==b&&(f=!0,s.value=b),f===!0&&e.onResize!==void 0&&n("resize",o)}function I({height:o}){l.value!==o&&(l.value=o,E())}function E(){if(e.container===!0){const o=a.value>l.value?R():0;i.value!==o&&(i.value=o)}}let S=null;const O={instances:{},view:g(()=>e.view),isContainer:g(()=>e.container),rootRef:r,height:a,containerHeight:l,scrollbarWidth:i,totalWidth:g(()=>s.value+i.value),rows:g(()=>{const o=e.view.toLowerCase().split(" ");return{top:o[0].split(""),middle:o[1].split(""),bottom:o[2].split("")}}),header:x({size:0,offset:0,space:!1}),right:x({size:300,offset:0,space:!1}),footer:x({size:0,offset:0,space:!1}),left:x({size:300,offset:0,space:!1}),scroll:v,animate(){S!==null?clearTimeout(S):document.body.classList.add("q-body--layout-animate"),S=setTimeout(()=>{S=null,document.body.classList.remove("q-body--layout-animate")},155)},update(o,u,b){O[o][u]=b}};if(V(N,O),R()>0){let o=function(){f=null,C.classList.remove("hide-scrollbar")},u=function(){if(f===null){if(C.scrollHeight>t.screen.height)return;C.classList.add("hide-scrollbar")}else clearTimeout(f);f=setTimeout(o,300)},b=function(Q){f!==null&&Q==="remove"&&(clearTimeout(f),o()),window[`${Q}EventListener`]("resize",u)},f=null;const C=document.body;_(()=>e.container!==!0?"add":"remove",b),e.container!==!0&&b("add"),X(()=>{b("remove")})}return()=>{const o=ne(p.default,[m(ue,{onScroll:z}),m(B,{onResize:U})]),u=m("div",{class:h.value,style:c.value,ref:e.container===!0?void 0:r,tabindex:-1},o);return e.container===!0?m("div",{class:"q-layout-container overflow-hidden",ref:r},[m(B,{onResize:I}),m("div",{class:"absolute-full",style:w.value},[m("div",{class:"scroll",style:d.value},[u])])]):u}}}),ve={};function he(e,p){const n=ee("router-view");return Z(),Y(fe,{view:"lHh Lpr lFf"},{default:k(()=>[M(se,null,{default:k(()=>[M(n)]),_:1})]),_:1})}const be=ae(ve,[["render",he]]);export{be as default};

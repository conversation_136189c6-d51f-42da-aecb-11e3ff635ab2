import{c as ke,g as Ee,i as an,e as Vt,l as zl,b as jl,a as U,h as W,j as Xl,r as te,o as gt,w as le,y as Ga,z as Ql,d as Be,A as Va,B as Rn,C as Ne,k as Se,D as ps,E as Ka,F as et,T as Ji,G as yi,P as Zl,H as ur,I as Jl,f as Qe,J as cr,q as Ha,K as eu,L as tt,M as tu,N as dr,O as iu,n as su,Q as nu,R as ri,S as qa,U as ru,V as ft,W as Kt,X as au,s as Ki,t as At,u as Xe,x as Me,Y as ci,Z as on,_ as Wa,$ as Ya,a0 as ou,a1 as lu,a2 as bi,a3 as Rs,v as uu,a4 as fr,a5 as cu}from"./index-Dj-TVJr_.js";import{h as Fe,b as du,c as fu,d as hu,e as za,a as ja}from"./dom-DgPyGaJm.js";import{Q as Rt,a as gu,u as Xa,b as Qa,R as mu,c as pu,d as vu,v as yu,g as Hi,e as Za,f as Su,h as Tu,i as hr}from"./QBtn-B1hLEsCD.js";import{c as xu,s as Eu,g as Au,b as bu,a as Lu,h as Ru,_ as Ja}from"./_plugin-vue_export-helper-LJ0ELJAz.js";const Iu=ke({name:"QPage",props:{padding:Boolean,styleFn:Function},setup(s,{slots:e}){const{proxy:{$q:t}}=Ee(),i=an(zl,Vt);if(i===Vt)return console.error("QPage needs to be a deep child of QLayout"),Vt;if(an(jl,Vt)===Vt)return console.error("QPage needs to be child of QPageContainer"),Vt;const r=U(()=>{const o=(i.header.space===!0?i.header.size:0)+(i.footer.space===!0?i.footer.size:0);if(typeof s.styleFn=="function"){const l=i.isContainer.value===!0?i.containerHeight.value:t.screen.height;return s.styleFn(o,l)}return{minHeight:i.isContainer.value===!0?i.containerHeight.value-o+"px":t.screen.height===0?o!==0?`calc(100vh - ${o}px)`:"100vh":t.screen.height-o+"px"}}),a=U(()=>`q-page${s.padding===!0?" q-layout-padding":""}`);return()=>W("main",{class:a.value,style:r.value},Fe(e.default))}});function Cu(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}var eo={exports:{}};(function(s,e){(function(t){var i=/^(?=((?:[a-zA-Z0-9+\-.]+:)?))\1(?=((?:\/\/[^\/?#]*)?))\2(?=((?:(?:[^?#\/]*\/)*[^;?#\/]*)?))\3((?:;[^?#]*)?)(\?[^#]*)?(#[^]*)?$/,n=/^(?=([^\/?#]*))\1([^]*)$/,r=/(?:\/|^)\.(?=\/)/g,a=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,o={buildAbsoluteURL:function(l,u,c){if(c=c||{},l=l.trim(),u=u.trim(),!u){if(!c.alwaysNormalize)return l;var d=o.parseURL(l);if(!d)throw new Error("Error trying to parse base URL.");return d.path=o.normalizePath(d.path),o.buildURLFromParts(d)}var f=o.parseURL(u);if(!f)throw new Error("Error trying to parse relative URL.");if(f.scheme)return c.alwaysNormalize?(f.path=o.normalizePath(f.path),o.buildURLFromParts(f)):u;var h=o.parseURL(l);if(!h)throw new Error("Error trying to parse base URL.");if(!h.netLoc&&h.path&&h.path[0]!=="/"){var g=n.exec(h.path);h.netLoc=g[1],h.path=g[2]}h.netLoc&&!h.path&&(h.path="/");var p={scheme:h.scheme,netLoc:f.netLoc,path:null,params:f.params,query:f.query,fragment:f.fragment};if(!f.netLoc&&(p.netLoc=h.netLoc,f.path[0]!=="/"))if(!f.path)p.path=h.path,f.params||(p.params=h.params,f.query||(p.query=h.query));else{var m=h.path,y=m.substring(0,m.lastIndexOf("/")+1)+f.path;p.path=o.normalizePath(y)}return p.path===null&&(p.path=c.alwaysNormalize?o.normalizePath(f.path):f.path),o.buildURLFromParts(p)},parseURL:function(l){var u=i.exec(l);return u?{scheme:u[1]||"",netLoc:u[2]||"",path:u[3]||"",params:u[4]||"",query:u[5]||"",fragment:u[6]||""}:null},normalizePath:function(l){for(l=l.split("").reverse().join("").replace(r,"");l.length!==(l=l.replace(a,"")).length;);return l.split("").reverse().join("")},buildURLFromParts:function(l){return l.scheme+l.netLoc+l.path+l.params+l.query+l.fragment}};s.exports=o})()})(eo);var In=eo.exports;function gr(s,e){var t=Object.keys(s);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(s);e&&(i=i.filter(function(n){return Object.getOwnPropertyDescriptor(s,n).enumerable})),t.push.apply(t,i)}return t}function Re(s){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?gr(Object(t),!0).forEach(function(i){ku(s,i,t[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(t)):gr(Object(t)).forEach(function(i){Object.defineProperty(s,i,Object.getOwnPropertyDescriptor(t,i))})}return s}function Du(s,e){if(typeof s!="object"||!s)return s;var t=s[Symbol.toPrimitive];if(t!==void 0){var i=t.call(s,e);if(typeof i!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(s)}function wu(s){var e=Du(s,"string");return typeof e=="symbol"?e:String(e)}function ku(s,e,t){return e=wu(e),e in s?Object.defineProperty(s,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):s[e]=t,s}function xe(){return xe=Object.assign?Object.assign.bind():function(s){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(s[i]=t[i])}return s},xe.apply(this,arguments)}const J=Number.isFinite||function(s){return typeof s=="number"&&isFinite(s)},_u=Number.isSafeInteger||function(s){return typeof s=="number"&&Math.abs(s)<=Pu},Pu=Number.MAX_SAFE_INTEGER||9007199254740991;let v=function(s){return s.MEDIA_ATTACHING="hlsMediaAttaching",s.MEDIA_ATTACHED="hlsMediaAttached",s.MEDIA_DETACHING="hlsMediaDetaching",s.MEDIA_DETACHED="hlsMediaDetached",s.BUFFER_RESET="hlsBufferReset",s.BUFFER_CODECS="hlsBufferCodecs",s.BUFFER_CREATED="hlsBufferCreated",s.BUFFER_APPENDING="hlsBufferAppending",s.BUFFER_APPENDED="hlsBufferAppended",s.BUFFER_EOS="hlsBufferEos",s.BUFFER_FLUSHING="hlsBufferFlushing",s.BUFFER_FLUSHED="hlsBufferFlushed",s.MANIFEST_LOADING="hlsManifestLoading",s.MANIFEST_LOADED="hlsManifestLoaded",s.MANIFEST_PARSED="hlsManifestParsed",s.LEVEL_SWITCHING="hlsLevelSwitching",s.LEVEL_SWITCHED="hlsLevelSwitched",s.LEVEL_LOADING="hlsLevelLoading",s.LEVEL_LOADED="hlsLevelLoaded",s.LEVEL_UPDATED="hlsLevelUpdated",s.LEVEL_PTS_UPDATED="hlsLevelPtsUpdated",s.LEVELS_UPDATED="hlsLevelsUpdated",s.AUDIO_TRACKS_UPDATED="hlsAudioTracksUpdated",s.AUDIO_TRACK_SWITCHING="hlsAudioTrackSwitching",s.AUDIO_TRACK_SWITCHED="hlsAudioTrackSwitched",s.AUDIO_TRACK_LOADING="hlsAudioTrackLoading",s.AUDIO_TRACK_LOADED="hlsAudioTrackLoaded",s.SUBTITLE_TRACKS_UPDATED="hlsSubtitleTracksUpdated",s.SUBTITLE_TRACKS_CLEARED="hlsSubtitleTracksCleared",s.SUBTITLE_TRACK_SWITCH="hlsSubtitleTrackSwitch",s.SUBTITLE_TRACK_LOADING="hlsSubtitleTrackLoading",s.SUBTITLE_TRACK_LOADED="hlsSubtitleTrackLoaded",s.SUBTITLE_FRAG_PROCESSED="hlsSubtitleFragProcessed",s.CUES_PARSED="hlsCuesParsed",s.NON_NATIVE_TEXT_TRACKS_FOUND="hlsNonNativeTextTracksFound",s.INIT_PTS_FOUND="hlsInitPtsFound",s.FRAG_LOADING="hlsFragLoading",s.FRAG_LOAD_EMERGENCY_ABORTED="hlsFragLoadEmergencyAborted",s.FRAG_LOADED="hlsFragLoaded",s.FRAG_DECRYPTED="hlsFragDecrypted",s.FRAG_PARSING_INIT_SEGMENT="hlsFragParsingInitSegment",s.FRAG_PARSING_USERDATA="hlsFragParsingUserdata",s.FRAG_PARSING_METADATA="hlsFragParsingMetadata",s.FRAG_PARSED="hlsFragParsed",s.FRAG_BUFFERED="hlsFragBuffered",s.FRAG_CHANGED="hlsFragChanged",s.FPS_DROP="hlsFpsDrop",s.FPS_DROP_LEVEL_CAPPING="hlsFpsDropLevelCapping",s.MAX_AUTO_LEVEL_UPDATED="hlsMaxAutoLevelUpdated",s.ERROR="hlsError",s.DESTROYING="hlsDestroying",s.KEY_LOADING="hlsKeyLoading",s.KEY_LOADED="hlsKeyLoaded",s.LIVE_BACK_BUFFER_REACHED="hlsLiveBackBufferReached",s.BACK_BUFFER_REACHED="hlsBackBufferReached",s.STEERING_MANIFEST_LOADED="hlsSteeringManifestLoaded",s}({}),re=function(s){return s.NETWORK_ERROR="networkError",s.MEDIA_ERROR="mediaError",s.KEY_SYSTEM_ERROR="keySystemError",s.MUX_ERROR="muxError",s.OTHER_ERROR="otherError",s}({}),O=function(s){return s.KEY_SYSTEM_NO_KEYS="keySystemNoKeys",s.KEY_SYSTEM_NO_ACCESS="keySystemNoAccess",s.KEY_SYSTEM_NO_SESSION="keySystemNoSession",s.KEY_SYSTEM_NO_CONFIGURED_LICENSE="keySystemNoConfiguredLicense",s.KEY_SYSTEM_LICENSE_REQUEST_FAILED="keySystemLicenseRequestFailed",s.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED="keySystemServerCertificateRequestFailed",s.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED="keySystemServerCertificateUpdateFailed",s.KEY_SYSTEM_SESSION_UPDATE_FAILED="keySystemSessionUpdateFailed",s.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED="keySystemStatusOutputRestricted",s.KEY_SYSTEM_STATUS_INTERNAL_ERROR="keySystemStatusInternalError",s.MANIFEST_LOAD_ERROR="manifestLoadError",s.MANIFEST_LOAD_TIMEOUT="manifestLoadTimeOut",s.MANIFEST_PARSING_ERROR="manifestParsingError",s.MANIFEST_INCOMPATIBLE_CODECS_ERROR="manifestIncompatibleCodecsError",s.LEVEL_EMPTY_ERROR="levelEmptyError",s.LEVEL_LOAD_ERROR="levelLoadError",s.LEVEL_LOAD_TIMEOUT="levelLoadTimeOut",s.LEVEL_PARSING_ERROR="levelParsingError",s.LEVEL_SWITCH_ERROR="levelSwitchError",s.AUDIO_TRACK_LOAD_ERROR="audioTrackLoadError",s.AUDIO_TRACK_LOAD_TIMEOUT="audioTrackLoadTimeOut",s.SUBTITLE_LOAD_ERROR="subtitleTrackLoadError",s.SUBTITLE_TRACK_LOAD_TIMEOUT="subtitleTrackLoadTimeOut",s.FRAG_LOAD_ERROR="fragLoadError",s.FRAG_LOAD_TIMEOUT="fragLoadTimeOut",s.FRAG_DECRYPT_ERROR="fragDecryptError",s.FRAG_PARSING_ERROR="fragParsingError",s.FRAG_GAP="fragGap",s.REMUX_ALLOC_ERROR="remuxAllocError",s.KEY_LOAD_ERROR="keyLoadError",s.KEY_LOAD_TIMEOUT="keyLoadTimeOut",s.BUFFER_ADD_CODEC_ERROR="bufferAddCodecError",s.BUFFER_INCOMPATIBLE_CODECS_ERROR="bufferIncompatibleCodecsError",s.BUFFER_APPEND_ERROR="bufferAppendError",s.BUFFER_APPENDING_ERROR="bufferAppendingError",s.BUFFER_STALLED_ERROR="bufferStalledError",s.BUFFER_FULL_ERROR="bufferFullError",s.BUFFER_SEEK_OVER_HOLE="bufferSeekOverHole",s.BUFFER_NUDGE_ON_STALL="bufferNudgeOnStall",s.INTERNAL_EXCEPTION="internalException",s.INTERNAL_ABORTED="aborted",s.UNKNOWN="unknown",s}({});const xt=function(){},ln={trace:xt,debug:xt,log:xt,warn:xt,info:xt,error:xt};let di=ln;function Fu(s){const e=self.console[s];return e?e.bind(self.console,`[${s}] >`):xt}function Ou(s,...e){e.forEach(function(t){di[t]=s[t]?s[t].bind(s):Fu(t)})}function Mu(s,e){if(typeof console=="object"&&s===!0||typeof s=="object"){Ou(s,"debug","log","info","warn","error");try{di.log(`Debug logs enabled for "${e}" in hls.js version 1.5.20`)}catch{di=ln}}else di=ln}const _=di,Nu=/^(\d+)x(\d+)$/,mr=/(.+?)=(".*?"|.*?)(?:,|$)/g;class pe{constructor(e){typeof e=="string"&&(e=pe.parseAttrList(e)),xe(this,e)}get clientAttrs(){return Object.keys(this).filter(e=>e.substring(0,2)==="X-")}decimalInteger(e){const t=parseInt(this[e],10);return t>Number.MAX_SAFE_INTEGER?1/0:t}hexadecimalInteger(e){if(this[e]){let t=(this[e]||"0x").slice(2);t=(t.length&1?"0":"")+t;const i=new Uint8Array(t.length/2);for(let n=0;n<t.length/2;n++)i[n]=parseInt(t.slice(n*2,n*2+2),16);return i}else return null}hexadecimalIntegerAsNumber(e){const t=parseInt(this[e],16);return t>Number.MAX_SAFE_INTEGER?1/0:t}decimalFloatingPoint(e){return parseFloat(this[e])}optionalFloat(e,t){const i=this[e];return i?parseFloat(i):t}enumeratedString(e){return this[e]}bool(e){return this[e]==="YES"}decimalResolution(e){const t=Nu.exec(this[e]);if(t!==null)return{width:parseInt(t[1],10),height:parseInt(t[2],10)}}static parseAttrList(e){let t;const i={},n='"';for(mr.lastIndex=0;(t=mr.exec(e))!==null;){let r=t[2];r.indexOf(n)===0&&r.lastIndexOf(n)===r.length-1&&(r=r.slice(1,-1));const a=t[1].trim();i[a]=r}return i}}function Bu(s){return s!=="ID"&&s!=="CLASS"&&s!=="START-DATE"&&s!=="DURATION"&&s!=="END-DATE"&&s!=="END-ON-NEXT"}function Uu(s){return s==="SCTE35-OUT"||s==="SCTE35-IN"}class to{constructor(e,t){if(this.attr=void 0,this._startDate=void 0,this._endDate=void 0,this._badValueForSameId=void 0,t){const i=t.attr;for(const n in i)if(Object.prototype.hasOwnProperty.call(e,n)&&e[n]!==i[n]){_.warn(`DATERANGE tag attribute: "${n}" does not match for tags with ID: "${e.ID}"`),this._badValueForSameId=n;break}e=xe(new pe({}),i,e)}if(this.attr=e,this._startDate=new Date(e["START-DATE"]),"END-DATE"in this.attr){const i=new Date(this.attr["END-DATE"]);J(i.getTime())&&(this._endDate=i)}}get id(){return this.attr.ID}get class(){return this.attr.CLASS}get startDate(){return this._startDate}get endDate(){if(this._endDate)return this._endDate;const e=this.duration;return e!==null?new Date(this._startDate.getTime()+e*1e3):null}get duration(){if("DURATION"in this.attr){const e=this.attr.decimalFloatingPoint("DURATION");if(J(e))return e}else if(this._endDate)return(this._endDate.getTime()-this._startDate.getTime())/1e3;return null}get plannedDuration(){return"PLANNED-DURATION"in this.attr?this.attr.decimalFloatingPoint("PLANNED-DURATION"):null}get endOnNext(){return this.attr.bool("END-ON-NEXT")}get isValid(){return!!this.id&&!this._badValueForSameId&&J(this.startDate.getTime())&&(this.duration===null||this.duration>=0)&&(!this.endOnNext||!!this.class)}}class vs{constructor(){this.aborted=!1,this.loaded=0,this.retry=0,this.total=0,this.chunkCount=0,this.bwEstimate=0,this.loading={start:0,first:0,end:0},this.parsing={start:0,end:0},this.buffering={start:0,first:0,end:0}}}var fe={AUDIO:"audio",VIDEO:"video",AUDIOVIDEO:"audiovideo"};class io{constructor(e){this._byteRange=null,this._url=null,this.baseurl=void 0,this.relurl=void 0,this.elementaryStreams={[fe.AUDIO]:null,[fe.VIDEO]:null,[fe.AUDIOVIDEO]:null},this.baseurl=e}setByteRange(e,t){const i=e.split("@",2);let n;i.length===1?n=t?.byteRangeEndOffset||0:n=parseInt(i[1]),this._byteRange=[n,parseInt(i[0])+n]}get byteRange(){return this._byteRange?this._byteRange:[]}get byteRangeStartOffset(){return this.byteRange[0]}get byteRangeEndOffset(){return this.byteRange[1]}get url(){return!this._url&&this.baseurl&&this.relurl&&(this._url=In.buildAbsoluteURL(this.baseurl,this.relurl,{alwaysNormalize:!0})),this._url||""}set url(e){this._url=e}}class Is extends io{constructor(e,t){super(t),this._decryptdata=null,this.rawProgramDateTime=null,this.programDateTime=null,this.tagList=[],this.duration=0,this.sn=0,this.levelkeys=void 0,this.type=void 0,this.loader=null,this.keyLoader=null,this.level=-1,this.cc=0,this.startPTS=void 0,this.endPTS=void 0,this.startDTS=void 0,this.endDTS=void 0,this.start=0,this.deltaPTS=void 0,this.maxStartPTS=void 0,this.minEndPTS=void 0,this.stats=new vs,this.data=void 0,this.bitrateTest=!1,this.title=null,this.initSegment=null,this.endList=void 0,this.gap=void 0,this.urlId=0,this.type=e}get decryptdata(){const{levelkeys:e}=this;if(!e&&!this._decryptdata)return null;if(!this._decryptdata&&this.levelkeys&&!this.levelkeys.NONE){const t=this.levelkeys.identity;if(t)this._decryptdata=t.getDecryptData(this.sn);else{const i=Object.keys(this.levelkeys);if(i.length===1)return this._decryptdata=this.levelkeys[i[0]].getDecryptData(this.sn)}}return this._decryptdata}get end(){return this.start+this.duration}get endProgramDateTime(){if(this.programDateTime===null||!J(this.programDateTime))return null;const e=J(this.duration)?this.duration:0;return this.programDateTime+e*1e3}get encrypted(){var e;if((e=this._decryptdata)!=null&&e.encrypted)return!0;if(this.levelkeys){const t=Object.keys(this.levelkeys),i=t.length;if(i>1||i===1&&this.levelkeys[t[0]].encrypted)return!0}return!1}setKeyFormat(e){if(this.levelkeys){const t=this.levelkeys[e];t&&!this._decryptdata&&(this._decryptdata=t.getDecryptData(this.sn))}}abortRequests(){var e,t;(e=this.loader)==null||e.abort(),(t=this.keyLoader)==null||t.abort()}setElementaryStreamInfo(e,t,i,n,r,a=!1){const{elementaryStreams:o}=this,l=o[e];if(!l){o[e]={startPTS:t,endPTS:i,startDTS:n,endDTS:r,partial:a};return}l.startPTS=Math.min(l.startPTS,t),l.endPTS=Math.max(l.endPTS,i),l.startDTS=Math.min(l.startDTS,n),l.endDTS=Math.max(l.endDTS,r)}clearElementaryStreamInfo(){const{elementaryStreams:e}=this;e[fe.AUDIO]=null,e[fe.VIDEO]=null,e[fe.AUDIOVIDEO]=null}}class $u extends io{constructor(e,t,i,n,r){super(i),this.fragOffset=0,this.duration=0,this.gap=!1,this.independent=!1,this.relurl=void 0,this.fragment=void 0,this.index=void 0,this.stats=new vs,this.duration=e.decimalFloatingPoint("DURATION"),this.gap=e.bool("GAP"),this.independent=e.bool("INDEPENDENT"),this.relurl=e.enumeratedString("URI"),this.fragment=t,this.index=n;const a=e.enumeratedString("BYTERANGE");a&&this.setByteRange(a,r),r&&(this.fragOffset=r.fragOffset+r.duration)}get start(){return this.fragment.start+this.fragOffset}get end(){return this.start+this.duration}get loaded(){const{elementaryStreams:e}=this;return!!(e.audio||e.video||e.audiovideo)}}const Gu=10;class Vu{constructor(e){this.PTSKnown=!1,this.alignedSliding=!1,this.averagetargetduration=void 0,this.endCC=0,this.endSN=0,this.fragments=void 0,this.fragmentHint=void 0,this.partList=null,this.dateRanges=void 0,this.live=!0,this.ageHeader=0,this.advancedDateTime=void 0,this.updated=!0,this.advanced=!0,this.availabilityDelay=void 0,this.misses=0,this.startCC=0,this.startSN=0,this.startTimeOffset=null,this.targetduration=0,this.totalduration=0,this.type=null,this.url=void 0,this.m3u8="",this.version=null,this.canBlockReload=!1,this.canSkipUntil=0,this.canSkipDateRanges=!1,this.skippedSegments=0,this.recentlyRemovedDateranges=void 0,this.partHoldBack=0,this.holdBack=0,this.partTarget=0,this.preloadHint=void 0,this.renditionReports=void 0,this.tuneInGoal=0,this.deltaUpdateFailed=void 0,this.driftStartTime=0,this.driftEndTime=0,this.driftStart=0,this.driftEnd=0,this.encryptedFragments=void 0,this.playlistParsingError=null,this.variableList=null,this.hasVariableRefs=!1,this.fragments=[],this.encryptedFragments=[],this.dateRanges={},this.url=e}reloaded(e){if(!e){this.advanced=!0,this.updated=!0;return}const t=this.lastPartSn-e.lastPartSn,i=this.lastPartIndex-e.lastPartIndex;this.updated=this.endSN!==e.endSN||!!i||!!t||!this.live,this.advanced=this.endSN>e.endSN||t>0||t===0&&i>0,this.updated||this.advanced?this.misses=Math.floor(e.misses*.6):this.misses=e.misses+1,this.availabilityDelay=e.availabilityDelay}get hasProgramDateTime(){return this.fragments.length?J(this.fragments[this.fragments.length-1].programDateTime):!1}get levelTargetDuration(){return this.averagetargetduration||this.targetduration||Gu}get drift(){const e=this.driftEndTime-this.driftStartTime;return e>0?(this.driftEnd-this.driftStart)*1e3/e:1}get edge(){return this.partEnd||this.fragmentEnd}get partEnd(){var e;return(e=this.partList)!=null&&e.length?this.partList[this.partList.length-1].end:this.fragmentEnd}get fragmentEnd(){var e;return(e=this.fragments)!=null&&e.length?this.fragments[this.fragments.length-1].end:0}get age(){return this.advancedDateTime?Math.max(Date.now()-this.advancedDateTime,0)/1e3:0}get lastPartIndex(){var e;return(e=this.partList)!=null&&e.length?this.partList[this.partList.length-1].index:-1}get lastPartSn(){var e;return(e=this.partList)!=null&&e.length?this.partList[this.partList.length-1].fragment.sn:this.endSN}}function Cn(s){return Uint8Array.from(atob(s),e=>e.charCodeAt(0))}function Ku(s){const e=un(s).subarray(0,16),t=new Uint8Array(16);return t.set(e,16-e.length),t}function Hu(s){const e=function(i,n,r){const a=i[n];i[n]=i[r],i[r]=a};e(s,0,3),e(s,1,2),e(s,4,5),e(s,6,7)}function qu(s){const e=s.split(":");let t=null;if(e[0]==="data"&&e.length===2){const i=e[1].split(";"),n=i[i.length-1].split(",");if(n.length===2){const r=n[0]==="base64",a=n[1];r?(i.splice(-1,1),t=Cn(a)):t=Ku(a)}}return t}function un(s){return Uint8Array.from(unescape(encodeURIComponent(s)),e=>e.charCodeAt(0))}const Zt=typeof self<"u"?self:void 0;var de={CLEARKEY:"org.w3.clearkey",FAIRPLAY:"com.apple.fps",PLAYREADY:"com.microsoft.playready",WIDEVINE:"com.widevine.alpha"},Pe={CLEARKEY:"org.w3.clearkey",FAIRPLAY:"com.apple.streamingkeydelivery",PLAYREADY:"com.microsoft.playready",WIDEVINE:"urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"};function Cs(s){switch(s){case Pe.FAIRPLAY:return de.FAIRPLAY;case Pe.PLAYREADY:return de.PLAYREADY;case Pe.WIDEVINE:return de.WIDEVINE;case Pe.CLEARKEY:return de.CLEARKEY}}var Li={CENC:"1077efecc0b24d02ace33c1e52e2fb4b",CLEARKEY:"e2719d58a985b3c9781ab030af78d30e",PLAYREADY:"9a04f07998404286ab92e65be0885f95",WIDEVINE:"edef8ba979d64acea3c827dcd51d21ed"};function Ds(s){if(s===Li.WIDEVINE)return de.WIDEVINE;if(s===Li.PLAYREADY)return de.PLAYREADY;if(s===Li.CENC||s===Li.CLEARKEY)return de.CLEARKEY}function ws(s){switch(s){case de.FAIRPLAY:return Pe.FAIRPLAY;case de.PLAYREADY:return Pe.PLAYREADY;case de.WIDEVINE:return Pe.WIDEVINE;case de.CLEARKEY:return Pe.CLEARKEY}}function Ri(s){const{drmSystems:e,widevineLicenseUrl:t}=s,i=e?[de.FAIRPLAY,de.WIDEVINE,de.PLAYREADY,de.CLEARKEY].filter(n=>!!e[n]):[];return!i[de.WIDEVINE]&&t&&i.push(de.WIDEVINE),i}const so=function(s){return Zt!=null&&(s=Zt.navigator)!=null&&s.requestMediaKeySystemAccess?self.navigator.requestMediaKeySystemAccess.bind(self.navigator):null}();function Wu(s,e,t,i){let n;switch(s){case de.FAIRPLAY:n=["cenc","sinf"];break;case de.WIDEVINE:case de.PLAYREADY:n=["cenc"];break;case de.CLEARKEY:n=["cenc","keyids"];break;default:throw new Error(`Unknown key-system: ${s}`)}return Yu(n,e,t,i)}function Yu(s,e,t,i){return[{initDataTypes:s,persistentState:i.persistentState||"optional",distinctiveIdentifier:i.distinctiveIdentifier||"optional",sessionTypes:i.sessionTypes||[i.sessionType||"temporary"],audioCapabilities:e.map(r=>({contentType:`audio/mp4; codecs="${r}"`,robustness:i.audioRobustness||"",encryptionScheme:i.audioEncryptionScheme||null})),videoCapabilities:t.map(r=>({contentType:`video/mp4; codecs="${r}"`,robustness:i.videoRobustness||"",encryptionScheme:i.videoEncryptionScheme||null}))}]}function no(s){const e=new Uint16Array(s.buffer,s.byteOffset,s.byteLength/2),t=String.fromCharCode.apply(null,Array.from(e)),i=t.substring(t.indexOf("<"),t.length),a=new DOMParser().parseFromString(i,"text/xml").getElementsByTagName("KID")[0];if(a){const o=a.childNodes[0]?a.childNodes[0].nodeValue:a.getAttribute("VALUE");if(o){const l=Cn(o).subarray(0,16);return Hu(l),l}}return null}function It(s,e,t){return Uint8Array.prototype.slice?s.slice(e,t):new Uint8Array(Array.prototype.slice.call(s,e,t))}const Dn=(s,e)=>e+10<=s.length&&s[e]===73&&s[e+1]===68&&s[e+2]===51&&s[e+3]<255&&s[e+4]<255&&s[e+6]<128&&s[e+7]<128&&s[e+8]<128&&s[e+9]<128,ro=(s,e)=>e+10<=s.length&&s[e]===51&&s[e+1]===68&&s[e+2]===73&&s[e+3]<255&&s[e+4]<255&&s[e+6]<128&&s[e+7]<128&&s[e+8]<128&&s[e+9]<128,fi=(s,e)=>{const t=e;let i=0;for(;Dn(s,e);){i+=10;const n=ys(s,e+6);i+=n,ro(s,e+10)&&(i+=10),e+=i}if(i>0)return s.subarray(t,t+i)},ys=(s,e)=>{let t=0;return t=(s[e]&127)<<21,t|=(s[e+1]&127)<<14,t|=(s[e+2]&127)<<7,t|=s[e+3]&127,t},zu=(s,e)=>Dn(s,e)&&ys(s,e+6)+10<=s.length-e,wn=s=>{const e=oo(s);for(let t=0;t<e.length;t++){const i=e[t];if(ao(i))return ec(i)}},ao=s=>s&&s.key==="PRIV"&&s.info==="com.apple.streaming.transportStreamTimestamp",ju=s=>{const e=String.fromCharCode(s[0],s[1],s[2],s[3]),t=ys(s,4),i=10;return{type:e,size:t,data:s.subarray(i,i+t)}},oo=s=>{let e=0;const t=[];for(;Dn(s,e);){const i=ys(s,e+6);e+=10;const n=e+i;for(;e+8<n;){const r=ju(s.subarray(e)),a=Xu(r);a&&t.push(a),e+=r.size+10}ro(s,e)&&(e+=10)}return t},Xu=s=>s.type==="PRIV"?Qu(s):s.type[0]==="W"?Ju(s):Zu(s),Qu=s=>{if(s.size<2)return;const e=nt(s.data,!0),t=new Uint8Array(s.data.subarray(e.length+1));return{key:s.type,info:e,data:t.buffer}},Zu=s=>{if(s.size<2)return;if(s.type==="TXXX"){let t=1;const i=nt(s.data.subarray(t),!0);t+=i.length+1;const n=nt(s.data.subarray(t));return{key:s.type,info:i,data:n}}const e=nt(s.data.subarray(1));return{key:s.type,data:e}},Ju=s=>{if(s.type==="WXXX"){if(s.size<2)return;let t=1;const i=nt(s.data.subarray(t),!0);t+=i.length+1;const n=nt(s.data.subarray(t));return{key:s.type,info:i,data:n}}const e=nt(s.data);return{key:s.type,data:e}},ec=s=>{if(s.data.byteLength===8){const e=new Uint8Array(s.data),t=e[3]&1;let i=(e[4]<<23)+(e[5]<<15)+(e[6]<<7)+e[7];return i/=45,t&&(i+=4772185884e-2),Math.round(i)}},nt=(s,e=!1)=>{const t=tc();if(t){const u=t.decode(s);if(e){const c=u.indexOf("\0");return c!==-1?u.substring(0,c):u}return u.replace(/\0/g,"")}const i=s.length;let n,r,a,o="",l=0;for(;l<i;){if(n=s[l++],n===0&&e)return o;if(n===0||n===3)continue;switch(n>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:o+=String.fromCharCode(n);break;case 12:case 13:r=s[l++],o+=String.fromCharCode((n&31)<<6|r&63);break;case 14:r=s[l++],a=s[l++],o+=String.fromCharCode((n&15)<<12|(r&63)<<6|(a&63)<<0);break}}return o};let ks;function tc(){if(!navigator.userAgent.includes("PlayStation 4"))return!ks&&typeof self.TextDecoder<"u"&&(ks=new self.TextDecoder("utf-8")),ks}const Ze={hexDump:function(s){let e="";for(let t=0;t<s.length;t++){let i=s[t].toString(16);i.length<2&&(i="0"+i),e+=i}return e}},es=Math.pow(2,32)-1,ic=[].push,lo={video:1,audio:2,id3:3,text:4};function Ae(s){return String.fromCharCode.apply(null,s)}function uo(s,e){const t=s[e]<<8|s[e+1];return t<0?65536+t:t}function se(s,e){const t=co(s,e);return t<0?4294967296+t:t}function pr(s,e){let t=se(s,e);return t*=Math.pow(2,32),t+=se(s,e+4),t}function co(s,e){return s[e]<<24|s[e+1]<<16|s[e+2]<<8|s[e+3]}function _s(s,e,t){s[e]=t>>24,s[e+1]=t>>16&255,s[e+2]=t>>8&255,s[e+3]=t&255}function sc(s){const e=s.byteLength;for(let t=0;t<e;){const i=se(s,t);if(i>8&&s[t+4]===109&&s[t+5]===111&&s[t+6]===111&&s[t+7]===102)return!0;t=i>1?t+i:e}return!1}function ae(s,e){const t=[];if(!e.length)return t;const i=s.byteLength;for(let n=0;n<i;){const r=se(s,n),a=Ae(s.subarray(n+4,n+8)),o=r>1?n+r:i;if(a===e[0])if(e.length===1)t.push(s.subarray(n+8,o));else{const l=ae(s.subarray(n+8,o),e.slice(1));l.length&&ic.apply(t,l)}n=o}return t}function nc(s){const e=[],t=s[0];let i=8;const n=se(s,i);i+=4;let r=0,a=0;t===0?(r=se(s,i),a=se(s,i+4),i+=8):(r=pr(s,i),a=pr(s,i+8),i+=16),i+=2;let o=s.length+a;const l=uo(s,i);i+=2;for(let u=0;u<l;u++){let c=i;const d=se(s,c);c+=4;const f=d&2147483647;if((d&2147483648)>>>31===1)return _.warn("SIDX has hierarchical references (not supported)"),null;const g=se(s,c);c+=4,e.push({referenceSize:f,subsegmentDuration:g,info:{duration:g/n,start:o,end:o+f-1}}),o+=f,c+=4,i=c}return{earliestPresentationTime:r,timescale:n,version:t,referencesCount:l,references:e}}function fo(s){const e=[],t=ae(s,["moov","trak"]);for(let n=0;n<t.length;n++){const r=t[n],a=ae(r,["tkhd"])[0];if(a){let o=a[0];const l=se(a,o===0?12:20),u=ae(r,["mdia","mdhd"])[0];if(u){o=u[0];const c=se(u,o===0?12:20),d=ae(r,["mdia","hdlr"])[0];if(d){const f=Ae(d.subarray(8,12)),h={soun:fe.AUDIO,vide:fe.VIDEO}[f];if(h){const g=ae(r,["mdia","minf","stbl","stsd"])[0],p=rc(g);e[l]={timescale:c,type:h},e[h]=Re({timescale:c,id:l},p)}}}}}return ae(s,["moov","mvex","trex"]).forEach(n=>{const r=se(n,4),a=e[r];a&&(a.default={duration:se(n,12),flags:se(n,20)})}),e}function rc(s){const e=s.subarray(8),t=e.subarray(86),i=Ae(e.subarray(4,8));let n=i;const r=i==="enca"||i==="encv";if(r){const o=ae(e,[i])[0].subarray(i==="enca"?28:78);ae(o,["sinf"]).forEach(u=>{const c=ae(u,["schm"])[0];if(c){const d=Ae(c.subarray(4,8));if(d==="cbcs"||d==="cenc"){const f=ae(u,["frma"])[0];f&&(n=Ae(f))}}})}switch(n){case"avc1":case"avc2":case"avc3":case"avc4":{const a=ae(t,["avcC"])[0];n+="."+Ii(a[1])+Ii(a[2])+Ii(a[3]);break}case"mp4a":{const a=ae(e,[i])[0],o=ae(a.subarray(28),["esds"])[0];if(o&&o.length>12){let l=4;if(o[l++]!==3)break;l=Ps(o,l),l+=2;const u=o[l++];if(u&128&&(l+=2),u&64&&(l+=o[l++]),o[l++]!==4)break;l=Ps(o,l);const c=o[l++];if(c===64)n+="."+Ii(c);else break;if(l+=12,o[l++]!==5)break;l=Ps(o,l);const d=o[l++];let f=(d&248)>>3;f===31&&(f+=1+((d&7)<<3)+((o[l]&224)>>5)),n+="."+f}break}case"hvc1":case"hev1":{const a=ae(t,["hvcC"])[0],o=a[1],l=["","A","B","C"][o>>6],u=o&31,c=se(a,2),d=(o&32)>>5?"H":"L",f=a[12],h=a.subarray(6,12);n+="."+l+u,n+="."+c.toString(16).toUpperCase(),n+="."+d+f;let g="";for(let p=h.length;p--;){const m=h[p];(m||g)&&(g="."+m.toString(16).toUpperCase()+g)}n+=g;break}case"dvh1":case"dvhe":{const a=ae(t,["dvcC"])[0],o=a[2]>>1&127,l=a[2]<<5&32|a[3]>>3&31;n+="."+je(o)+"."+je(l);break}case"vp09":{const a=ae(t,["vpcC"])[0],o=a[4],l=a[5],u=a[6]>>4&15;n+="."+je(o)+"."+je(l)+"."+je(u);break}case"av01":{const a=ae(t,["av1C"])[0],o=a[1]>>>5,l=a[1]&31,u=a[2]>>>7?"H":"M",c=(a[2]&64)>>6,d=(a[2]&32)>>5,f=o===2&&c?d?12:10:c?10:8,h=(a[2]&16)>>4,g=(a[2]&8)>>3,p=(a[2]&4)>>2,m=a[2]&3;n+="."+o+"."+je(l)+u+"."+je(f)+"."+h+"."+g+p+m+"."+je(1)+"."+je(1)+"."+je(1)+"."+0;break}}return{codec:n,encrypted:r}}function Ps(s,e){const t=e+5;for(;s[e++]&128&&e<t;);return e}function Ii(s){return("0"+s.toString(16).toUpperCase()).slice(-2)}function je(s){return(s<10?"0":"")+s}function ac(s,e){if(!s||!e)return s;const t=e.keyId;return t&&e.isCommonEncryption&&ae(s,["moov","trak"]).forEach(n=>{const a=ae(n,["mdia","minf","stbl","stsd"])[0].subarray(8);let o=ae(a,["enca"]);const l=o.length>0;l||(o=ae(a,["encv"])),o.forEach(u=>{const c=l?u.subarray(28):u.subarray(78);ae(c,["sinf"]).forEach(f=>{const h=ho(f);if(h){const g=h.subarray(8,24);g.some(p=>p!==0)||(_.log(`[eme] Patching keyId in 'enc${l?"a":"v"}>sinf>>tenc' box: ${Ze.hexDump(g)} -> ${Ze.hexDump(t)}`),h.set(t,8))}})})}),s}function ho(s){const e=ae(s,["schm"])[0];if(e){const t=Ae(e.subarray(4,8));if(t==="cbcs"||t==="cenc")return ae(s,["schi","tenc"])[0]}return null}function oc(s,e){return ae(e,["moof","traf"]).reduce((t,i)=>{const n=ae(i,["tfdt"])[0],r=n[0],a=ae(i,["tfhd"]).reduce((o,l)=>{const u=se(l,4),c=s[u];if(c){let d=se(n,4);if(r===1){if(d===es)return _.warn("[mp4-demuxer]: Ignoring assumed invalid signed 64-bit track fragment decode time"),o;d*=es+1,d+=se(n,8)}const f=c.timescale||9e4,h=d/f;if(J(h)&&(o===null||h<o))return h}return o},null);return a!==null&&J(a)&&(t===null||a<t)?a:t},null)}function lc(s,e){let t=0,i=0,n=0;const r=ae(s,["moof","traf"]);for(let a=0;a<r.length;a++){const o=r[a],l=ae(o,["tfhd"])[0],u=se(l,4),c=e[u];if(!c)continue;const d=c.default,f=se(l,0)|d?.flags;let h=d?.duration;f&8&&(f&2?h=se(l,12):h=se(l,8));const g=c.timescale||9e4,p=ae(o,["trun"]);for(let m=0;m<p.length;m++){if(t=uc(p[m]),!t&&h){const y=se(p[m],4);t=h*y}c.type===fe.VIDEO?i+=t/g:c.type===fe.AUDIO&&(n+=t/g)}}if(i===0&&n===0){let a=1/0,o=0,l=0;const u=ae(s,["sidx"]);for(let c=0;c<u.length;c++){const d=nc(u[c]);if(d!=null&&d.references){a=Math.min(a,d.earliestPresentationTime/d.timescale);const f=d.references.reduce((h,g)=>h+g.info.duration||0,0);o=Math.max(o,f+d.earliestPresentationTime/d.timescale),l=o-a}}if(l&&J(l))return l}return i||n}function uc(s){const e=se(s,0);let t=8;e&1&&(t+=4),e&4&&(t+=4);let i=0;const n=se(s,4);for(let r=0;r<n;r++){if(e&256){const a=se(s,t);i+=a,t+=4}e&512&&(t+=4),e&1024&&(t+=4),e&2048&&(t+=4)}return i}function cc(s,e,t){ae(e,["moof","traf"]).forEach(i=>{ae(i,["tfhd"]).forEach(n=>{const r=se(n,4),a=s[r];if(!a)return;const o=a.timescale||9e4;ae(i,["tfdt"]).forEach(l=>{const u=l[0],c=t*o;if(c){let d=se(l,4);if(u===0)d-=c,d=Math.max(d,0),_s(l,4,d);else{d*=Math.pow(2,32),d+=se(l,8),d-=c,d=Math.max(d,0);const f=Math.floor(d/(es+1)),h=Math.floor(d%(es+1));_s(l,4,f),_s(l,8,h)}}})})})}function dc(s){const e={valid:null,remainder:null},t=ae(s,["moof"]);if(t.length<2)return e.remainder=s,e;const i=t[t.length-1];return e.valid=It(s,0,i.byteOffset-8),e.remainder=It(s,i.byteOffset-8),e}function Ge(s,e){const t=new Uint8Array(s.length+e.length);return t.set(s),t.set(e,s.length),t}function vr(s,e){const t=[],i=e.samples,n=e.timescale,r=e.id;let a=!1;return ae(i,["moof"]).map(l=>{const u=l.byteOffset-8;ae(l,["traf"]).map(d=>{const f=ae(d,["tfdt"]).map(h=>{const g=h[0];let p=se(h,4);return g===1&&(p*=Math.pow(2,32),p+=se(h,8)),p/n})[0];return f!==void 0&&(s=f),ae(d,["tfhd"]).map(h=>{const g=se(h,4),p=se(h,0)&16777215,m=(p&1)!==0,y=(p&2)!==0,S=(p&8)!==0;let T=0;const I=(p&16)!==0;let E=0;const P=(p&32)!==0;let R=8;g===r&&(m&&(R+=8),y&&(R+=4),S&&(T=se(h,R),R+=4),I&&(E=se(h,R),R+=4),P&&(R+=4),e.type==="video"&&(a=fc(e.codec)),ae(d,["trun"]).map(w=>{const b=w[0],x=se(w,0)&16777215,D=(x&1)!==0;let C=0;const M=(x&4)!==0,N=(x&256)!==0;let Y=0;const K=(x&512)!==0;let z=0;const V=(x&1024)!==0,B=(x&2048)!==0;let $=0;const k=se(w,4);let L=8;D&&(C=se(w,L),L+=4),M&&(L+=4);let q=C+u;for(let ee=0;ee<k;ee++){if(N?(Y=se(w,L),L+=4):Y=T,K?(z=se(w,L),L+=4):z=E,V&&(L+=4),B&&(b===0?$=se(w,L):$=co(w,L),L+=4),e.type===fe.VIDEO){let ie=0;for(;ie<z;){const H=se(i,q);if(q+=4,hc(a,i[q])){const j=i.subarray(q,q+H);go(j,a?2:1,s+$/n,t)}q+=H,ie+=H+4}}s+=Y/n}}))})})}),t}function fc(s){if(!s)return!1;const e=s.indexOf("."),t=e<0?s:s.substring(0,e);return t==="hvc1"||t==="hev1"||t==="dvh1"||t==="dvhe"}function hc(s,e){if(s){const t=e>>1&63;return t===39||t===40}else return(e&31)===6}function go(s,e,t,i){const n=mo(s);let r=0;r+=e;let a=0,o=0,l=0;for(;r<n.length;){a=0;do{if(r>=n.length)break;l=n[r++],a+=l}while(l===255);o=0;do{if(r>=n.length)break;l=n[r++],o+=l}while(l===255);const u=n.length-r;let c=r;if(o<u)r+=o;else if(o>u){_.error(`Malformed SEI payload. ${o} is too small, only ${u} bytes left to parse.`);break}if(a===4){if(n[c++]===181){const f=uo(n,c);if(c+=2,f===49){const h=se(n,c);if(c+=4,h===1195456820){const g=n[c++];if(g===3){const p=n[c++],m=31&p,y=64&p,S=y?2+m*3:0,T=new Uint8Array(S);if(y){T[0]=p;for(let I=1;I<S;I++)T[I]=n[c++]}i.push({type:g,payloadType:a,pts:t,bytes:T})}}}}}else if(a===5&&o>16){const d=[];for(let g=0;g<16;g++){const p=n[c++].toString(16);d.push(p.length==1?"0"+p:p),(g===3||g===5||g===7||g===9)&&d.push("-")}const f=o-16,h=new Uint8Array(f);for(let g=0;g<f;g++)h[g]=n[c++];i.push({payloadType:a,pts:t,uuid:d.join(""),userData:nt(h),userDataBytes:h})}}}function mo(s){const e=s.byteLength,t=[];let i=1;for(;i<e-2;)s[i]===0&&s[i+1]===0&&s[i+2]===3?(t.push(i+2),i+=2):i++;if(t.length===0)return s;const n=e-t.length,r=new Uint8Array(n);let a=0;for(i=0;i<n;a++,i++)a===t[0]&&(a++,t.shift()),r[i]=s[a];return r}function gc(s){const e=s[0];let t="",i="",n=0,r=0,a=0,o=0,l=0,u=0;if(e===0){for(;Ae(s.subarray(u,u+1))!=="\0";)t+=Ae(s.subarray(u,u+1)),u+=1;for(t+=Ae(s.subarray(u,u+1)),u+=1;Ae(s.subarray(u,u+1))!=="\0";)i+=Ae(s.subarray(u,u+1)),u+=1;i+=Ae(s.subarray(u,u+1)),u+=1,n=se(s,12),r=se(s,16),o=se(s,20),l=se(s,24),u=28}else if(e===1){u+=4,n=se(s,u),u+=4;const d=se(s,u);u+=4;const f=se(s,u);for(u+=4,a=2**32*d+f,_u(a)||(a=Number.MAX_SAFE_INTEGER,_.warn("Presentation time exceeds safe integer limit and wrapped to max safe integer in parsing emsg box")),o=se(s,u),u+=4,l=se(s,u),u+=4;Ae(s.subarray(u,u+1))!=="\0";)t+=Ae(s.subarray(u,u+1)),u+=1;for(t+=Ae(s.subarray(u,u+1)),u+=1;Ae(s.subarray(u,u+1))!=="\0";)i+=Ae(s.subarray(u,u+1)),u+=1;i+=Ae(s.subarray(u,u+1)),u+=1}const c=s.subarray(u,s.byteLength);return{schemeIdUri:t,value:i,timeScale:n,presentationTime:a,presentationTimeDelta:r,eventDuration:o,id:l,payload:c}}function mc(s,...e){const t=e.length;let i=8,n=t;for(;n--;)i+=e[n].byteLength;const r=new Uint8Array(i);for(r[0]=i>>24&255,r[1]=i>>16&255,r[2]=i>>8&255,r[3]=i&255,r.set(s,4),n=0,i=8;n<t;n++)r.set(e[n],i),i+=e[n].byteLength;return r}function pc(s,e,t){if(s.byteLength!==16)throw new RangeError("Invalid system id");let i,n;i=0,n=new Uint8Array;let r;i>0?(r=new Uint8Array(4),e.length>0&&new DataView(r.buffer).setUint32(0,e.length,!1)):r=new Uint8Array;const a=new Uint8Array(4);return t&&t.byteLength>0&&new DataView(a.buffer).setUint32(0,t.byteLength,!1),mc([112,115,115,104],new Uint8Array([i,0,0,0]),s,r,n,a,t||new Uint8Array)}function vc(s){const e=[];if(s instanceof ArrayBuffer){const t=s.byteLength;let i=0;for(;i+32<t;){const n=new DataView(s,i),r=yc(n);e.push(r),i+=r.size}}return e}function yc(s){const e=s.getUint32(0),t=s.byteOffset,i=s.byteLength;if(i<e)return{offset:t,size:i};if(s.getUint32(4)!==1886614376)return{offset:t,size:e};const r=s.getUint32(8)>>>24;if(r!==0&&r!==1)return{offset:t,size:e};const a=s.buffer,o=Ze.hexDump(new Uint8Array(a,t+12,16)),l=s.getUint32(28);let u=null,c=null;if(r===0){if(e-32<l||l<22)return{offset:t,size:e};c=new Uint8Array(a,t+32,l)}else if(r===1){if(!l||i<t+32+l*16+16)return{offset:t,size:e};u=[];for(let d=0;d<l;d++)u.push(new Uint8Array(a,t+32+d*16,16))}return{version:r,systemId:o,kids:u,data:c,offset:t,size:e}}let Ci={};class hi{static clearKeyUriToKeyIdMap(){Ci={}}constructor(e,t,i,n=[1],r=null){this.uri=void 0,this.method=void 0,this.keyFormat=void 0,this.keyFormatVersions=void 0,this.encrypted=void 0,this.isCommonEncryption=void 0,this.iv=null,this.key=null,this.keyId=null,this.pssh=null,this.method=e,this.uri=t,this.keyFormat=i,this.keyFormatVersions=n,this.iv=r,this.encrypted=e?e!=="NONE":!1,this.isCommonEncryption=this.encrypted&&e!=="AES-128"}isSupported(){if(this.method){if(this.method==="AES-128"||this.method==="NONE")return!0;if(this.keyFormat==="identity")return this.method==="SAMPLE-AES";switch(this.keyFormat){case Pe.FAIRPLAY:case Pe.WIDEVINE:case Pe.PLAYREADY:case Pe.CLEARKEY:return["ISO-23001-7","SAMPLE-AES","SAMPLE-AES-CENC","SAMPLE-AES-CTR"].indexOf(this.method)!==-1}}return!1}getDecryptData(e){if(!this.encrypted||!this.uri)return null;if(this.method==="AES-128"&&this.uri&&!this.iv){typeof e!="number"&&(this.method==="AES-128"&&!this.iv&&_.warn(`missing IV for initialization segment with method="${this.method}" - compliance issue`),e=0);const i=Sc(e);return new hi(this.method,this.uri,"identity",this.keyFormatVersions,i)}const t=qu(this.uri);if(t)switch(this.keyFormat){case Pe.WIDEVINE:this.pssh=t,t.length>=22&&(this.keyId=t.subarray(t.length-22,t.length-6));break;case Pe.PLAYREADY:{const i=new Uint8Array([154,4,240,121,152,64,66,134,171,146,230,91,224,136,95,149]);this.pssh=pc(i,null,t),this.keyId=no(t);break}default:{let i=t.subarray(0,16);if(i.length!==16){const n=new Uint8Array(16);n.set(i,16-i.length),i=n}this.keyId=i;break}}if(!this.keyId||this.keyId.byteLength!==16){let i=Ci[this.uri];if(!i){const n=Object.keys(Ci).length%Number.MAX_SAFE_INTEGER;i=new Uint8Array(16),new DataView(i.buffer,12,4).setUint32(0,n),Ci[this.uri]=i}this.keyId=i}return this}}function Sc(s){const e=new Uint8Array(16);for(let t=12;t<16;t++)e[t]=s>>8*(15-t)&255;return e}const po=/\{\$([a-zA-Z0-9-_]+)\}/g;function yr(s){return po.test(s)}function _e(s,e,t){if(s.variableList!==null||s.hasVariableRefs)for(let i=t.length;i--;){const n=t[i],r=e[n];r&&(e[n]=cn(s,r))}}function cn(s,e){if(s.variableList!==null||s.hasVariableRefs){const t=s.variableList;return e.replace(po,i=>{const n=i.substring(2,i.length-1),r=t?.[n];return r===void 0?(s.playlistParsingError||(s.playlistParsingError=new Error(`Missing preceding EXT-X-DEFINE tag for Variable Reference: "${n}"`)),i):r})}return e}function Sr(s,e,t){let i=s.variableList;i||(s.variableList=i={});let n,r;if("QUERYPARAM"in e){n=e.QUERYPARAM;try{const a=new self.URL(t).searchParams;if(a.has(n))r=a.get(n);else throw new Error(`"${n}" does not match any query parameter in URI: "${t}"`)}catch(a){s.playlistParsingError||(s.playlistParsingError=new Error(`EXT-X-DEFINE QUERYPARAM: ${a.message}`))}}else n=e.NAME,r=e.VALUE;n in i?s.playlistParsingError||(s.playlistParsingError=new Error(`EXT-X-DEFINE duplicate Variable Name declarations: "${n}"`)):i[n]=r||""}function Tc(s,e,t){const i=e.IMPORT;if(t&&i in t){let n=s.variableList;n||(s.variableList=n={}),n[i]=t[i]}else s.playlistParsingError||(s.playlistParsingError=new Error(`EXT-X-DEFINE IMPORT attribute not found in Multivariant Playlist: "${i}"`))}function _t(s=!0){return typeof self>"u"?void 0:(s||!self.MediaSource)&&self.ManagedMediaSource||self.MediaSource||self.WebKitMediaSource}function xc(s){return typeof self<"u"&&s===self.ManagedMediaSource}const ts={audio:{a3ds:1,"ac-3":.95,"ac-4":1,alac:.9,alaw:1,dra1:1,"dts+":1,"dts-":1,dtsc:1,dtse:1,dtsh:1,"ec-3":.9,enca:1,fLaC:.9,flac:.9,FLAC:.9,g719:1,g726:1,m4ae:1,mha1:1,mha2:1,mhm1:1,mhm2:1,mlpa:1,mp4a:1,"raw ":1,Opus:1,opus:1,samr:1,sawb:1,sawp:1,sevc:1,sqcp:1,ssmv:1,twos:1,ulaw:1},video:{avc1:1,avc2:1,avc3:1,avc4:1,avcp:1,av01:.8,drac:1,dva1:1,dvav:1,dvh1:.7,dvhe:.7,encv:1,hev1:.75,hvc1:.75,mjp2:1,mp4v:1,mvc1:1,mvc2:1,mvc3:1,mvc4:1,resv:1,rv60:1,s263:1,svc1:1,svc2:1,"vc-1":1,vp08:1,vp09:.9},text:{stpp:1,wvtt:1}};function Ec(s,e){const t=ts[e];return!!t&&!!t[s.slice(0,4)]}function Fs(s,e,t=!0){return!s.split(",").some(i=>!vo(i,e,t))}function vo(s,e,t=!0){var i;const n=_t(t);return(i=n?.isTypeSupported(gi(s,e)))!=null?i:!1}function gi(s,e){return`${e}/mp4;codecs="${s}"`}function Tr(s){if(s){const e=s.substring(0,4);return ts.video[e]}return 2}function is(s){return s.split(",").reduce((e,t)=>{const i=ts.video[t];return i?(i*2+e)/(e?3:2):(ts.audio[t]+e)/(e?2:1)},0)}const Os={};function Ac(s,e=!0){if(Os[s])return Os[s];const t={flac:["flac","fLaC","FLAC"],opus:["opus","Opus"]}[s];for(let i=0;i<t.length;i++)if(vo(t[i],"audio",e))return Os[s]=t[i],t[i];return s}const bc=/flac|opus/i;function ss(s,e=!0){return s.replace(bc,t=>Ac(t.toLowerCase(),e))}function xr(s,e){return s&&s!=="mp4a"?s:e&&e.split(",")[0]}function Lc(s){const e=s.split(",");for(let t=0;t<e.length;t++){const i=e[t].split(".");if(i.length>2){let n=i.shift()+".";n+=parseInt(i.shift()).toString(16),n+=("000"+parseInt(i.shift()).toString(16)).slice(-4),e[t]=n}}return e.join(",")}const Er=/#EXT-X-STREAM-INF:([^\r\n]*)(?:[\r\n](?:#[^\r\n]*)?)*([^\r\n]+)|#EXT-X-(SESSION-DATA|SESSION-KEY|DEFINE|CONTENT-STEERING|START):([^\r\n]*)[\r\n]+/g,Ar=/#EXT-X-MEDIA:(.*)/g,Rc=/^#EXT(?:INF|-X-TARGETDURATION):/m,br=new RegExp([/#EXTINF:\s*(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/(?!#) *(\S[^\r\n]*)/.source,/#EXT-X-BYTERANGE:*(.+)/.source,/#EXT-X-PROGRAM-DATE-TIME:(.+)/.source,/#.*/.source].join("|"),"g"),Ic=new RegExp([/#(EXTM3U)/.source,/#EXT-X-(DATERANGE|DEFINE|KEY|MAP|PART|PART-INF|PLAYLIST-TYPE|PRELOAD-HINT|RENDITION-REPORT|SERVER-CONTROL|SKIP|START):(.+)/.source,/#EXT-X-(BITRATE|DISCONTINUITY-SEQUENCE|MEDIA-SEQUENCE|TARGETDURATION|VERSION): *(\d+)/.source,/#EXT-X-(DISCONTINUITY|ENDLIST|GAP|INDEPENDENT-SEGMENTS)/.source,/(#)([^:]*):(.*)/.source,/(#)(.*)(?:.*)\r?\n?/.source].join("|"));class it{static findGroup(e,t){for(let i=0;i<e.length;i++){const n=e[i];if(n.id===t)return n}}static resolve(e,t){return In.buildAbsoluteURL(t,e,{alwaysNormalize:!0})}static isMediaPlaylist(e){return Rc.test(e)}static parseMasterPlaylist(e,t){const i=yr(e),n={contentSteering:null,levels:[],playlistParsingError:null,sessionData:null,sessionKeys:null,startTimeOffset:null,variableList:null,hasVariableRefs:i},r=[];Er.lastIndex=0;let a;for(;(a=Er.exec(e))!=null;)if(a[1]){var o;const u=new pe(a[1]);_e(n,u,["CODECS","SUPPLEMENTAL-CODECS","ALLOWED-CPC","PATHWAY-ID","STABLE-VARIANT-ID","AUDIO","VIDEO","SUBTITLES","CLOSED-CAPTIONS","NAME"]);const c=cn(n,a[2]),d={attrs:u,bitrate:u.decimalInteger("BANDWIDTH")||u.decimalInteger("AVERAGE-BANDWIDTH"),name:u.NAME,url:it.resolve(c,t)},f=u.decimalResolution("RESOLUTION");f&&(d.width=f.width,d.height=f.height),Cc(u.CODECS,d),(o=d.unknownCodecs)!=null&&o.length||r.push(d),n.levels.push(d)}else if(a[3]){const u=a[3],c=a[4];switch(u){case"SESSION-DATA":{const d=new pe(c);_e(n,d,["DATA-ID","LANGUAGE","VALUE","URI"]);const f=d["DATA-ID"];f&&(n.sessionData===null&&(n.sessionData={}),n.sessionData[f]=d);break}case"SESSION-KEY":{const d=Lr(c,t,n);d.encrypted&&d.isSupported()?(n.sessionKeys===null&&(n.sessionKeys=[]),n.sessionKeys.push(d)):_.warn(`[Keys] Ignoring invalid EXT-X-SESSION-KEY tag: "${c}"`);break}case"DEFINE":{{const d=new pe(c);_e(n,d,["NAME","VALUE","QUERYPARAM"]),Sr(n,d,t)}break}case"CONTENT-STEERING":{const d=new pe(c);_e(n,d,["SERVER-URI","PATHWAY-ID"]),n.contentSteering={uri:it.resolve(d["SERVER-URI"],t),pathwayId:d["PATHWAY-ID"]||"."};break}case"START":{n.startTimeOffset=Rr(c);break}}}const l=r.length>0&&r.length<n.levels.length;return n.levels=l?r:n.levels,n.levels.length===0&&(n.playlistParsingError=new Error("no levels found in manifest")),n}static parseMasterPlaylistMedia(e,t,i){let n;const r={},a=i.levels,o={AUDIO:a.map(u=>({id:u.attrs.AUDIO,audioCodec:u.audioCodec})),SUBTITLES:a.map(u=>({id:u.attrs.SUBTITLES,textCodec:u.textCodec})),"CLOSED-CAPTIONS":[]};let l=0;for(Ar.lastIndex=0;(n=Ar.exec(e))!==null;){const u=new pe(n[1]),c=u.TYPE;if(c){const d=o[c],f=r[c]||[];r[c]=f,_e(i,u,["URI","GROUP-ID","LANGUAGE","ASSOC-LANGUAGE","STABLE-RENDITION-ID","NAME","INSTREAM-ID","CHARACTERISTICS","CHANNELS"]);const h=u.LANGUAGE,g=u["ASSOC-LANGUAGE"],p=u.CHANNELS,m=u.CHARACTERISTICS,y=u["INSTREAM-ID"],S={attrs:u,bitrate:0,id:l++,groupId:u["GROUP-ID"]||"",name:u.NAME||h||"",type:c,default:u.bool("DEFAULT"),autoselect:u.bool("AUTOSELECT"),forced:u.bool("FORCED"),lang:h,url:u.URI?it.resolve(u.URI,t):""};if(g&&(S.assocLang=g),p&&(S.channels=p),m&&(S.characteristics=m),y&&(S.instreamId=y),d!=null&&d.length){const T=it.findGroup(d,S.groupId)||d[0];Ir(S,T,"audioCodec"),Ir(S,T,"textCodec")}f.push(S)}}return r}static parseLevelPlaylist(e,t,i,n,r,a){const o=new Vu(t),l=o.fragments;let u=null,c=0,d=0,f=0,h=0,g=null,p=new Is(n,t),m,y,S,T=-1,I=!1,E=null;for(br.lastIndex=0,o.m3u8=e,o.hasVariableRefs=yr(e);(m=br.exec(e))!==null;){I&&(I=!1,p=new Is(n,t),p.start=f,p.sn=c,p.cc=h,p.level=i,u&&(p.initSegment=u,p.rawProgramDateTime=u.rawProgramDateTime,u.rawProgramDateTime=null,E&&(p.setByteRange(E),E=null)));const b=m[1];if(b){p.duration=parseFloat(b);const x=(" "+m[2]).slice(1);p.title=x||null,p.tagList.push(x?["INF",b,x]:["INF",b])}else if(m[3]){if(J(p.duration)){p.start=f,S&&wr(p,S,o),p.sn=c,p.level=i,p.cc=h,l.push(p);const x=(" "+m[3]).slice(1);p.relurl=cn(o,x),Cr(p,g),g=p,f+=p.duration,c++,d=0,I=!0}}else if(m[4]){const x=(" "+m[4]).slice(1);g?p.setByteRange(x,g):p.setByteRange(x)}else if(m[5])p.rawProgramDateTime=(" "+m[5]).slice(1),p.tagList.push(["PROGRAM-DATE-TIME",p.rawProgramDateTime]),T===-1&&(T=l.length);else{if(m=m[0].match(Ic),!m){_.warn("No matches on slow regex match for level playlist!");continue}for(y=1;y<m.length&&!(typeof m[y]<"u");y++);const x=(" "+m[y]).slice(1),D=(" "+m[y+1]).slice(1),C=m[y+2]?(" "+m[y+2]).slice(1):"";switch(x){case"PLAYLIST-TYPE":o.type=D.toUpperCase();break;case"MEDIA-SEQUENCE":c=o.startSN=parseInt(D);break;case"SKIP":{const M=new pe(D);_e(o,M,["RECENTLY-REMOVED-DATERANGES"]);const N=M.decimalInteger("SKIPPED-SEGMENTS");if(J(N)){o.skippedSegments=N;for(let K=N;K--;)l.unshift(null);c+=N}const Y=M.enumeratedString("RECENTLY-REMOVED-DATERANGES");Y&&(o.recentlyRemovedDateranges=Y.split("	"));break}case"TARGETDURATION":o.targetduration=Math.max(parseInt(D),1);break;case"VERSION":o.version=parseInt(D);break;case"INDEPENDENT-SEGMENTS":case"EXTM3U":break;case"ENDLIST":o.live=!1;break;case"#":(D||C)&&p.tagList.push(C?[D,C]:[D]);break;case"DISCONTINUITY":h++,p.tagList.push(["DIS"]);break;case"GAP":p.gap=!0,p.tagList.push([x]);break;case"BITRATE":p.tagList.push([x,D]);break;case"DATERANGE":{const M=new pe(D);_e(o,M,["ID","CLASS","START-DATE","END-DATE","SCTE35-CMD","SCTE35-OUT","SCTE35-IN"]),_e(o,M,M.clientAttrs);const N=new to(M,o.dateRanges[M.ID]);N.isValid||o.skippedSegments?o.dateRanges[N.id]=N:_.warn(`Ignoring invalid DATERANGE tag: "${D}"`),p.tagList.push(["EXT-X-DATERANGE",D]);break}case"DEFINE":{{const M=new pe(D);_e(o,M,["NAME","VALUE","IMPORT","QUERYPARAM"]),"IMPORT"in M?Tc(o,M,a):Sr(o,M,t)}break}case"DISCONTINUITY-SEQUENCE":h=parseInt(D);break;case"KEY":{const M=Lr(D,t,o);if(M.isSupported()){if(M.method==="NONE"){S=void 0;break}S||(S={}),S[M.keyFormat]&&(S=xe({},S)),S[M.keyFormat]=M}else _.warn(`[Keys] Ignoring invalid EXT-X-KEY tag: "${D}"`);break}case"START":o.startTimeOffset=Rr(D);break;case"MAP":{const M=new pe(D);if(_e(o,M,["BYTERANGE","URI"]),p.duration){const N=new Is(n,t);Dr(N,M,i,S),u=N,p.initSegment=u,u.rawProgramDateTime&&!p.rawProgramDateTime&&(p.rawProgramDateTime=u.rawProgramDateTime)}else{const N=p.byteRangeEndOffset;if(N){const Y=p.byteRangeStartOffset;E=`${N-Y}@${Y}`}else E=null;Dr(p,M,i,S),u=p,I=!0}break}case"SERVER-CONTROL":{const M=new pe(D);o.canBlockReload=M.bool("CAN-BLOCK-RELOAD"),o.canSkipUntil=M.optionalFloat("CAN-SKIP-UNTIL",0),o.canSkipDateRanges=o.canSkipUntil>0&&M.bool("CAN-SKIP-DATERANGES"),o.partHoldBack=M.optionalFloat("PART-HOLD-BACK",0),o.holdBack=M.optionalFloat("HOLD-BACK",0);break}case"PART-INF":{const M=new pe(D);o.partTarget=M.decimalFloatingPoint("PART-TARGET");break}case"PART":{let M=o.partList;M||(M=o.partList=[]);const N=d>0?M[M.length-1]:void 0,Y=d++,K=new pe(D);_e(o,K,["BYTERANGE","URI"]);const z=new $u(K,p,t,Y,N);M.push(z),p.duration+=z.duration;break}case"PRELOAD-HINT":{const M=new pe(D);_e(o,M,["URI"]),o.preloadHint=M;break}case"RENDITION-REPORT":{const M=new pe(D);_e(o,M,["URI"]),o.renditionReports=o.renditionReports||[],o.renditionReports.push(M);break}default:_.warn(`line parsed but not handled: ${m}`);break}}}g&&!g.relurl?(l.pop(),f-=g.duration,o.partList&&(o.fragmentHint=g)):o.partList&&(Cr(p,g),p.cc=h,o.fragmentHint=p,S&&wr(p,S,o));const P=l.length,R=l[0],w=l[P-1];if(f+=o.skippedSegments*o.targetduration,f>0&&P&&w){o.averagetargetduration=f/P;const b=w.sn;o.endSN=b!=="initSegment"?b:0,o.live||(w.endList=!0),R&&(o.startCC=R.cc)}else o.endSN=0,o.startCC=0;return o.fragmentHint&&(f+=o.fragmentHint.duration),o.totalduration=f,o.endCC=h,T>0&&Dc(l,T),o}}function Lr(s,e,t){var i,n;const r=new pe(s);_e(t,r,["KEYFORMAT","KEYFORMATVERSIONS","URI","IV","URI"]);const a=(i=r.METHOD)!=null?i:"",o=r.URI,l=r.hexadecimalInteger("IV"),u=r.KEYFORMATVERSIONS,c=(n=r.KEYFORMAT)!=null?n:"identity";o&&r.IV&&!l&&_.error(`Invalid IV: ${r.IV}`);const d=o?it.resolve(o,e):"",f=(u||"1").split("/").map(Number).filter(Number.isFinite);return new hi(a,d,c,f,l)}function Rr(s){const t=new pe(s).decimalFloatingPoint("TIME-OFFSET");return J(t)?t:null}function Cc(s,e){let t=(s||"").split(/[ ,]+/).filter(i=>i);["video","audio","text"].forEach(i=>{const n=t.filter(r=>Ec(r,i));n.length&&(e[`${i}Codec`]=n.join(","),t=t.filter(r=>n.indexOf(r)===-1))}),e.unknownCodecs=t}function Ir(s,e,t){const i=e[t];i&&(s[t]=i)}function Dc(s,e){let t=s[e];for(let i=e;i--;){const n=s[i];if(!n)return;n.programDateTime=t.programDateTime-n.duration*1e3,t=n}}function Cr(s,e){s.rawProgramDateTime?s.programDateTime=Date.parse(s.rawProgramDateTime):e!=null&&e.programDateTime&&(s.programDateTime=e.endProgramDateTime),J(s.programDateTime)||(s.programDateTime=null,s.rawProgramDateTime=null)}function Dr(s,e,t,i){s.relurl=e.URI,e.BYTERANGE&&s.setByteRange(e.BYTERANGE),s.level=t,s.sn="initSegment",i&&(s.levelkeys=i),s.initSegment=null}function wr(s,e,t){s.levelkeys=e;const{encryptedFragments:i}=t;(!i.length||i[i.length-1].levelkeys!==e)&&Object.keys(e).some(n=>e[n].isCommonEncryption)&&i.push(s)}var ce={MANIFEST:"manifest",LEVEL:"level",AUDIO_TRACK:"audioTrack",SUBTITLE_TRACK:"subtitleTrack"},ne={MAIN:"main",AUDIO:"audio",SUBTITLE:"subtitle"};function kr(s){const{type:e}=s;switch(e){case ce.AUDIO_TRACK:return ne.AUDIO;case ce.SUBTITLE_TRACK:return ne.SUBTITLE;default:return ne.MAIN}}function Ms(s,e){let t=s.url;return(t===void 0||t.indexOf("data:")===0)&&(t=e.url),t}class wc{constructor(e){this.hls=void 0,this.loaders=Object.create(null),this.variableList=null,this.hls=e,this.registerListeners()}startLoad(e){}stopLoad(){this.destroyInternalLoaders()}registerListeners(){const{hls:e}=this;e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.LEVEL_LOADING,this.onLevelLoading,this),e.on(v.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),e.on(v.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)}unregisterListeners(){const{hls:e}=this;e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.LEVEL_LOADING,this.onLevelLoading,this),e.off(v.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),e.off(v.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)}createInternalLoader(e){const t=this.hls.config,i=t.pLoader,n=t.loader,r=i||n,a=new r(t);return this.loaders[e.type]=a,a}getInternalLoader(e){return this.loaders[e.type]}resetInternalLoader(e){this.loaders[e]&&delete this.loaders[e]}destroyInternalLoaders(){for(const e in this.loaders){const t=this.loaders[e];t&&t.destroy(),this.resetInternalLoader(e)}}destroy(){this.variableList=null,this.unregisterListeners(),this.destroyInternalLoaders()}onManifestLoading(e,t){const{url:i}=t;this.variableList=null,this.load({id:null,level:0,responseType:"text",type:ce.MANIFEST,url:i,deliveryDirectives:null})}onLevelLoading(e,t){const{id:i,level:n,pathwayId:r,url:a,deliveryDirectives:o}=t;this.load({id:i,level:n,pathwayId:r,responseType:"text",type:ce.LEVEL,url:a,deliveryDirectives:o})}onAudioTrackLoading(e,t){const{id:i,groupId:n,url:r,deliveryDirectives:a}=t;this.load({id:i,groupId:n,level:null,responseType:"text",type:ce.AUDIO_TRACK,url:r,deliveryDirectives:a})}onSubtitleTrackLoading(e,t){const{id:i,groupId:n,url:r,deliveryDirectives:a}=t;this.load({id:i,groupId:n,level:null,responseType:"text",type:ce.SUBTITLE_TRACK,url:r,deliveryDirectives:a})}load(e){var t;const i=this.hls.config;let n=this.getInternalLoader(e);if(n){const u=n.context;if(u&&u.url===e.url&&u.level===e.level){_.trace("[playlist-loader]: playlist request ongoing");return}_.log(`[playlist-loader]: aborting previous loader for type: ${e.type}`),n.abort()}let r;if(e.type===ce.MANIFEST?r=i.manifestLoadPolicy.default:r=xe({},i.playlistLoadPolicy.default,{timeoutRetry:null,errorRetry:null}),n=this.createInternalLoader(e),J((t=e.deliveryDirectives)==null?void 0:t.part)){let u;if(e.type===ce.LEVEL&&e.level!==null?u=this.hls.levels[e.level].details:e.type===ce.AUDIO_TRACK&&e.id!==null?u=this.hls.audioTracks[e.id].details:e.type===ce.SUBTITLE_TRACK&&e.id!==null&&(u=this.hls.subtitleTracks[e.id].details),u){const c=u.partTarget,d=u.targetduration;if(c&&d){const f=Math.max(c*3,d*.8)*1e3;r=xe({},r,{maxTimeToFirstByteMs:Math.min(f,r.maxTimeToFirstByteMs),maxLoadTimeMs:Math.min(f,r.maxTimeToFirstByteMs)})}}}const a=r.errorRetry||r.timeoutRetry||{},o={loadPolicy:r,timeout:r.maxLoadTimeMs,maxRetry:a.maxNumRetry||0,retryDelay:a.retryDelayMs||0,maxRetryDelay:a.maxRetryDelayMs||0},l={onSuccess:(u,c,d,f)=>{const h=this.getInternalLoader(d);this.resetInternalLoader(d.type);const g=u.data;if(g.indexOf("#EXTM3U")!==0){this.handleManifestParsingError(u,d,new Error("no EXTM3U delimiter"),f||null,c);return}c.parsing.start=performance.now(),it.isMediaPlaylist(g)?this.handleTrackOrLevelPlaylist(u,c,d,f||null,h):this.handleMasterPlaylist(u,c,d,f)},onError:(u,c,d,f)=>{this.handleNetworkError(c,d,!1,u,f)},onTimeout:(u,c,d)=>{this.handleNetworkError(c,d,!0,void 0,u)}};n.load(e,o,l)}handleMasterPlaylist(e,t,i,n){const r=this.hls,a=e.data,o=Ms(e,i),l=it.parseMasterPlaylist(a,o);if(l.playlistParsingError){this.handleManifestParsingError(e,i,l.playlistParsingError,n,t);return}const{contentSteering:u,levels:c,sessionData:d,sessionKeys:f,startTimeOffset:h,variableList:g}=l;this.variableList=g;const{AUDIO:p=[],SUBTITLES:m,"CLOSED-CAPTIONS":y}=it.parseMasterPlaylistMedia(a,o,l);p.length&&!p.some(T=>!T.url)&&c[0].audioCodec&&!c[0].attrs.AUDIO&&(_.log("[playlist-loader]: audio codec signaled in quality level, but no embedded audio track signaled, create one"),p.unshift({type:"main",name:"main",groupId:"main",default:!1,autoselect:!1,forced:!1,id:-1,attrs:new pe({}),bitrate:0,url:""})),r.trigger(v.MANIFEST_LOADED,{levels:c,audioTracks:p,subtitles:m,captions:y,contentSteering:u,url:o,stats:t,networkDetails:n,sessionData:d,sessionKeys:f,startTimeOffset:h,variableList:g})}handleTrackOrLevelPlaylist(e,t,i,n,r){const a=this.hls,{id:o,level:l,type:u}=i,c=Ms(e,i),d=0,f=J(l)?l:J(o)?o:0,h=kr(i),g=it.parseLevelPlaylist(e.data,c,f,h,d,this.variableList);if(u===ce.MANIFEST){const p={attrs:new pe({}),bitrate:0,details:g,name:"",url:c};a.trigger(v.MANIFEST_LOADED,{levels:[p],audioTracks:[],url:c,stats:t,networkDetails:n,sessionData:null,sessionKeys:null,contentSteering:null,startTimeOffset:null,variableList:null})}t.parsing.end=performance.now(),i.levelDetails=g,this.handlePlaylistLoaded(g,e,t,i,n,r)}handleManifestParsingError(e,t,i,n,r){this.hls.trigger(v.ERROR,{type:re.NETWORK_ERROR,details:O.MANIFEST_PARSING_ERROR,fatal:t.type===ce.MANIFEST,url:e.url,err:i,error:i,reason:i.message,response:e,context:t,networkDetails:n,stats:r})}handleNetworkError(e,t,i=!1,n,r){let a=`A network ${i?"timeout":"error"+(n?" (status "+n.code+")":"")} occurred while loading ${e.type}`;e.type===ce.LEVEL?a+=`: ${e.level} id: ${e.id}`:(e.type===ce.AUDIO_TRACK||e.type===ce.SUBTITLE_TRACK)&&(a+=` id: ${e.id} group-id: "${e.groupId}"`);const o=new Error(a);_.warn(`[playlist-loader]: ${a}`);let l=O.UNKNOWN,u=!1;const c=this.getInternalLoader(e);switch(e.type){case ce.MANIFEST:l=i?O.MANIFEST_LOAD_TIMEOUT:O.MANIFEST_LOAD_ERROR,u=!0;break;case ce.LEVEL:l=i?O.LEVEL_LOAD_TIMEOUT:O.LEVEL_LOAD_ERROR,u=!1;break;case ce.AUDIO_TRACK:l=i?O.AUDIO_TRACK_LOAD_TIMEOUT:O.AUDIO_TRACK_LOAD_ERROR,u=!1;break;case ce.SUBTITLE_TRACK:l=i?O.SUBTITLE_TRACK_LOAD_TIMEOUT:O.SUBTITLE_LOAD_ERROR,u=!1;break}c&&this.resetInternalLoader(e.type);const d={type:re.NETWORK_ERROR,details:l,fatal:u,url:e.url,loader:c,context:e,error:o,networkDetails:t,stats:r};if(n){const f=t?.url||e.url;d.response=Re({url:f,data:void 0},n)}this.hls.trigger(v.ERROR,d)}handlePlaylistLoaded(e,t,i,n,r,a){const o=this.hls,{type:l,level:u,id:c,groupId:d,deliveryDirectives:f}=n,h=Ms(t,n),g=kr(n),p=typeof n.level=="number"&&g===ne.MAIN?u:void 0;if(!e.fragments.length){const y=new Error("No Segments found in Playlist");o.trigger(v.ERROR,{type:re.NETWORK_ERROR,details:O.LEVEL_EMPTY_ERROR,fatal:!1,url:h,error:y,reason:y.message,response:t,context:n,level:p,parent:g,networkDetails:r,stats:i});return}e.targetduration||(e.playlistParsingError=new Error("Missing Target Duration"));const m=e.playlistParsingError;if(m){o.trigger(v.ERROR,{type:re.NETWORK_ERROR,details:O.LEVEL_PARSING_ERROR,fatal:!1,url:h,error:m,reason:m.message,response:t,context:n,level:p,parent:g,networkDetails:r,stats:i});return}switch(e.live&&a&&(a.getCacheAge&&(e.ageHeader=a.getCacheAge()||0),(!a.getCacheAge||isNaN(e.ageHeader))&&(e.ageHeader=0)),l){case ce.MANIFEST:case ce.LEVEL:o.trigger(v.LEVEL_LOADED,{details:e,level:p||0,id:c||0,stats:i,networkDetails:r,deliveryDirectives:f});break;case ce.AUDIO_TRACK:o.trigger(v.AUDIO_TRACK_LOADED,{details:e,id:c||0,groupId:d||"",stats:i,networkDetails:r,deliveryDirectives:f});break;case ce.SUBTITLE_TRACK:o.trigger(v.SUBTITLE_TRACK_LOADED,{details:e,id:c||0,groupId:d||"",stats:i,networkDetails:r,deliveryDirectives:f});break}}}function yo(s,e){let t;try{t=new Event("addtrack")}catch{t=document.createEvent("Event"),t.initEvent("addtrack",!1,!1)}t.track=s,e.dispatchEvent(t)}function So(s,e){const t=s.mode;if(t==="disabled"&&(s.mode="hidden"),s.cues&&!s.cues.getCueById(e.id))try{if(s.addCue(e),!s.cues.getCueById(e.id))throw new Error(`addCue is failed for: ${e}`)}catch(i){_.debug(`[texttrack-utils]: ${i}`);try{const n=new self.TextTrackCue(e.startTime,e.endTime,e.text);n.id=e.id,s.addCue(n)}catch(n){_.debug(`[texttrack-utils]: Legacy TextTrackCue fallback failed: ${n}`)}}t==="disabled"&&(s.mode=t)}function zt(s){const e=s.mode;if(e==="disabled"&&(s.mode="hidden"),s.cues)for(let t=s.cues.length;t--;)s.removeCue(s.cues[t]);e==="disabled"&&(s.mode=e)}function dn(s,e,t,i){const n=s.mode;if(n==="disabled"&&(s.mode="hidden"),s.cues&&s.cues.length>0){const r=_c(s.cues,e,t);for(let a=0;a<r.length;a++)(!i||i(r[a]))&&s.removeCue(r[a])}n==="disabled"&&(s.mode=n)}function kc(s,e){if(e<s[0].startTime)return 0;const t=s.length-1;if(e>s[t].endTime)return-1;let i=0,n=t;for(;i<=n;){const r=Math.floor((n+i)/2);if(e<s[r].startTime)n=r-1;else if(e>s[r].startTime&&i<t)i=r+1;else return r}return s[i].startTime-e<e-s[n].startTime?i:n}function _c(s,e,t){const i=[],n=kc(s,e);if(n>-1)for(let r=n,a=s.length;r<a;r++){const o=s[r];if(o.startTime>=e&&o.endTime<=t)i.push(o);else if(o.startTime>t)return i}return i}function qi(s){const e=[];for(let t=0;t<s.length;t++){const i=s[t];(i.kind==="subtitles"||i.kind==="captions")&&i.label&&e.push(s[t])}return e}var He={audioId3:"org.id3",dateRange:"com.apple.quicktime.HLS",emsg:"https://aomedia.org/emsg/ID3"};const Pc=.25;function fn(){if(!(typeof self>"u"))return self.VTTCue||self.TextTrackCue}function _r(s,e,t,i,n){let r=new s(e,t,"");try{r.value=i,n&&(r.type=n)}catch{r=new s(e,t,JSON.stringify(n?Re({type:n},i):i))}return r}const Di=(()=>{const s=fn();try{s&&new s(0,Number.POSITIVE_INFINITY,"")}catch{return Number.MAX_VALUE}return Number.POSITIVE_INFINITY})();function Ns(s,e){return s.getTime()/1e3-e}function Fc(s){return Uint8Array.from(s.replace(/^0x/,"").replace(/([\da-fA-F]{2}) ?/g,"0x$1 ").replace(/ +$/,"").split(" ")).buffer}class Oc{constructor(e){this.hls=void 0,this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=e,this._registerListeners()}destroy(){this._unregisterListeners(),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=null}_registerListeners(){const{hls:e}=this;e.on(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),e.on(v.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(v.LEVEL_UPDATED,this.onLevelUpdated,this)}_unregisterListeners(){const{hls:e}=this;e.off(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),e.off(v.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(v.LEVEL_UPDATED,this.onLevelUpdated,this)}onMediaAttached(e,t){this.media=t.media}onMediaDetaching(){this.id3Track&&(zt(this.id3Track),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={})}onManifestLoading(){this.dateRangeCuesAppended={}}createTrack(e){const t=this.getID3Track(e.textTracks);return t.mode="hidden",t}getID3Track(e){if(this.media){for(let t=0;t<e.length;t++){const i=e[t];if(i.kind==="metadata"&&i.label==="id3")return yo(i,this.media),i}return this.media.addTextTrack("metadata","id3")}}onFragParsingMetadata(e,t){if(!this.media)return;const{hls:{config:{enableEmsgMetadataCues:i,enableID3MetadataCues:n}}}=this;if(!i&&!n)return;const{samples:r}=t;this.id3Track||(this.id3Track=this.createTrack(this.media));const a=fn();if(a)for(let o=0;o<r.length;o++){const l=r[o].type;if(l===He.emsg&&!i||!n)continue;const u=oo(r[o].data);if(u){const c=r[o].pts;let d=c+r[o].duration;d>Di&&(d=Di),d-c<=0&&(d=c+Pc);for(let h=0;h<u.length;h++){const g=u[h];if(!ao(g)){this.updateId3CueEnds(c,l);const p=_r(a,c,d,g,l);p&&this.id3Track.addCue(p)}}}}}updateId3CueEnds(e,t){var i;const n=(i=this.id3Track)==null?void 0:i.cues;if(n)for(let r=n.length;r--;){const a=n[r];a.type===t&&a.startTime<e&&a.endTime===Di&&(a.endTime=e)}}onBufferFlushing(e,{startOffset:t,endOffset:i,type:n}){const{id3Track:r,hls:a}=this;if(!a)return;const{config:{enableEmsgMetadataCues:o,enableID3MetadataCues:l}}=a;if(r&&(o||l)){let u;n==="audio"?u=c=>c.type===He.audioId3&&l:n==="video"?u=c=>c.type===He.emsg&&o:u=c=>c.type===He.audioId3&&l||c.type===He.emsg&&o,dn(r,t,i,u)}}onLevelUpdated(e,{details:t}){if(!this.media||!t.hasProgramDateTime||!this.hls.config.enableDateRangeMetadataCues)return;const{dateRangeCuesAppended:i,id3Track:n}=this,{dateRanges:r}=t,a=Object.keys(r);if(n){const c=Object.keys(i).filter(d=>!a.includes(d));for(let d=c.length;d--;){const f=c[d];Object.keys(i[f].cues).forEach(h=>{n.removeCue(i[f].cues[h])}),delete i[f]}}const o=t.fragments[t.fragments.length-1];if(a.length===0||!J(o?.programDateTime))return;this.id3Track||(this.id3Track=this.createTrack(this.media));const l=o.programDateTime/1e3-o.start,u=fn();for(let c=0;c<a.length;c++){const d=a[c],f=r[d],h=Ns(f.startDate,l),g=i[d],p=g?.cues||{};let m=g?.durationKnown||!1,y=Di;const S=f.endDate;if(S)y=Ns(S,l),m=!0;else if(f.endOnNext&&!m){const I=a.reduce((E,P)=>{if(P!==f.id){const R=r[P];if(R.class===f.class&&R.startDate>f.startDate&&(!E||f.startDate<E.startDate))return R}return E},null);I&&(y=Ns(I.startDate,l),m=!0)}const T=Object.keys(f.attr);for(let I=0;I<T.length;I++){const E=T[I];if(!Bu(E))continue;const P=p[E];if(P)m&&!g.durationKnown&&(P.endTime=y);else if(u){let R=f.attr[E];Uu(E)&&(R=Fc(R));const w=_r(u,h,y,{key:E,data:R},He.dateRange);w&&(w.id=d,this.id3Track.addCue(w),p[E]=w)}}i[d]={cues:p,dateRange:f,durationKnown:m}}}}class Mc{constructor(e){this.hls=void 0,this.config=void 0,this.media=null,this.levelDetails=null,this.currentTime=0,this.stallCount=0,this._latency=null,this.timeupdateHandler=()=>this.timeupdate(),this.hls=e,this.config=e.config,this.registerListeners()}get latency(){return this._latency||0}get maxLatency(){const{config:e,levelDetails:t}=this;return e.liveMaxLatencyDuration!==void 0?e.liveMaxLatencyDuration:t?e.liveMaxLatencyDurationCount*t.targetduration:0}get targetLatency(){const{levelDetails:e}=this;if(e===null)return null;const{holdBack:t,partHoldBack:i,targetduration:n}=e,{liveSyncDuration:r,liveSyncDurationCount:a,lowLatencyMode:o}=this.config,l=this.hls.userConfig;let u=o&&i||t;(l.liveSyncDuration||l.liveSyncDurationCount||u===0)&&(u=r!==void 0?r:a*n);const c=n;return u+Math.min(this.stallCount*1,c)}get liveSyncPosition(){const e=this.estimateLiveEdge(),t=this.targetLatency,i=this.levelDetails;if(e===null||t===null||i===null)return null;const n=i.edge,r=e-t-this.edgeStalled,a=n-i.totalduration,o=n-(this.config.lowLatencyMode&&i.partTarget||i.targetduration);return Math.min(Math.max(a,r),o)}get drift(){const{levelDetails:e}=this;return e===null?1:e.drift}get edgeStalled(){const{levelDetails:e}=this;if(e===null)return 0;const t=(this.config.lowLatencyMode&&e.partTarget||e.targetduration)*3;return Math.max(e.age-t,0)}get forwardBufferLength(){const{media:e,levelDetails:t}=this;if(!e||!t)return 0;const i=e.buffered.length;return(i?e.buffered.end(i-1):t.edge)-this.currentTime}destroy(){this.unregisterListeners(),this.onMediaDetaching(),this.levelDetails=null,this.hls=this.timeupdateHandler=null}registerListeners(){this.hls.on(v.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(v.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.on(v.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(v.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.on(v.ERROR,this.onError,this)}unregisterListeners(){this.hls.off(v.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(v.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.off(v.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.off(v.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.off(v.ERROR,this.onError,this)}onMediaAttached(e,t){this.media=t.media,this.media.addEventListener("timeupdate",this.timeupdateHandler)}onMediaDetaching(){this.media&&(this.media.removeEventListener("timeupdate",this.timeupdateHandler),this.media=null)}onManifestLoading(){this.levelDetails=null,this._latency=null,this.stallCount=0}onLevelUpdated(e,{details:t}){this.levelDetails=t,t.advanced&&this.timeupdate(),!t.live&&this.media&&this.media.removeEventListener("timeupdate",this.timeupdateHandler)}onError(e,t){var i;t.details===O.BUFFER_STALLED_ERROR&&(this.stallCount++,(i=this.levelDetails)!=null&&i.live&&_.warn("[playback-rate-controller]: Stall detected, adjusting target latency"))}timeupdate(){const{media:e,levelDetails:t}=this;if(!e||!t)return;this.currentTime=e.currentTime;const i=this.computeLatency();if(i===null)return;this._latency=i;const{lowLatencyMode:n,maxLiveSyncPlaybackRate:r}=this.config;if(!n||r===1||!t.live)return;const a=this.targetLatency;if(a===null)return;const o=i-a,l=Math.min(this.maxLatency,a+t.targetduration);if(o<l&&o>.05&&this.forwardBufferLength>1){const c=Math.min(2,Math.max(1,r)),d=Math.round(2/(1+Math.exp(-.75*o-this.edgeStalled))*20)/20;e.playbackRate=Math.min(c,Math.max(1,d))}else e.playbackRate!==1&&e.playbackRate!==0&&(e.playbackRate=1)}estimateLiveEdge(){const{levelDetails:e}=this;return e===null?null:e.edge+e.age}computeLatency(){const e=this.estimateLiveEdge();return e===null?null:e-this.currentTime}}const hn=["NONE","TYPE-0","TYPE-1",null];function Nc(s){return hn.indexOf(s)>-1}const ns=["SDR","PQ","HLG"];function Bc(s){return!!s&&ns.indexOf(s)>-1}var Wi={No:"",Yes:"YES",v2:"v2"};function Pr(s){const{canSkipUntil:e,canSkipDateRanges:t,age:i}=s,n=i<e/2;return e&&n?t?Wi.v2:Wi.Yes:Wi.No}class Fr{constructor(e,t,i){this.msn=void 0,this.part=void 0,this.skip=void 0,this.msn=e,this.part=t,this.skip=i}addDirectives(e){const t=new self.URL(e);return this.msn!==void 0&&t.searchParams.set("_HLS_msn",this.msn.toString()),this.part!==void 0&&t.searchParams.set("_HLS_part",this.part.toString()),this.skip&&t.searchParams.set("_HLS_skip",this.skip),t.href}}class Jt{constructor(e){this._attrs=void 0,this.audioCodec=void 0,this.bitrate=void 0,this.codecSet=void 0,this.url=void 0,this.frameRate=void 0,this.height=void 0,this.id=void 0,this.name=void 0,this.videoCodec=void 0,this.width=void 0,this.details=void 0,this.fragmentError=0,this.loadError=0,this.loaded=void 0,this.realBitrate=0,this.supportedPromise=void 0,this.supportedResult=void 0,this._avgBitrate=0,this._audioGroups=void 0,this._subtitleGroups=void 0,this._urlId=0,this.url=[e.url],this._attrs=[e.attrs],this.bitrate=e.bitrate,e.details&&(this.details=e.details),this.id=e.id||0,this.name=e.name,this.width=e.width||0,this.height=e.height||0,this.frameRate=e.attrs.optionalFloat("FRAME-RATE",0),this._avgBitrate=e.attrs.decimalInteger("AVERAGE-BANDWIDTH"),this.audioCodec=e.audioCodec,this.videoCodec=e.videoCodec,this.codecSet=[e.videoCodec,e.audioCodec].filter(t=>!!t).map(t=>t.substring(0,4)).join(","),this.addGroupId("audio",e.attrs.AUDIO),this.addGroupId("text",e.attrs.SUBTITLES)}get maxBitrate(){return Math.max(this.realBitrate,this.bitrate)}get averageBitrate(){return this._avgBitrate||this.realBitrate||this.bitrate}get attrs(){return this._attrs[0]}get codecs(){return this.attrs.CODECS||""}get pathwayId(){return this.attrs["PATHWAY-ID"]||"."}get videoRange(){return this.attrs["VIDEO-RANGE"]||"SDR"}get score(){return this.attrs.optionalFloat("SCORE",0)}get uri(){return this.url[0]||""}hasAudioGroup(e){return Or(this._audioGroups,e)}hasSubtitleGroup(e){return Or(this._subtitleGroups,e)}get audioGroups(){return this._audioGroups}get subtitleGroups(){return this._subtitleGroups}addGroupId(e,t){if(t){if(e==="audio"){let i=this._audioGroups;i||(i=this._audioGroups=[]),i.indexOf(t)===-1&&i.push(t)}else if(e==="text"){let i=this._subtitleGroups;i||(i=this._subtitleGroups=[]),i.indexOf(t)===-1&&i.push(t)}}}get urlId(){return 0}set urlId(e){}get audioGroupIds(){return this.audioGroups?[this.audioGroupId]:void 0}get textGroupIds(){return this.subtitleGroups?[this.textGroupId]:void 0}get audioGroupId(){var e;return(e=this.audioGroups)==null?void 0:e[0]}get textGroupId(){var e;return(e=this.subtitleGroups)==null?void 0:e[0]}addFallback(){}}function Or(s,e){return!e||!s?!1:s.indexOf(e)!==-1}function Bs(s,e){const t=e.startPTS;if(J(t)){let i=0,n;e.sn>s.sn?(i=t-s.start,n=s):(i=s.start-t,n=e),n.duration!==i&&(n.duration=i)}else e.sn>s.sn?s.cc===e.cc&&s.minEndPTS?e.start=s.start+(s.minEndPTS-s.start):e.start=s.start+s.duration:e.start=Math.max(s.start-e.duration,0)}function To(s,e,t,i,n,r){i-t<=0&&(_.warn("Fragment should have a positive duration",e),i=t+e.duration,r=n+e.duration);let o=t,l=i;const u=e.startPTS,c=e.endPTS;if(J(u)){const m=Math.abs(u-t);J(e.deltaPTS)?e.deltaPTS=Math.max(m,e.deltaPTS):e.deltaPTS=m,o=Math.max(t,u),t=Math.min(t,u),n=Math.min(n,e.startDTS),l=Math.min(i,c),i=Math.max(i,c),r=Math.max(r,e.endDTS)}const d=t-e.start;e.start!==0&&(e.start=t),e.duration=i-e.start,e.startPTS=t,e.maxStartPTS=o,e.startDTS=n,e.endPTS=i,e.minEndPTS=l,e.endDTS=r;const f=e.sn;if(!s||f<s.startSN||f>s.endSN)return 0;let h;const g=f-s.startSN,p=s.fragments;for(p[g]=e,h=g;h>0;h--)Bs(p[h],p[h-1]);for(h=g;h<p.length-1;h++)Bs(p[h],p[h+1]);return s.fragmentHint&&Bs(p[p.length-1],s.fragmentHint),s.PTSKnown=s.alignedSliding=!0,d}function Uc(s,e){let t=null;const i=s.fragments;for(let o=i.length-1;o>=0;o--){const l=i[o].initSegment;if(l){t=l;break}}s.fragmentHint&&delete s.fragmentHint.endPTS;let n;Vc(s,e,(o,l,u,c)=>{if(e.skippedSegments&&l.cc!==o.cc){const d=o.cc-l.cc;for(let f=u;f<c.length;f++)c[f].cc+=d}J(o.startPTS)&&J(o.endPTS)&&(l.start=l.startPTS=o.startPTS,l.startDTS=o.startDTS,l.maxStartPTS=o.maxStartPTS,l.endPTS=o.endPTS,l.endDTS=o.endDTS,l.minEndPTS=o.minEndPTS,l.duration=o.endPTS-o.startPTS,l.duration&&(n=l),e.PTSKnown=e.alignedSliding=!0),l.elementaryStreams=o.elementaryStreams,l.loader=o.loader,l.stats=o.stats,o.initSegment&&(l.initSegment=o.initSegment,t=o.initSegment)});const r=e.fragments;if(t&&(e.fragmentHint?r.concat(e.fragmentHint):r).forEach(l=>{var u;l&&(!l.initSegment||l.initSegment.relurl===((u=t)==null?void 0:u.relurl))&&(l.initSegment=t)}),e.skippedSegments){if(e.deltaUpdateFailed=r.some(o=>!o),e.deltaUpdateFailed){_.warn("[level-helper] Previous playlist missing segments skipped in delta playlist");for(let o=e.skippedSegments;o--;)r.shift();e.startSN=r[0].sn}else e.canSkipDateRanges&&(e.dateRanges=$c(s.dateRanges,e.dateRanges,e.recentlyRemovedDateranges));e.startCC=e.fragments[0].cc,e.endCC=r[r.length-1].cc}Gc(s.partList,e.partList,(o,l)=>{l.elementaryStreams=o.elementaryStreams,l.stats=o.stats}),n?To(e,n,n.startPTS,n.endPTS,n.startDTS,n.endDTS):xo(s,e),r.length&&(e.totalduration=e.edge-r[0].start),e.driftStartTime=s.driftStartTime,e.driftStart=s.driftStart;const a=e.advancedDateTime;if(e.advanced&&a){const o=e.edge;e.driftStart||(e.driftStartTime=a,e.driftStart=o),e.driftEndTime=a,e.driftEnd=o}else e.driftEndTime=s.driftEndTime,e.driftEnd=s.driftEnd,e.advancedDateTime=s.advancedDateTime}function $c(s,e,t){const i=xe({},s);return t&&t.forEach(n=>{delete i[n]}),Object.keys(e).forEach(n=>{const r=new to(e[n].attr,i[n]);r.isValid?i[n]=r:_.warn(`Ignoring invalid Playlist Delta Update DATERANGE tag: "${JSON.stringify(e[n].attr)}"`)}),i}function Gc(s,e,t){if(s&&e){let i=0;for(let n=0,r=s.length;n<=r;n++){const a=s[n],o=e[n+i];a&&o&&a.index===o.index&&a.fragment.sn===o.fragment.sn?t(a,o):i--}}}function Vc(s,e,t){const i=e.skippedSegments,n=Math.max(s.startSN,e.startSN)-e.startSN,r=(s.fragmentHint?1:0)+(i?e.endSN:Math.min(s.endSN,e.endSN))-e.startSN,a=e.startSN-s.startSN,o=e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments,l=s.fragmentHint?s.fragments.concat(s.fragmentHint):s.fragments;for(let u=n;u<=r;u++){const c=l[a+u];let d=o[u];i&&!d&&u<i&&(d=e.fragments[u]=c),c&&d&&t(c,d,u,o)}}function xo(s,e){const t=e.startSN+e.skippedSegments-s.startSN,i=s.fragments;t<0||t>=i.length||gn(e,i[t].start)}function gn(s,e){if(e){const t=s.fragments;for(let i=s.skippedSegments;i<t.length;i++)t[i].start+=e;s.fragmentHint&&(s.fragmentHint.start+=e)}}function Kc(s,e=1/0){let t=1e3*s.targetduration;if(s.updated){const i=s.fragments;if(i.length&&t*4>e){const r=i[i.length-1].duration*1e3;r<t&&(t=r)}}else t/=2;return Math.round(t)}function Hc(s,e,t){if(!(s!=null&&s.details))return null;const i=s.details;let n=i.fragments[e-i.startSN];return n||(n=i.fragmentHint,n&&n.sn===e)?n:e<i.startSN&&t&&t.sn===e?t:null}function Mr(s,e,t){var i;return s!=null&&s.details?Eo((i=s.details)==null?void 0:i.partList,e,t):null}function Eo(s,e,t){if(s)for(let i=s.length;i--;){const n=s[i];if(n.index===t&&n.fragment.sn===e)return n}return null}function Ao(s){s.forEach((e,t)=>{const{details:i}=e;i!=null&&i.fragments&&i.fragments.forEach(n=>{n.level=t})})}function rs(s){switch(s.details){case O.FRAG_LOAD_TIMEOUT:case O.KEY_LOAD_TIMEOUT:case O.LEVEL_LOAD_TIMEOUT:case O.MANIFEST_LOAD_TIMEOUT:return!0}return!1}function Nr(s,e){const t=rs(e);return s.default[`${t?"timeout":"error"}Retry`]}function kn(s,e){const t=s.backoff==="linear"?1:Math.pow(2,e);return Math.min(t*s.retryDelayMs,s.maxRetryDelayMs)}function Br(s){return Re(Re({},s),{errorRetry:null,timeoutRetry:null})}function as(s,e,t,i){if(!s)return!1;const n=i?.code,r=e<s.maxNumRetry&&(qc(n)||!!t);return s.shouldRetry?s.shouldRetry(s,e,t,i,r):r}function qc(s){return s===0&&navigator.onLine===!1||!!s&&(s<400||s>499)}const bo={search:function(s,e){let t=0,i=s.length-1,n=null,r=null;for(;t<=i;){n=(t+i)/2|0,r=s[n];const a=e(r);if(a>0)t=n+1;else if(a<0)i=n-1;else return r}return null}};function Wc(s,e,t){if(e===null||!Array.isArray(s)||!s.length||!J(e))return null;const i=s[0].programDateTime;if(e<(i||0))return null;const n=s[s.length-1].endProgramDateTime;if(e>=(n||0))return null;t=t||0;for(let r=0;r<s.length;++r){const a=s[r];if(zc(e,t,a))return a}return null}function os(s,e,t=0,i=0,n=.005){let r=null;if(s){r=e[s.sn-e[0].sn+1]||null;const o=s.endDTS-t;o>0&&o<15e-7&&(t+=15e-7)}else t===0&&e[0].start===0&&(r=e[0]);if(r&&((!s||s.level===r.level)&&mn(t,i,r)===0||Yc(r,s,Math.min(n,i))))return r;const a=bo.search(e,mn.bind(null,t,i));return a&&(a!==s||!r)?a:r}function Yc(s,e,t){if(e&&e.start===0&&e.level<s.level&&(e.endPTS||0)>0){const i=e.tagList.reduce((n,r)=>(r[0]==="INF"&&(n+=parseFloat(r[1])),n),t);return s.start<=i}return!1}function mn(s=0,e=0,t){if(t.start<=s&&t.start+t.duration>s)return 0;const i=Math.min(e,t.duration+(t.deltaPTS?t.deltaPTS:0));return t.start+t.duration-i<=s?1:t.start-i>s&&t.start?-1:0}function zc(s,e,t){const i=Math.min(e,t.duration+(t.deltaPTS?t.deltaPTS:0))*1e3;return(t.endProgramDateTime||0)-i>s}function jc(s,e){return bo.search(s,t=>t.cc<e?1:t.cc>e?-1:0)}var De={DoNothing:0,SendAlternateToPenaltyBox:2,RemoveAlternatePermanently:3,RetryRequest:5},Ve={None:0,MoveAllAlternatesMatchingHost:1,MoveAllAlternatesMatchingHDCP:2};class Xc{constructor(e){this.hls=void 0,this.playlistError=0,this.penalizedRenditions={},this.log=void 0,this.warn=void 0,this.error=void 0,this.hls=e,this.log=_.log.bind(_,"[info]:"),this.warn=_.warn.bind(_,"[warning]:"),this.error=_.error.bind(_,"[error]:"),this.registerListeners()}registerListeners(){const e=this.hls;e.on(v.ERROR,this.onError,this),e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.LEVEL_UPDATED,this.onLevelUpdated,this)}unregisterListeners(){const e=this.hls;e&&(e.off(v.ERROR,this.onError,this),e.off(v.ERROR,this.onErrorOut,this),e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.LEVEL_UPDATED,this.onLevelUpdated,this))}destroy(){this.unregisterListeners(),this.hls=null,this.penalizedRenditions={}}startLoad(e){}stopLoad(){this.playlistError=0}getVariantLevelIndex(e){return e?.type===ne.MAIN?e.level:this.hls.loadLevel}onManifestLoading(){this.playlistError=0,this.penalizedRenditions={}}onLevelUpdated(){this.playlistError=0}onError(e,t){var i,n;if(t.fatal)return;const r=this.hls,a=t.context;switch(t.details){case O.FRAG_LOAD_ERROR:case O.FRAG_LOAD_TIMEOUT:case O.KEY_LOAD_ERROR:case O.KEY_LOAD_TIMEOUT:t.errorAction=this.getFragRetryOrSwitchAction(t);return;case O.FRAG_PARSING_ERROR:if((i=t.frag)!=null&&i.gap){t.errorAction={action:De.DoNothing,flags:Ve.None};return}case O.FRAG_GAP:case O.FRAG_DECRYPT_ERROR:{t.errorAction=this.getFragRetryOrSwitchAction(t),t.errorAction.action=De.SendAlternateToPenaltyBox;return}case O.LEVEL_EMPTY_ERROR:case O.LEVEL_PARSING_ERROR:{var o,l;const u=t.parent===ne.MAIN?t.level:r.loadLevel;t.details===O.LEVEL_EMPTY_ERROR&&((o=t.context)!=null&&(l=o.levelDetails)!=null&&l.live)?t.errorAction=this.getPlaylistRetryOrSwitchAction(t,u):(t.levelRetry=!1,t.errorAction=this.getLevelSwitchAction(t,u))}return;case O.LEVEL_LOAD_ERROR:case O.LEVEL_LOAD_TIMEOUT:typeof a?.level=="number"&&(t.errorAction=this.getPlaylistRetryOrSwitchAction(t,a.level));return;case O.AUDIO_TRACK_LOAD_ERROR:case O.AUDIO_TRACK_LOAD_TIMEOUT:case O.SUBTITLE_LOAD_ERROR:case O.SUBTITLE_TRACK_LOAD_TIMEOUT:if(a){const u=r.levels[r.loadLevel];if(u&&(a.type===ce.AUDIO_TRACK&&u.hasAudioGroup(a.groupId)||a.type===ce.SUBTITLE_TRACK&&u.hasSubtitleGroup(a.groupId))){t.errorAction=this.getPlaylistRetryOrSwitchAction(t,r.loadLevel),t.errorAction.action=De.SendAlternateToPenaltyBox,t.errorAction.flags=Ve.MoveAllAlternatesMatchingHost;return}}return;case O.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED:{const u=r.levels[r.loadLevel],c=u?.attrs["HDCP-LEVEL"];c?t.errorAction={action:De.SendAlternateToPenaltyBox,flags:Ve.MoveAllAlternatesMatchingHDCP,hdcpLevel:c}:this.keySystemError(t)}return;case O.BUFFER_ADD_CODEC_ERROR:case O.REMUX_ALLOC_ERROR:case O.BUFFER_APPEND_ERROR:t.errorAction=this.getLevelSwitchAction(t,(n=t.level)!=null?n:r.loadLevel);return;case O.INTERNAL_EXCEPTION:case O.BUFFER_APPENDING_ERROR:case O.BUFFER_FULL_ERROR:case O.LEVEL_SWITCH_ERROR:case O.BUFFER_STALLED_ERROR:case O.BUFFER_SEEK_OVER_HOLE:case O.BUFFER_NUDGE_ON_STALL:t.errorAction={action:De.DoNothing,flags:Ve.None};return}t.type===re.KEY_SYSTEM_ERROR&&this.keySystemError(t)}keySystemError(e){const t=this.getVariantLevelIndex(e.frag);e.levelRetry=!1,e.errorAction=this.getLevelSwitchAction(e,t)}getPlaylistRetryOrSwitchAction(e,t){const i=this.hls,n=Nr(i.config.playlistLoadPolicy,e),r=this.playlistError++;if(as(n,r,rs(e),e.response))return{action:De.RetryRequest,flags:Ve.None,retryConfig:n,retryCount:r};const o=this.getLevelSwitchAction(e,t);return n&&(o.retryConfig=n,o.retryCount=r),o}getFragRetryOrSwitchAction(e){const t=this.hls,i=this.getVariantLevelIndex(e.frag),n=t.levels[i],{fragLoadPolicy:r,keyLoadPolicy:a}=t.config,o=Nr(e.details.startsWith("key")?a:r,e),l=t.levels.reduce((c,d)=>c+d.fragmentError,0);if(n&&(e.details!==O.FRAG_GAP&&n.fragmentError++,as(o,l,rs(e),e.response)))return{action:De.RetryRequest,flags:Ve.None,retryConfig:o,retryCount:l};const u=this.getLevelSwitchAction(e,i);return o&&(u.retryConfig=o,u.retryCount=l),u}getLevelSwitchAction(e,t){const i=this.hls;t==null&&(t=i.loadLevel);const n=this.hls.levels[t];if(n){var r,a;const u=e.details;n.loadError++,u===O.BUFFER_APPEND_ERROR&&n.fragmentError++;let c=-1;const{levels:d,loadLevel:f,minAutoLevel:h,maxAutoLevel:g}=i;i.autoLevelEnabled||(i.loadLevel=-1);const p=(r=e.frag)==null?void 0:r.type,y=(p===ne.AUDIO&&u===O.FRAG_PARSING_ERROR||e.sourceBufferName==="audio"&&(u===O.BUFFER_ADD_CODEC_ERROR||u===O.BUFFER_APPEND_ERROR))&&d.some(({audioCodec:P})=>n.audioCodec!==P),T=e.sourceBufferName==="video"&&(u===O.BUFFER_ADD_CODEC_ERROR||u===O.BUFFER_APPEND_ERROR)&&d.some(({codecSet:P,audioCodec:R})=>n.codecSet!==P&&n.audioCodec===R),{type:I,groupId:E}=(a=e.context)!=null?a:{};for(let P=d.length;P--;){const R=(P+f)%d.length;if(R!==f&&R>=h&&R<=g&&d[R].loadError===0){var o,l;const w=d[R];if(u===O.FRAG_GAP&&p===ne.MAIN&&e.frag){const b=d[R].details;if(b){const x=os(e.frag,b.fragments,e.frag.start);if(x!=null&&x.gap)continue}}else{if(I===ce.AUDIO_TRACK&&w.hasAudioGroup(E)||I===ce.SUBTITLE_TRACK&&w.hasSubtitleGroup(E))continue;if(p===ne.AUDIO&&(o=n.audioGroups)!=null&&o.some(b=>w.hasAudioGroup(b))||p===ne.SUBTITLE&&(l=n.subtitleGroups)!=null&&l.some(b=>w.hasSubtitleGroup(b))||y&&n.audioCodec===w.audioCodec||!y&&n.audioCodec!==w.audioCodec||T&&n.codecSet===w.codecSet)continue}c=R;break}}if(c>-1&&i.loadLevel!==c)return e.levelRetry=!0,this.playlistError=0,{action:De.SendAlternateToPenaltyBox,flags:Ve.None,nextAutoLevel:c}}return{action:De.SendAlternateToPenaltyBox,flags:Ve.MoveAllAlternatesMatchingHost}}onErrorOut(e,t){var i;switch((i=t.errorAction)==null?void 0:i.action){case De.DoNothing:break;case De.SendAlternateToPenaltyBox:this.sendAlternateToPenaltyBox(t),!t.errorAction.resolved&&t.details!==O.FRAG_GAP?t.fatal=!0:/MediaSource readyState: ended/.test(t.error.message)&&(this.warn(`MediaSource ended after "${t.sourceBufferName}" sourceBuffer append error. Attempting to recover from media error.`),this.hls.recoverMediaError());break}if(t.fatal){this.hls.stopLoad();return}}sendAlternateToPenaltyBox(e){const t=this.hls,i=e.errorAction;if(!i)return;const{flags:n,hdcpLevel:r,nextAutoLevel:a}=i;switch(n){case Ve.None:this.switchLevel(e,a);break;case Ve.MoveAllAlternatesMatchingHDCP:r&&(t.maxHdcpLevel=hn[hn.indexOf(r)-1],i.resolved=!0),this.warn(`Restricting playback to HDCP-LEVEL of "${t.maxHdcpLevel}" or lower`);break}i.resolved||this.switchLevel(e,a)}switchLevel(e,t){t!==void 0&&e.errorAction&&(this.warn(`switching to level ${t} after ${e.details}`),this.hls.nextAutoLevel=t,e.errorAction.resolved=!0,this.hls.nextLoadLevel=this.hls.nextAutoLevel)}}class _n{constructor(e,t){this.hls=void 0,this.timer=-1,this.requestScheduled=-1,this.canLoad=!1,this.log=void 0,this.warn=void 0,this.log=_.log.bind(_,`${t}:`),this.warn=_.warn.bind(_,`${t}:`),this.hls=e}destroy(){this.clearTimer(),this.hls=this.log=this.warn=null}clearTimer(){this.timer!==-1&&(self.clearTimeout(this.timer),this.timer=-1)}startLoad(){this.canLoad=!0,this.requestScheduled=-1,this.loadPlaylist()}stopLoad(){this.canLoad=!1,this.clearTimer()}switchParams(e,t,i){const n=t?.renditionReports;if(n){let r=-1;for(let a=0;a<n.length;a++){const o=n[a];let l;try{l=new self.URL(o.URI,t.url).href}catch(u){_.warn(`Could not construct new URL for Rendition Report: ${u}`),l=o.URI||""}if(l===e){r=a;break}else l===e.substring(0,l.length)&&(r=a)}if(r!==-1){const a=n[r],o=parseInt(a["LAST-MSN"])||t?.lastPartSn;let l=parseInt(a["LAST-PART"])||t?.lastPartIndex;if(this.hls.config.lowLatencyMode){const c=Math.min(t.age-t.partTarget,t.targetduration);l>=0&&c>t.partTarget&&(l+=1)}const u=i&&Pr(i);return new Fr(o,l>=0?l:void 0,u)}}}loadPlaylist(e){this.requestScheduled===-1&&(this.requestScheduled=self.performance.now())}shouldLoadPlaylist(e){return this.canLoad&&!!e&&!!e.url&&(!e.details||e.details.live)}shouldReloadPlaylist(e){return this.timer===-1&&this.requestScheduled===-1&&this.shouldLoadPlaylist(e)}playlistLoaded(e,t,i){const{details:n,stats:r}=t,a=self.performance.now(),o=r.loading.first?Math.max(0,a-r.loading.first):0;if(n.advancedDateTime=Date.now()-o,n.live||i!=null&&i.live){if(n.reloaded(i),i&&this.log(`live playlist ${e} ${n.advanced?"REFRESHED "+n.lastPartSn+"-"+n.lastPartIndex:n.updated?"UPDATED":"MISSED"}`),i&&n.fragments.length>0&&Uc(i,n),!this.canLoad||!n.live)return;let l,u,c;if(n.canBlockReload&&n.endSN&&n.advanced){const m=this.hls.config.lowLatencyMode,y=n.lastPartSn,S=n.endSN,T=n.lastPartIndex,I=T!==-1,E=y===S,P=m?0:T;I?(u=E?S+1:y,c=E?P:T+1):u=S+1;const R=n.age,w=R+n.ageHeader;let b=Math.min(w-n.partTarget,n.targetduration*1.5);if(b>0){if(i&&b>i.tuneInGoal)this.warn(`CDN Tune-in goal increased from: ${i.tuneInGoal} to: ${b} with playlist age: ${n.age}`),b=0;else{const x=Math.floor(b/n.targetduration);if(u+=x,c!==void 0){const D=Math.round(b%n.targetduration/n.partTarget);c+=D}this.log(`CDN Tune-in age: ${n.ageHeader}s last advanced ${R.toFixed(2)}s goal: ${b} skip sn ${x} to part ${c}`)}n.tuneInGoal=b}if(l=this.getDeliveryDirectives(n,t.deliveryDirectives,u,c),m||!E){this.loadPlaylist(l);return}}else(n.canBlockReload||n.canSkipUntil)&&(l=this.getDeliveryDirectives(n,t.deliveryDirectives,u,c));const d=this.hls.mainForwardBufferInfo,f=d?d.end-d.len:0,h=(n.edge-f)*1e3,g=Kc(n,h);n.updated&&a>this.requestScheduled+g&&(this.requestScheduled=r.loading.start),u!==void 0&&n.canBlockReload?this.requestScheduled=r.loading.first+g-(n.partTarget*1e3||1e3):this.requestScheduled===-1||this.requestScheduled+g<a?this.requestScheduled=a:this.requestScheduled-a<=0&&(this.requestScheduled+=g);let p=this.requestScheduled-a;p=Math.max(0,p),this.log(`reload live playlist ${e} in ${Math.round(p)} ms`),this.timer=self.setTimeout(()=>this.loadPlaylist(l),p)}else this.clearTimer()}getDeliveryDirectives(e,t,i,n){let r=Pr(e);return t!=null&&t.skip&&e.deltaUpdateFailed&&(i=t.msn,n=t.part,r=Wi.No),new Fr(i,n,r)}checkRetry(e){const t=e.details,i=rs(e),n=e.errorAction,{action:r,retryCount:a=0,retryConfig:o}=n||{},l=!!n&&!!o&&(r===De.RetryRequest||!n.resolved&&r===De.SendAlternateToPenaltyBox);if(l){var u;if(this.requestScheduled=-1,a>=o.maxNumRetry)return!1;if(i&&(u=e.context)!=null&&u.deliveryDirectives)this.warn(`Retrying playlist loading ${a+1}/${o.maxNumRetry} after "${t}" without delivery-directives`),this.loadPlaylist();else{const c=kn(o,a);this.timer=self.setTimeout(()=>this.loadPlaylist(),c),this.warn(`Retrying playlist loading ${a+1}/${o.maxNumRetry} after "${t}" in ${c}ms`)}e.levelRetry=!0,n.resolved=!0}return l}}class Ht{constructor(e,t=0,i=0){this.halfLife=void 0,this.alpha_=void 0,this.estimate_=void 0,this.totalWeight_=void 0,this.halfLife=e,this.alpha_=e?Math.exp(Math.log(.5)/e):0,this.estimate_=t,this.totalWeight_=i}sample(e,t){const i=Math.pow(this.alpha_,e);this.estimate_=t*(1-i)+i*this.estimate_,this.totalWeight_+=e}getTotalWeight(){return this.totalWeight_}getEstimate(){if(this.alpha_){const e=1-Math.pow(this.alpha_,this.totalWeight_);if(e)return this.estimate_/e}return this.estimate_}}class Qc{constructor(e,t,i,n=100){this.defaultEstimate_=void 0,this.minWeight_=void 0,this.minDelayMs_=void 0,this.slow_=void 0,this.fast_=void 0,this.defaultTTFB_=void 0,this.ttfb_=void 0,this.defaultEstimate_=i,this.minWeight_=.001,this.minDelayMs_=50,this.slow_=new Ht(e),this.fast_=new Ht(t),this.defaultTTFB_=n,this.ttfb_=new Ht(e)}update(e,t){const{slow_:i,fast_:n,ttfb_:r}=this;i.halfLife!==e&&(this.slow_=new Ht(e,i.getEstimate(),i.getTotalWeight())),n.halfLife!==t&&(this.fast_=new Ht(t,n.getEstimate(),n.getTotalWeight())),r.halfLife!==e&&(this.ttfb_=new Ht(e,r.getEstimate(),r.getTotalWeight()))}sample(e,t){e=Math.max(e,this.minDelayMs_);const i=8*t,n=e/1e3,r=i/n;this.fast_.sample(n,r),this.slow_.sample(n,r)}sampleTTFB(e){const t=e/1e3,i=Math.sqrt(2)*Math.exp(-Math.pow(t,2)/2);this.ttfb_.sample(i,Math.max(e,5))}canEstimate(){return this.fast_.getTotalWeight()>=this.minWeight_}getEstimate(){return this.canEstimate()?Math.min(this.fast_.getEstimate(),this.slow_.getEstimate()):this.defaultEstimate_}getEstimateTTFB(){return this.ttfb_.getTotalWeight()>=this.minWeight_?this.ttfb_.getEstimate():this.defaultTTFB_}destroy(){}}const Lo={supported:!0,configurations:[],decodingInfoResults:[{supported:!0,powerEfficient:!0,smooth:!0}]},Ur={};function Zc(s,e,t,i,n,r){const a=s.audioCodec?s.audioGroups:null,o=r?.audioCodec,l=r?.channels,u=l?parseInt(l):o?1/0:2;let c=null;if(a!=null&&a.length)try{a.length===1&&a[0]?c=e.groups[a[0]].channels:c=a.reduce((d,f)=>{if(f){const h=e.groups[f];if(!h)throw new Error(`Audio track group ${f} not found`);Object.keys(h.channels).forEach(g=>{d[g]=(d[g]||0)+h.channels[g]})}return d},{2:0})}catch{return!0}return s.videoCodec!==void 0&&(s.width>1920&&s.height>1088||s.height>1920&&s.width>1088||s.frameRate>Math.max(i,30)||s.videoRange!=="SDR"&&s.videoRange!==t||s.bitrate>Math.max(n,8e6))||!!c&&J(u)&&Object.keys(c).some(d=>parseInt(d)>u)}function Jc(s,e,t){const i=s.videoCodec,n=s.audioCodec;if(!i||!n||!t)return Promise.resolve(Lo);const r={width:s.width,height:s.height,bitrate:Math.ceil(Math.max(s.bitrate*.9,s.averageBitrate)),framerate:s.frameRate||30},a=s.videoRange;a!=="SDR"&&(r.transferFunction=a.toLowerCase());const o=i.split(",").map(l=>({type:"media-source",video:Re(Re({},r),{},{contentType:gi(l,"video")})}));return n&&s.audioGroups&&s.audioGroups.forEach(l=>{var u;l&&((u=e.groups[l])==null||u.tracks.forEach(c=>{if(c.groupId===l){const d=c.channels||"",f=parseFloat(d);J(f)&&f>2&&o.push.apply(o,n.split(",").map(h=>({type:"media-source",audio:{contentType:gi(h,"audio"),channels:""+f}})))}}))}),Promise.all(o.map(l=>{const u=ed(l);return Ur[u]||(Ur[u]=t.decodingInfo(l))})).then(l=>({supported:!l.some(u=>!u.supported),configurations:o,decodingInfoResults:l})).catch(l=>({supported:!1,configurations:o,decodingInfoResults:[],error:l}))}function ed(s){const{audio:e,video:t}=s,i=t||e;if(i){const n=i.contentType.split('"')[1];if(t)return`r${t.height}x${t.width}f${Math.ceil(t.framerate)}${t.transferFunction||"sd"}_${n}_${Math.ceil(t.bitrate/1e5)}`;if(e)return`c${e.channels}${e.spatialRendering?"s":"n"}_${n}`}return""}function td(){if(typeof matchMedia=="function"){const s=matchMedia("(dynamic-range: high)"),e=matchMedia("bad query");if(s.media!==e.media)return s.matches===!0}return!1}function id(s,e){let t=!1,i=[];return s&&(t=s!=="SDR",i=[s]),e&&(i=e.allowedVideoRanges||ns.slice(0),t=e.preferHDR!==void 0?e.preferHDR:td(),t?i=i.filter(n=>n!=="SDR"):i=["SDR"]),{preferHDR:t,allowedVideoRanges:i}}function sd(s,e,t,i,n){const r=Object.keys(s),a=i?.channels,o=i?.audioCodec,l=a&&parseInt(a)===2;let u=!0,c=!1,d=1/0,f=1/0,h=1/0,g=0,p=[];const{preferHDR:m,allowedVideoRanges:y}=id(e,n);for(let E=r.length;E--;){const P=s[r[E]];u=P.channels[2]>0,d=Math.min(d,P.minHeight),f=Math.min(f,P.minFramerate),h=Math.min(h,P.minBitrate);const R=y.filter(w=>P.videoRanges[w]>0);R.length>0&&(c=!0,p=R)}d=J(d)?d:0,f=J(f)?f:0;const S=Math.max(1080,d),T=Math.max(30,f);return h=J(h)?h:t,t=Math.max(h,t),c||(e=void 0,p=[]),{codecSet:r.reduce((E,P)=>{const R=s[P];if(P===E)return E;if(R.minBitrate>t)return ot(P,`min bitrate of ${R.minBitrate} > current estimate of ${t}`),E;if(!R.hasDefaultAudio)return ot(P,"no renditions with default or auto-select sound found"),E;if(o&&P.indexOf(o.substring(0,4))%5!==0)return ot(P,`audio codec preference "${o}" not found`),E;if(a&&!l){if(!R.channels[a])return ot(P,`no renditions with ${a} channel sound found (channels options: ${Object.keys(R.channels)})`),E}else if((!o||l)&&u&&R.channels[2]===0)return ot(P,"no renditions with stereo sound found"),E;return R.minHeight>S?(ot(P,`min resolution of ${R.minHeight} > maximum of ${S}`),E):R.minFramerate>T?(ot(P,`min framerate of ${R.minFramerate} > maximum of ${T}`),E):p.some(w=>R.videoRanges[w]>0)?R.maxScore<g?(ot(P,`max score of ${R.maxScore} < selected max of ${g}`),E):E&&(is(P)>=is(E)||R.fragmentError>s[E].fragmentError)?E:(g=R.maxScore,P):(ot(P,`no variants with VIDEO-RANGE of ${JSON.stringify(p)} found`),E)},void 0),videoRanges:p,preferHDR:m,minFramerate:f,minBitrate:h}}function ot(s,e){_.log(`[abr] start candidates with "${s}" ignored because ${e}`)}function nd(s){return s.reduce((e,t)=>{let i=e.groups[t.groupId];i||(i=e.groups[t.groupId]={tracks:[],channels:{2:0},hasDefault:!1,hasAutoSelect:!1}),i.tracks.push(t);const n=t.channels||"2";return i.channels[n]=(i.channels[n]||0)+1,i.hasDefault=i.hasDefault||t.default,i.hasAutoSelect=i.hasAutoSelect||t.autoselect,i.hasDefault&&(e.hasDefaultAudio=!0),i.hasAutoSelect&&(e.hasAutoSelectAudio=!0),e},{hasDefaultAudio:!1,hasAutoSelectAudio:!1,groups:{}})}function rd(s,e,t,i){return s.slice(t,i+1).reduce((n,r)=>{if(!r.codecSet)return n;const a=r.audioGroups;let o=n[r.codecSet];o||(n[r.codecSet]=o={minBitrate:1/0,minHeight:1/0,minFramerate:1/0,maxScore:0,videoRanges:{SDR:0},channels:{2:0},hasDefaultAudio:!a,fragmentError:0}),o.minBitrate=Math.min(o.minBitrate,r.bitrate);const l=Math.min(r.height,r.width);return o.minHeight=Math.min(o.minHeight,l),o.minFramerate=Math.min(o.minFramerate,r.frameRate),o.maxScore=Math.max(o.maxScore,r.score),o.fragmentError+=r.fragmentError,o.videoRanges[r.videoRange]=(o.videoRanges[r.videoRange]||0)+1,a&&a.forEach(u=>{if(!u)return;const c=e.groups[u];c&&(o.hasDefaultAudio=o.hasDefaultAudio||e.hasDefaultAudio?c.hasDefault:c.hasAutoSelect||!e.hasDefaultAudio&&!e.hasAutoSelectAudio,Object.keys(c.channels).forEach(d=>{o.channels[d]=(o.channels[d]||0)+c.channels[d]}))}),n},{})}function st(s,e,t){if("attrs"in s){const i=e.indexOf(s);if(i!==-1)return i}for(let i=0;i<e.length;i++){const n=e[i];if(Ct(s,n,t))return i}return-1}function Ct(s,e,t){const{groupId:i,name:n,lang:r,assocLang:a,default:o}=s,l=s.forced;return(i===void 0||e.groupId===i)&&(n===void 0||e.name===n)&&(r===void 0||e.lang===r)&&(r===void 0||e.assocLang===a)&&(o===void 0||e.default===o)&&(l===void 0||e.forced===l)&&(!("characteristics"in s)||ad(s.characteristics||"",e.characteristics))&&(t===void 0||t(s,e))}function ad(s,e=""){const t=s.split(","),i=e.split(",");return t.length===i.length&&!t.some(n=>i.indexOf(n)===-1)}function Et(s,e){const{audioCodec:t,channels:i}=s;return(t===void 0||(e.audioCodec||"").substring(0,4)===t.substring(0,4))&&(i===void 0||i===(e.channels||"2"))}function od(s,e,t,i,n){const r=e[i],o=e.reduce((f,h,g)=>{const p=h.uri;return(f[p]||(f[p]=[])).push(g),f},{})[r.uri];o.length>1&&(i=Math.max.apply(Math,o));const l=r.videoRange,u=r.frameRate,c=r.codecSet.substring(0,4),d=$r(e,i,f=>{if(f.videoRange!==l||f.frameRate!==u||f.codecSet.substring(0,4)!==c)return!1;const h=f.audioGroups,g=t.filter(p=>!h||h.indexOf(p.groupId)!==-1);return st(s,g,n)>-1});return d>-1?d:$r(e,i,f=>{const h=f.audioGroups,g=t.filter(p=>!h||h.indexOf(p.groupId)!==-1);return st(s,g,n)>-1})}function $r(s,e,t){for(let i=e;i>-1;i--)if(t(s[i]))return i;for(let i=e+1;i<s.length;i++)if(t(s[i]))return i;return-1}class ld{constructor(e){this.hls=void 0,this.lastLevelLoadSec=0,this.lastLoadedFragLevel=-1,this.firstSelection=-1,this._nextAutoLevel=-1,this.nextAutoLevelKey="",this.audioTracksByGroup=null,this.codecTiers=null,this.timer=-1,this.fragCurrent=null,this.partCurrent=null,this.bitrateTestDelay=0,this.bwEstimator=void 0,this._abandonRulesCheck=()=>{const{fragCurrent:t,partCurrent:i,hls:n}=this,{autoLevelEnabled:r,media:a}=n;if(!t||!a)return;const o=performance.now(),l=i?i.stats:t.stats,u=i?i.duration:t.duration,c=o-l.loading.start,d=n.minAutoLevel;if(l.aborted||l.loaded&&l.loaded===l.total||t.level<=d){this.clearTimer(),this._nextAutoLevel=-1;return}if(!r||a.paused||!a.playbackRate||!a.readyState)return;const f=n.mainForwardBufferInfo;if(f===null)return;const h=this.bwEstimator.getEstimateTTFB(),g=Math.abs(a.playbackRate);if(c<=Math.max(h,1e3*(u/(g*2))))return;const p=f.len/g,m=l.loading.first?l.loading.first-l.loading.start:-1,y=l.loaded&&m>-1,S=this.getBwEstimate(),T=n.levels,I=T[t.level],E=l.total||Math.max(l.loaded,Math.round(u*I.averageBitrate/8));let P=y?c-m:c;P<1&&y&&(P=Math.min(c,l.loaded*8/S));const R=y?l.loaded*1e3/P:0,w=R?(E-l.loaded)/R:E*8/S+h/1e3;if(w<=p)return;const b=R?R*8:S;let x=Number.POSITIVE_INFINITY,D;for(D=t.level-1;D>d;D--){const M=T[D].maxBitrate;if(x=this.getTimeToLoadFrag(h/1e3,b,u*M,!T[D].details),x<p)break}if(x>=w||x>u*10)return;n.nextLoadLevel=n.nextAutoLevel=D,y?this.bwEstimator.sample(c-Math.min(h,m),l.loaded):this.bwEstimator.sampleTTFB(c);const C=T[D].maxBitrate;this.getBwEstimate()*this.hls.config.abrBandWidthUpFactor>C&&this.resetEstimator(C),this.clearTimer(),_.warn(`[abr] Fragment ${t.sn}${i?" part "+i.index:""} of level ${t.level} is loading too slowly;
      Time to underbuffer: ${p.toFixed(3)} s
      Estimated load time for current fragment: ${w.toFixed(3)} s
      Estimated load time for down switch fragment: ${x.toFixed(3)} s
      TTFB estimate: ${m|0} ms
      Current BW estimate: ${J(S)?S|0:"Unknown"} bps
      New BW estimate: ${this.getBwEstimate()|0} bps
      Switching to level ${D} @ ${C|0} bps`),n.trigger(v.FRAG_LOAD_EMERGENCY_ABORTED,{frag:t,part:i,stats:l})},this.hls=e,this.bwEstimator=this.initEstimator(),this.registerListeners()}resetEstimator(e){e&&(_.log(`setting initial bwe to ${e}`),this.hls.config.abrEwmaDefaultEstimate=e),this.firstSelection=-1,this.bwEstimator=this.initEstimator()}initEstimator(){const e=this.hls.config;return new Qc(e.abrEwmaSlowVoD,e.abrEwmaFastVoD,e.abrEwmaDefaultEstimate)}registerListeners(){const{hls:e}=this;e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.FRAG_LOADING,this.onFragLoading,this),e.on(v.FRAG_LOADED,this.onFragLoaded,this),e.on(v.FRAG_BUFFERED,this.onFragBuffered,this),e.on(v.LEVEL_SWITCHING,this.onLevelSwitching,this),e.on(v.LEVEL_LOADED,this.onLevelLoaded,this),e.on(v.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(v.MAX_AUTO_LEVEL_UPDATED,this.onMaxAutoLevelUpdated,this),e.on(v.ERROR,this.onError,this)}unregisterListeners(){const{hls:e}=this;e&&(e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.FRAG_LOADING,this.onFragLoading,this),e.off(v.FRAG_LOADED,this.onFragLoaded,this),e.off(v.FRAG_BUFFERED,this.onFragBuffered,this),e.off(v.LEVEL_SWITCHING,this.onLevelSwitching,this),e.off(v.LEVEL_LOADED,this.onLevelLoaded,this),e.off(v.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(v.MAX_AUTO_LEVEL_UPDATED,this.onMaxAutoLevelUpdated,this),e.off(v.ERROR,this.onError,this))}destroy(){this.unregisterListeners(),this.clearTimer(),this.hls=this._abandonRulesCheck=null,this.fragCurrent=this.partCurrent=null}onManifestLoading(e,t){this.lastLoadedFragLevel=-1,this.firstSelection=-1,this.lastLevelLoadSec=0,this.fragCurrent=this.partCurrent=null,this.onLevelsUpdated(),this.clearTimer()}onLevelsUpdated(){this.lastLoadedFragLevel>-1&&this.fragCurrent&&(this.lastLoadedFragLevel=this.fragCurrent.level),this._nextAutoLevel=-1,this.onMaxAutoLevelUpdated(),this.codecTiers=null,this.audioTracksByGroup=null}onMaxAutoLevelUpdated(){this.firstSelection=-1,this.nextAutoLevelKey=""}onFragLoading(e,t){const i=t.frag;if(!this.ignoreFragment(i)){if(!i.bitrateTest){var n;this.fragCurrent=i,this.partCurrent=(n=t.part)!=null?n:null}this.clearTimer(),this.timer=self.setInterval(this._abandonRulesCheck,100)}}onLevelSwitching(e,t){this.clearTimer()}onError(e,t){if(!t.fatal)switch(t.details){case O.BUFFER_ADD_CODEC_ERROR:case O.BUFFER_APPEND_ERROR:this.lastLoadedFragLevel=-1,this.firstSelection=-1;break;case O.FRAG_LOAD_TIMEOUT:{const i=t.frag,{fragCurrent:n,partCurrent:r}=this;if(i&&n&&i.sn===n.sn&&i.level===n.level){const a=performance.now(),o=r?r.stats:i.stats,l=a-o.loading.start,u=o.loading.first?o.loading.first-o.loading.start:-1;if(o.loaded&&u>-1){const d=this.bwEstimator.getEstimateTTFB();this.bwEstimator.sample(l-Math.min(d,u),o.loaded)}else this.bwEstimator.sampleTTFB(l)}break}}}getTimeToLoadFrag(e,t,i,n){const r=e+i/t,a=n?this.lastLevelLoadSec:0;return r+a}onLevelLoaded(e,t){const i=this.hls.config,{loading:n}=t.stats,r=n.end-n.start;J(r)&&(this.lastLevelLoadSec=r/1e3),t.details.live?this.bwEstimator.update(i.abrEwmaSlowLive,i.abrEwmaFastLive):this.bwEstimator.update(i.abrEwmaSlowVoD,i.abrEwmaFastVoD)}onFragLoaded(e,{frag:t,part:i}){const n=i?i.stats:t.stats;if(t.type===ne.MAIN&&this.bwEstimator.sampleTTFB(n.loading.first-n.loading.start),!this.ignoreFragment(t)){if(this.clearTimer(),t.level===this._nextAutoLevel&&(this._nextAutoLevel=-1),this.firstSelection=-1,this.hls.config.abrMaxWithRealBitrate){const r=i?i.duration:t.duration,a=this.hls.levels[t.level],o=(a.loaded?a.loaded.bytes:0)+n.loaded,l=(a.loaded?a.loaded.duration:0)+r;a.loaded={bytes:o,duration:l},a.realBitrate=Math.round(8*o/l)}if(t.bitrateTest){const r={stats:n,frag:t,part:i,id:t.type};this.onFragBuffered(v.FRAG_BUFFERED,r),t.bitrateTest=!1}else this.lastLoadedFragLevel=t.level}}onFragBuffered(e,t){const{frag:i,part:n}=t,r=n!=null&&n.stats.loaded?n.stats:i.stats;if(r.aborted||this.ignoreFragment(i))return;const a=r.parsing.end-r.loading.start-Math.min(r.loading.first-r.loading.start,this.bwEstimator.getEstimateTTFB());this.bwEstimator.sample(a,r.loaded),r.bwEstimate=this.getBwEstimate(),i.bitrateTest?this.bitrateTestDelay=a/1e3:this.bitrateTestDelay=0}ignoreFragment(e){return e.type!==ne.MAIN||e.sn==="initSegment"}clearTimer(){this.timer>-1&&(self.clearInterval(this.timer),this.timer=-1)}get firstAutoLevel(){const{maxAutoLevel:e,minAutoLevel:t}=this.hls,i=this.getBwEstimate(),n=this.hls.config.maxStarvationDelay,r=this.findBestLevel(i,t,e,0,n,1,1);if(r>-1)return r;const a=this.hls.firstLevel,o=Math.min(Math.max(a,t),e);return _.warn(`[abr] Could not find best starting auto level. Defaulting to first in playlist ${a} clamped to ${o}`),o}get forcedAutoLevel(){return this.nextAutoLevelKey?-1:this._nextAutoLevel}get nextAutoLevel(){const e=this.forcedAutoLevel,i=this.bwEstimator.canEstimate(),n=this.lastLoadedFragLevel>-1;if(e!==-1&&(!i||!n||this.nextAutoLevelKey===this.getAutoLevelKey()))return e;const r=i&&n?this.getNextABRAutoLevel():this.firstAutoLevel;if(e!==-1){const a=this.hls.levels;if(a.length>Math.max(e,r)&&a[e].loadError<=a[r].loadError)return e}return this._nextAutoLevel=r,this.nextAutoLevelKey=this.getAutoLevelKey(),r}getAutoLevelKey(){return`${this.getBwEstimate()}_${this.getStarvationDelay().toFixed(2)}`}getNextABRAutoLevel(){const{fragCurrent:e,partCurrent:t,hls:i}=this,{maxAutoLevel:n,config:r,minAutoLevel:a}=i,o=t?t.duration:e?e.duration:0,l=this.getBwEstimate(),u=this.getStarvationDelay();let c=r.abrBandWidthFactor,d=r.abrBandWidthUpFactor;if(u){const m=this.findBestLevel(l,a,n,u,0,c,d);if(m>=0)return m}let f=o?Math.min(o,r.maxStarvationDelay):r.maxStarvationDelay;if(!u){const m=this.bitrateTestDelay;m&&(f=(o?Math.min(o,r.maxLoadingDelay):r.maxLoadingDelay)-m,_.info(`[abr] bitrate test took ${Math.round(1e3*m)}ms, set first fragment max fetchDuration to ${Math.round(1e3*f)} ms`),c=d=1)}const h=this.findBestLevel(l,a,n,u,f,c,d);if(_.info(`[abr] ${u?"rebuffering expected":"buffer is empty"}, optimal quality level ${h}`),h>-1)return h;const g=i.levels[a],p=i.levels[i.loadLevel];return g?.bitrate<p?.bitrate?a:i.loadLevel}getStarvationDelay(){const e=this.hls,t=e.media;if(!t)return 1/0;const i=t&&t.playbackRate!==0?Math.abs(t.playbackRate):1,n=e.mainForwardBufferInfo;return(n?n.len:0)/i}getBwEstimate(){return this.bwEstimator.canEstimate()?this.bwEstimator.getEstimate():this.hls.config.abrEwmaDefaultEstimate}findBestLevel(e,t,i,n,r,a,o){var l;const u=n+r,c=this.lastLoadedFragLevel,d=c===-1?this.hls.firstLevel:c,{fragCurrent:f,partCurrent:h}=this,{levels:g,allAudioTracks:p,loadLevel:m,config:y}=this.hls;if(g.length===1)return 0;const S=g[d],T=!!(S!=null&&(l=S.details)!=null&&l.live),I=m===-1||c===-1;let E,P="SDR",R=S?.frameRate||0;const{audioPreference:w,videoPreference:b}=y,x=this.audioTracksByGroup||(this.audioTracksByGroup=nd(p));if(I){if(this.firstSelection!==-1)return this.firstSelection;const Y=this.codecTiers||(this.codecTiers=rd(g,x,t,i)),K=sd(Y,P,e,w,b),{codecSet:z,videoRanges:V,minFramerate:B,minBitrate:$,preferHDR:k}=K;E=z,P=k?V[V.length-1]:V[0],R=B,e=Math.max(e,$),_.log(`[abr] picked start tier ${JSON.stringify(K)}`)}else E=S?.codecSet,P=S?.videoRange;const D=h?h.duration:f?f.duration:0,C=this.bwEstimator.getEstimateTTFB()/1e3,M=[];for(let Y=i;Y>=t;Y--){var N;const K=g[Y],z=Y>d;if(!K)continue;if(y.useMediaCapabilities&&!K.supportedResult&&!K.supportedPromise){const ee=navigator.mediaCapabilities;typeof ee?.decodingInfo=="function"&&Zc(K,x,P,R,e,w)?(K.supportedPromise=Jc(K,x,ee),K.supportedPromise.then(ie=>{if(!this.hls)return;K.supportedResult=ie;const H=this.hls.levels,j=H.indexOf(K);ie.error?_.warn(`[abr] MediaCapabilities decodingInfo error: "${ie.error}" for level ${j} ${JSON.stringify(ie)}`):ie.supported||(_.warn(`[abr] Unsupported MediaCapabilities decodingInfo result for level ${j} ${JSON.stringify(ie)}`),j>-1&&H.length>1&&(_.log(`[abr] Removing unsupported level ${j}`),this.hls.removeLevel(j)))})):K.supportedResult=Lo}if(E&&K.codecSet!==E||P&&K.videoRange!==P||z&&R>K.frameRate||!z&&R>0&&R<K.frameRate||K.supportedResult&&!((N=K.supportedResult.decodingInfoResults)!=null&&N[0].smooth)){M.push(Y);continue}const V=K.details,B=(h?V?.partTarget:V?.averagetargetduration)||D;let $;z?$=o*e:$=a*e;const k=D&&n>=D*2&&r===0?g[Y].averageBitrate:g[Y].maxBitrate,L=this.getTimeToLoadFrag(C,$,k*B,V===void 0);if($>=k&&(Y===c||K.loadError===0&&K.fragmentError===0)&&(L<=C||!J(L)||T&&!this.bitrateTestDelay||L<u)){const ee=this.forcedAutoLevel;return Y!==m&&(ee===-1||ee!==m)&&(M.length&&_.trace(`[abr] Skipped level(s) ${M.join(",")} of ${i} max with CODECS and VIDEO-RANGE:"${g[M[0]].codecs}" ${g[M[0]].videoRange}; not compatible with "${S.codecs}" ${P}`),_.info(`[abr] switch candidate:${d}->${Y} adjustedbw(${Math.round($)})-bitrate=${Math.round($-k)} ttfb:${C.toFixed(1)} avgDuration:${B.toFixed(1)} maxFetchDuration:${u.toFixed(1)} fetchDuration:${L.toFixed(1)} firstSelection:${I} codecSet:${E} videoRange:${P} hls.loadLevel:${m}`)),I&&(this.firstSelection=Y),Y}}return-1}set nextAutoLevel(e){const{maxAutoLevel:t,minAutoLevel:i}=this.hls,n=Math.min(Math.max(e,i),t);this._nextAutoLevel!==n&&(this.nextAutoLevelKey="",this._nextAutoLevel=n)}}class ud{constructor(){this._boundTick=void 0,this._tickTimer=null,this._tickInterval=null,this._tickCallCount=0,this._boundTick=this.tick.bind(this)}destroy(){this.onHandlerDestroying(),this.onHandlerDestroyed()}onHandlerDestroying(){this.clearNextTick(),this.clearInterval()}onHandlerDestroyed(){}hasInterval(){return!!this._tickInterval}hasNextTick(){return!!this._tickTimer}setInterval(e){return this._tickInterval?!1:(this._tickCallCount=0,this._tickInterval=self.setInterval(this._boundTick,e),!0)}clearInterval(){return this._tickInterval?(self.clearInterval(this._tickInterval),this._tickInterval=null,!0):!1}clearNextTick(){return this._tickTimer?(self.clearTimeout(this._tickTimer),this._tickTimer=null,!0):!1}tick(){this._tickCallCount++,this._tickCallCount===1&&(this.doTick(),this._tickCallCount>1&&this.tickImmediate(),this._tickCallCount=0)}tickImmediate(){this.clearNextTick(),this._tickTimer=self.setTimeout(this._boundTick,0)}doTick(){}}var Le={NOT_LOADED:"NOT_LOADED",APPENDING:"APPENDING",PARTIAL:"PARTIAL",OK:"OK"};class cd{constructor(e){this.activePartLists=Object.create(null),this.endListFragments=Object.create(null),this.fragments=Object.create(null),this.timeRanges=Object.create(null),this.bufferPadding=.2,this.hls=void 0,this.hasGaps=!1,this.hls=e,this._registerListeners()}_registerListeners(){const{hls:e}=this;e.on(v.BUFFER_APPENDED,this.onBufferAppended,this),e.on(v.FRAG_BUFFERED,this.onFragBuffered,this),e.on(v.FRAG_LOADED,this.onFragLoaded,this)}_unregisterListeners(){const{hls:e}=this;e.off(v.BUFFER_APPENDED,this.onBufferAppended,this),e.off(v.FRAG_BUFFERED,this.onFragBuffered,this),e.off(v.FRAG_LOADED,this.onFragLoaded,this)}destroy(){this._unregisterListeners(),this.fragments=this.activePartLists=this.endListFragments=this.timeRanges=null}getAppendedFrag(e,t){const i=this.activePartLists[t];if(i)for(let n=i.length;n--;){const r=i[n];if(!r)break;const a=r.end;if(r.start<=e&&a!==null&&e<=a)return r}return this.getBufferedFrag(e,t)}getBufferedFrag(e,t){const{fragments:i}=this,n=Object.keys(i);for(let r=n.length;r--;){const a=i[n[r]];if(a?.body.type===t&&a.buffered){const o=a.body;if(o.start<=e&&e<=o.end)return o}}return null}detectEvictedFragments(e,t,i,n){this.timeRanges&&(this.timeRanges[e]=t);const r=n?.fragment.sn||-1;Object.keys(this.fragments).forEach(a=>{const o=this.fragments[a];if(!o||r>=o.body.sn)return;if(!o.buffered&&!o.loaded){o.body.type===i&&this.removeFragment(o.body);return}const l=o.range[e];l&&l.time.some(u=>{const c=!this.isTimeBuffered(u.startPTS,u.endPTS,t);return c&&this.removeFragment(o.body),c})})}detectPartialFragments(e){const t=this.timeRanges,{frag:i,part:n}=e;if(!t||i.sn==="initSegment")return;const r=qt(i),a=this.fragments[r];if(!a||a.buffered&&i.gap)return;const o=!i.relurl;Object.keys(t).forEach(l=>{const u=i.elementaryStreams[l];if(!u)return;const c=t[l],d=o||u.partial===!0;a.range[l]=this.getBufferedTimes(i,n,d,c)}),a.loaded=null,Object.keys(a.range).length?(a.buffered=!0,(a.body.endList=i.endList||a.body.endList)&&(this.endListFragments[a.body.type]=a),wi(a)||this.removeParts(i.sn-1,i.type)):this.removeFragment(a.body)}removeParts(e,t){const i=this.activePartLists[t];i&&(this.activePartLists[t]=i.filter(n=>n.fragment.sn>=e))}fragBuffered(e,t){const i=qt(e);let n=this.fragments[i];!n&&t&&(n=this.fragments[i]={body:e,appendedPTS:null,loaded:null,buffered:!1,range:Object.create(null)},e.gap&&(this.hasGaps=!0)),n&&(n.loaded=null,n.buffered=!0)}getBufferedTimes(e,t,i,n){const r={time:[],partial:i},a=e.start,o=e.end,l=e.minEndPTS||o,u=e.maxStartPTS||a;for(let c=0;c<n.length;c++){const d=n.start(c)-this.bufferPadding,f=n.end(c)+this.bufferPadding;if(u>=d&&l<=f){r.time.push({startPTS:Math.max(a,n.start(c)),endPTS:Math.min(o,n.end(c))});break}else if(a<f&&o>d){const h=Math.max(a,n.start(c)),g=Math.min(o,n.end(c));g>h&&(r.partial=!0,r.time.push({startPTS:h,endPTS:g}))}else if(o<=d)break}return r}getPartialFragment(e){let t=null,i,n,r,a=0;const{bufferPadding:o,fragments:l}=this;return Object.keys(l).forEach(u=>{const c=l[u];c&&wi(c)&&(n=c.body.start-o,r=c.body.end+o,e>=n&&e<=r&&(i=Math.min(e-n,r-e),a<=i&&(t=c.body,a=i)))}),t}isEndListAppended(e){const t=this.endListFragments[e];return t!==void 0&&(t.buffered||wi(t))}getState(e){const t=qt(e),i=this.fragments[t];return i?i.buffered?wi(i)?Le.PARTIAL:Le.OK:Le.APPENDING:Le.NOT_LOADED}isTimeBuffered(e,t,i){let n,r;for(let a=0;a<i.length;a++){if(n=i.start(a)-this.bufferPadding,r=i.end(a)+this.bufferPadding,e>=n&&t<=r)return!0;if(t<=n)return!1}return!1}onFragLoaded(e,t){const{frag:i,part:n}=t;if(i.sn==="initSegment"||i.bitrateTest)return;const r=n?null:t,a=qt(i);this.fragments[a]={body:i,appendedPTS:null,loaded:r,buffered:!1,range:Object.create(null)}}onBufferAppended(e,t){const{frag:i,part:n,timeRanges:r}=t;if(i.sn==="initSegment")return;const a=i.type;if(n){let o=this.activePartLists[a];o||(this.activePartLists[a]=o=[]),o.push(n)}this.timeRanges=r,Object.keys(r).forEach(o=>{const l=r[o];this.detectEvictedFragments(o,l,a,n)})}onFragBuffered(e,t){this.detectPartialFragments(t)}hasFragment(e){const t=qt(e);return!!this.fragments[t]}hasParts(e){var t;return!!((t=this.activePartLists[e])!=null&&t.length)}removeFragmentsInRange(e,t,i,n,r){n&&!this.hasGaps||Object.keys(this.fragments).forEach(a=>{const o=this.fragments[a];if(!o)return;const l=o.body;l.type!==i||n&&!l.gap||l.start<t&&l.end>e&&(o.buffered||r)&&this.removeFragment(l)})}removeFragment(e){const t=qt(e);e.stats.loaded=0,e.clearElementaryStreamInfo();const i=this.activePartLists[e.type];if(i){const n=e.sn;this.activePartLists[e.type]=i.filter(r=>r.fragment.sn!==n)}delete this.fragments[t],e.endList&&delete this.endListFragments[e.type]}removeAllFragments(){this.fragments=Object.create(null),this.endListFragments=Object.create(null),this.activePartLists=Object.create(null),this.hasGaps=!1}}function wi(s){var e,t,i;return s.buffered&&(s.body.gap||((e=s.range.video)==null?void 0:e.partial)||((t=s.range.audio)==null?void 0:t.partial)||((i=s.range.audiovideo)==null?void 0:i.partial))}function qt(s){return`${s.type}_${s.level}_${s.sn}`}const dd={length:0,start:()=>0,end:()=>0};class me{static isBuffered(e,t){try{if(e){const i=me.getBuffered(e);for(let n=0;n<i.length;n++)if(t>=i.start(n)&&t<=i.end(n))return!0}}catch{}return!1}static bufferInfo(e,t,i){try{if(e){const n=me.getBuffered(e),r=[];let a;for(a=0;a<n.length;a++)r.push({start:n.start(a),end:n.end(a)});return this.bufferedInfo(r,t,i)}}catch{}return{len:0,start:t,end:t,nextStart:void 0}}static bufferedInfo(e,t,i){t=Math.max(0,t),e.sort(function(u,c){const d=u.start-c.start;return d||c.end-u.end});let n=[];if(i)for(let u=0;u<e.length;u++){const c=n.length;if(c){const d=n[c-1].end;e[u].start-d<i?e[u].end>d&&(n[c-1].end=e[u].end):n.push(e[u])}else n.push(e[u])}else n=e;let r=0,a,o=t,l=t;for(let u=0;u<n.length;u++){const c=n[u].start,d=n[u].end;if(t+i>=c&&t<d)o=c,l=d,r=l-t;else if(t+i<c){a=c;break}}return{len:r,start:o||0,end:l||0,nextStart:a}}static getBuffered(e){try{return e.buffered}catch(t){return _.log("failed to get media.buffered",t),dd}}}class Pn{constructor(e,t,i,n=0,r=-1,a=!1){this.level=void 0,this.sn=void 0,this.part=void 0,this.id=void 0,this.size=void 0,this.partial=void 0,this.transmuxing=ki(),this.buffering={audio:ki(),video:ki(),audiovideo:ki()},this.level=e,this.sn=t,this.id=i,this.size=n,this.part=r,this.partial=a}}function ki(){return{start:0,executeStart:0,executeEnd:0,end:0}}function Yi(s,e){for(let i=0,n=s.length;i<n;i++){var t;if(((t=s[i])==null?void 0:t.cc)===e)return s[i]}return null}function fd(s,e,t){return!!(e&&(t.endCC>t.startCC||s&&s.cc<t.startCC))}function hd(s,e){const t=s.fragments,i=e.fragments;if(!i.length||!t.length){_.log("No fragments to align");return}const n=Yi(t,i[0].cc);if(!n||n&&!n.startPTS){_.log("No frag in previous level to align on");return}return n}function Gr(s,e){if(s){const t=s.start+e;s.start=s.startPTS=t,s.endPTS=t+s.duration}}function Ro(s,e){const t=e.fragments;for(let i=0,n=t.length;i<n;i++)Gr(t[i],s);e.fragmentHint&&Gr(e.fragmentHint,s),e.alignedSliding=!0}function gd(s,e,t){e&&(md(s,t,e),!t.alignedSliding&&e&&ls(t,e),!t.alignedSliding&&e&&!t.skippedSegments&&xo(e,t))}function md(s,e,t){if(fd(s,t,e)){const i=hd(t,e);i&&J(i.start)&&(_.log(`Adjusting PTS using last level due to CC increase within current level ${e.url}`),Ro(i.start,e))}}function ls(s,e){if(!s.hasProgramDateTime||!e.hasProgramDateTime)return;const t=s.fragments,i=e.fragments;if(!t.length||!i.length)return;let n,r;const a=Math.min(e.endCC,s.endCC);e.startCC<a&&s.startCC<a&&(n=Yi(i,a),r=Yi(t,a)),(!n||!r)&&(n=i[Math.floor(i.length/2)],r=Yi(t,n.cc)||t[Math.floor(t.length/2)]);const o=n.programDateTime,l=r.programDateTime;if(!o||!l)return;const u=(l-o)/1e3-(r.start-n.start);Ro(u,s)}const Vr=Math.pow(2,17);class pd{constructor(e){this.config=void 0,this.loader=null,this.partLoadTimeout=-1,this.config=e}destroy(){this.loader&&(this.loader.destroy(),this.loader=null)}abort(){this.loader&&this.loader.abort()}load(e,t){const i=e.url;if(!i)return Promise.reject(new ut({type:re.NETWORK_ERROR,details:O.FRAG_LOAD_ERROR,fatal:!1,frag:e,error:new Error(`Fragment does not have a ${i?"part list":"url"}`),networkDetails:null}));this.abort();const n=this.config,r=n.fLoader,a=n.loader;return new Promise((o,l)=>{if(this.loader&&this.loader.destroy(),e.gap)if(e.tagList.some(h=>h[0]==="GAP")){l(Hr(e));return}else e.gap=!1;const u=this.loader=e.loader=r?new r(n):new a(n),c=Kr(e),d=Br(n.fragLoadPolicy.default),f={loadPolicy:d,timeout:d.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0,highWaterMark:e.sn==="initSegment"?1/0:Vr};e.stats=u.stats,u.load(c,f,{onSuccess:(h,g,p,m)=>{this.resetLoader(e,u);let y=h.data;p.resetIV&&e.decryptdata&&(e.decryptdata.iv=new Uint8Array(y.slice(0,16)),y=y.slice(16)),o({frag:e,part:null,payload:y,networkDetails:m})},onError:(h,g,p,m)=>{this.resetLoader(e,u),l(new ut({type:re.NETWORK_ERROR,details:O.FRAG_LOAD_ERROR,fatal:!1,frag:e,response:Re({url:i,data:void 0},h),error:new Error(`HTTP Error ${h.code} ${h.text}`),networkDetails:p,stats:m}))},onAbort:(h,g,p)=>{this.resetLoader(e,u),l(new ut({type:re.NETWORK_ERROR,details:O.INTERNAL_ABORTED,fatal:!1,frag:e,error:new Error("Aborted"),networkDetails:p,stats:h}))},onTimeout:(h,g,p)=>{this.resetLoader(e,u),l(new ut({type:re.NETWORK_ERROR,details:O.FRAG_LOAD_TIMEOUT,fatal:!1,frag:e,error:new Error(`Timeout after ${f.timeout}ms`),networkDetails:p,stats:h}))},onProgress:(h,g,p,m)=>{t&&t({frag:e,part:null,payload:p,networkDetails:m})}})})}loadPart(e,t,i){this.abort();const n=this.config,r=n.fLoader,a=n.loader;return new Promise((o,l)=>{if(this.loader&&this.loader.destroy(),e.gap||t.gap){l(Hr(e,t));return}const u=this.loader=e.loader=r?new r(n):new a(n),c=Kr(e,t),d=Br(n.fragLoadPolicy.default),f={loadPolicy:d,timeout:d.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0,highWaterMark:Vr};t.stats=u.stats,u.load(c,f,{onSuccess:(h,g,p,m)=>{this.resetLoader(e,u),this.updateStatsFromPart(e,t);const y={frag:e,part:t,payload:h.data,networkDetails:m};i(y),o(y)},onError:(h,g,p,m)=>{this.resetLoader(e,u),l(new ut({type:re.NETWORK_ERROR,details:O.FRAG_LOAD_ERROR,fatal:!1,frag:e,part:t,response:Re({url:c.url,data:void 0},h),error:new Error(`HTTP Error ${h.code} ${h.text}`),networkDetails:p,stats:m}))},onAbort:(h,g,p)=>{e.stats.aborted=t.stats.aborted,this.resetLoader(e,u),l(new ut({type:re.NETWORK_ERROR,details:O.INTERNAL_ABORTED,fatal:!1,frag:e,part:t,error:new Error("Aborted"),networkDetails:p,stats:h}))},onTimeout:(h,g,p)=>{this.resetLoader(e,u),l(new ut({type:re.NETWORK_ERROR,details:O.FRAG_LOAD_TIMEOUT,fatal:!1,frag:e,part:t,error:new Error(`Timeout after ${f.timeout}ms`),networkDetails:p,stats:h}))}})})}updateStatsFromPart(e,t){const i=e.stats,n=t.stats,r=n.total;if(i.loaded+=n.loaded,r){const l=Math.round(e.duration/t.duration),u=Math.min(Math.round(i.loaded/r),l),d=(l-u)*Math.round(i.loaded/u);i.total=i.loaded+d}else i.total=Math.max(i.loaded,i.total);const a=i.loading,o=n.loading;a.start?a.first+=o.first-o.start:(a.start=o.start,a.first=o.first),a.end=o.end}resetLoader(e,t){e.loader=null,this.loader===t&&(self.clearTimeout(this.partLoadTimeout),this.loader=null),t.destroy()}}function Kr(s,e=null){const t=e||s,i={frag:s,part:e,responseType:"arraybuffer",url:t.url,headers:{},rangeStart:0,rangeEnd:0},n=t.byteRangeStartOffset,r=t.byteRangeEndOffset;if(J(n)&&J(r)){var a;let o=n,l=r;if(s.sn==="initSegment"&&((a=s.decryptdata)==null?void 0:a.method)==="AES-128"){const u=r-n;u%16&&(l=r+(16-u%16)),n!==0&&(i.resetIV=!0,o=n-16)}i.rangeStart=o,i.rangeEnd=l}return i}function Hr(s,e){const t=new Error(`GAP ${s.gap?"tag":"attribute"} found`),i={type:re.MEDIA_ERROR,details:O.FRAG_GAP,fatal:!1,frag:s,error:t,networkDetails:null};return e&&(i.part=e),(e||s).stats.aborted=!0,new ut(i)}class ut extends Error{constructor(e){super(e.error.message),this.data=void 0,this.data=e}}class vd{constructor(e,t){this.subtle=void 0,this.aesIV=void 0,this.subtle=e,this.aesIV=t}decrypt(e,t){return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},t,e)}}class yd{constructor(e,t){this.subtle=void 0,this.key=void 0,this.subtle=e,this.key=t}expandKey(){return this.subtle.importKey("raw",this.key,{name:"AES-CBC"},!1,["encrypt","decrypt"])}}function Sd(s){const e=s.byteLength,t=e&&new DataView(s.buffer).getUint8(e-1);return t?It(s,0,e-t):s}class Td{constructor(){this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.ksRows=0,this.keySize=0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.initTable()}uint8ArrayToUint32Array_(e){const t=new DataView(e),i=new Uint32Array(4);for(let n=0;n<4;n++)i[n]=t.getUint32(n*4);return i}initTable(){const e=this.sBox,t=this.invSBox,i=this.subMix,n=i[0],r=i[1],a=i[2],o=i[3],l=this.invSubMix,u=l[0],c=l[1],d=l[2],f=l[3],h=new Uint32Array(256);let g=0,p=0,m=0;for(m=0;m<256;m++)m<128?h[m]=m<<1:h[m]=m<<1^283;for(m=0;m<256;m++){let y=p^p<<1^p<<2^p<<3^p<<4;y=y>>>8^y&255^99,e[g]=y,t[y]=g;const S=h[g],T=h[S],I=h[T];let E=h[y]*257^y*16843008;n[g]=E<<24|E>>>8,r[g]=E<<16|E>>>16,a[g]=E<<8|E>>>24,o[g]=E,E=I*16843009^T*65537^S*257^g*16843008,u[y]=E<<24|E>>>8,c[y]=E<<16|E>>>16,d[y]=E<<8|E>>>24,f[y]=E,g?(g=S^h[h[h[I^S]]],p^=h[h[p]]):g=p=1}}expandKey(e){const t=this.uint8ArrayToUint32Array_(e);let i=!0,n=0;for(;n<t.length&&i;)i=t[n]===this.key[n],n++;if(i)return;this.key=t;const r=this.keySize=t.length;if(r!==4&&r!==6&&r!==8)throw new Error("Invalid aes key size="+r);const a=this.ksRows=(r+6+1)*4;let o,l;const u=this.keySchedule=new Uint32Array(a),c=this.invKeySchedule=new Uint32Array(a),d=this.sBox,f=this.rcon,h=this.invSubMix,g=h[0],p=h[1],m=h[2],y=h[3];let S,T;for(o=0;o<a;o++){if(o<r){S=u[o]=t[o];continue}T=S,o%r===0?(T=T<<8|T>>>24,T=d[T>>>24]<<24|d[T>>>16&255]<<16|d[T>>>8&255]<<8|d[T&255],T^=f[o/r|0]<<24):r>6&&o%r===4&&(T=d[T>>>24]<<24|d[T>>>16&255]<<16|d[T>>>8&255]<<8|d[T&255]),u[o]=S=(u[o-r]^T)>>>0}for(l=0;l<a;l++)o=a-l,l&3?T=u[o]:T=u[o-4],l<4||o<=4?c[l]=T:c[l]=g[d[T>>>24]]^p[d[T>>>16&255]]^m[d[T>>>8&255]]^y[d[T&255]],c[l]=c[l]>>>0}networkToHostOrderSwap(e){return e<<24|(e&65280)<<8|(e&16711680)>>8|e>>>24}decrypt(e,t,i){const n=this.keySize+6,r=this.invKeySchedule,a=this.invSBox,o=this.invSubMix,l=o[0],u=o[1],c=o[2],d=o[3],f=this.uint8ArrayToUint32Array_(i);let h=f[0],g=f[1],p=f[2],m=f[3];const y=new Int32Array(e),S=new Int32Array(y.length);let T,I,E,P,R,w,b,x,D,C,M,N,Y,K;const z=this.networkToHostOrderSwap;for(;t<y.length;){for(D=z(y[t]),C=z(y[t+1]),M=z(y[t+2]),N=z(y[t+3]),R=D^r[0],w=N^r[1],b=M^r[2],x=C^r[3],Y=4,K=1;K<n;K++)T=l[R>>>24]^u[w>>16&255]^c[b>>8&255]^d[x&255]^r[Y],I=l[w>>>24]^u[b>>16&255]^c[x>>8&255]^d[R&255]^r[Y+1],E=l[b>>>24]^u[x>>16&255]^c[R>>8&255]^d[w&255]^r[Y+2],P=l[x>>>24]^u[R>>16&255]^c[w>>8&255]^d[b&255]^r[Y+3],R=T,w=I,b=E,x=P,Y=Y+4;T=a[R>>>24]<<24^a[w>>16&255]<<16^a[b>>8&255]<<8^a[x&255]^r[Y],I=a[w>>>24]<<24^a[b>>16&255]<<16^a[x>>8&255]<<8^a[R&255]^r[Y+1],E=a[b>>>24]<<24^a[x>>16&255]<<16^a[R>>8&255]<<8^a[w&255]^r[Y+2],P=a[x>>>24]<<24^a[R>>16&255]<<16^a[w>>8&255]<<8^a[b&255]^r[Y+3],S[t]=z(T^h),S[t+1]=z(P^g),S[t+2]=z(E^p),S[t+3]=z(I^m),h=D,g=C,p=M,m=N,t=t+4}return S.buffer}}const xd=16;class Fn{constructor(e,{removePKCS7Padding:t=!0}={}){if(this.logEnabled=!0,this.removePKCS7Padding=void 0,this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null,this.useSoftware=void 0,this.useSoftware=e.enableSoftwareAES,this.removePKCS7Padding=t,t)try{const i=self.crypto;i&&(this.subtle=i.subtle||i.webkitSubtle)}catch{}this.useSoftware=!this.subtle}destroy(){this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null}isSync(){return this.useSoftware}flush(){const{currentResult:e,remainderData:t}=this;if(!e||t)return this.reset(),null;const i=new Uint8Array(e);return this.reset(),this.removePKCS7Padding?Sd(i):i}reset(){this.currentResult=null,this.currentIV=null,this.remainderData=null,this.softwareDecrypter&&(this.softwareDecrypter=null)}decrypt(e,t,i){return this.useSoftware?new Promise((n,r)=>{this.softwareDecrypt(new Uint8Array(e),t,i);const a=this.flush();a?n(a.buffer):r(new Error("[softwareDecrypt] Failed to decrypt data"))}):this.webCryptoDecrypt(new Uint8Array(e),t,i)}softwareDecrypt(e,t,i){const{currentIV:n,currentResult:r,remainderData:a}=this;this.logOnce("JS AES decrypt"),a&&(e=Ge(a,e),this.remainderData=null);const o=this.getValidChunk(e);if(!o.length)return null;n&&(i=n);let l=this.softwareDecrypter;l||(l=this.softwareDecrypter=new Td),l.expandKey(t);const u=r;return this.currentResult=l.decrypt(o.buffer,0,i),this.currentIV=It(o,-16).buffer,u||null}webCryptoDecrypt(e,t,i){if(this.key!==t||!this.fastAesKey){if(!this.subtle)return Promise.resolve(this.onWebCryptoError(e,t,i));this.key=t,this.fastAesKey=new yd(this.subtle,t)}return this.fastAesKey.expandKey().then(n=>this.subtle?(this.logOnce("WebCrypto AES decrypt"),new vd(this.subtle,new Uint8Array(i)).decrypt(e.buffer,n)):Promise.reject(new Error("web crypto not initialized"))).catch(n=>(_.warn(`[decrypter]: WebCrypto Error, disable WebCrypto API, ${n.name}: ${n.message}`),this.onWebCryptoError(e,t,i)))}onWebCryptoError(e,t,i){this.useSoftware=!0,this.logEnabled=!0,this.softwareDecrypt(e,t,i);const n=this.flush();if(n)return n.buffer;throw new Error("WebCrypto and softwareDecrypt: failed to decrypt data")}getValidChunk(e){let t=e;const i=e.length-e.length%xd;return i!==e.length&&(t=It(e,0,i),this.remainderData=It(e,i)),t}logOnce(e){this.logEnabled&&(_.log(`[decrypter]: ${e}`),this.logEnabled=!1)}}const Ed={toString:function(s){let e="";const t=s.length;for(let i=0;i<t;i++)e+=`[${s.start(i).toFixed(3)}-${s.end(i).toFixed(3)}]`;return e}},G={STOPPED:"STOPPED",IDLE:"IDLE",KEY_LOADING:"KEY_LOADING",FRAG_LOADING:"FRAG_LOADING",FRAG_LOADING_WAITING_RETRY:"FRAG_LOADING_WAITING_RETRY",WAITING_TRACK:"WAITING_TRACK",PARSING:"PARSING",PARSED:"PARSED",ENDED:"ENDED",ERROR:"ERROR",WAITING_INIT_PTS:"WAITING_INIT_PTS",WAITING_LEVEL:"WAITING_LEVEL"};class On extends ud{constructor(e,t,i,n,r){super(),this.hls=void 0,this.fragPrevious=null,this.fragCurrent=null,this.fragmentTracker=void 0,this.transmuxer=null,this._state=G.STOPPED,this.playlistType=void 0,this.media=null,this.mediaBuffer=null,this.config=void 0,this.bitrateTest=!1,this.lastCurrentTime=0,this.nextLoadPosition=0,this.startPosition=0,this.startTimeOffset=null,this.loadedmetadata=!1,this.retryDate=0,this.levels=null,this.fragmentLoader=void 0,this.keyLoader=void 0,this.levelLastLoaded=null,this.startFragRequested=!1,this.decrypter=void 0,this.initPTS=[],this.buffering=!0,this.onvseeking=null,this.onvended=null,this.logPrefix="",this.log=void 0,this.warn=void 0,this.playlistType=r,this.logPrefix=n,this.log=_.log.bind(_,`${n}:`),this.warn=_.warn.bind(_,`${n}:`),this.hls=e,this.fragmentLoader=new pd(e.config),this.keyLoader=i,this.fragmentTracker=t,this.config=e.config,this.decrypter=new Fn(e.config),e.on(v.MANIFEST_LOADED,this.onManifestLoaded,this)}doTick(){this.onTickEnd()}onTickEnd(){}startLoad(e){}stopLoad(){this.fragmentLoader.abort(),this.keyLoader.abort(this.playlistType);const e=this.fragCurrent;e!=null&&e.loader&&(e.abortRequests(),this.fragmentTracker.removeFragment(e)),this.resetTransmuxer(),this.fragCurrent=null,this.fragPrevious=null,this.clearInterval(),this.clearNextTick(),this.state=G.STOPPED}pauseBuffering(){this.buffering=!1}resumeBuffering(){this.buffering=!0}_streamEnded(e,t){if(t.live||e.nextStart||!e.end||!this.media)return!1;const i=t.partList;if(i!=null&&i.length){const r=i[i.length-1];return me.isBuffered(this.media,r.start+r.duration/2)}const n=t.fragments[t.fragments.length-1].type;return this.fragmentTracker.isEndListAppended(n)}getLevelDetails(){if(this.levels&&this.levelLastLoaded!==null){var e;return(e=this.levelLastLoaded)==null?void 0:e.details}}onMediaAttached(e,t){const i=this.media=this.mediaBuffer=t.media;this.onvseeking=this.onMediaSeeking.bind(this),this.onvended=this.onMediaEnded.bind(this),i.addEventListener("seeking",this.onvseeking),i.addEventListener("ended",this.onvended);const n=this.config;this.levels&&n.autoStartLoad&&this.state===G.STOPPED&&this.startLoad(n.startPosition)}onMediaDetaching(){const e=this.media;e!=null&&e.ended&&(this.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0),e&&this.onvseeking&&this.onvended&&(e.removeEventListener("seeking",this.onvseeking),e.removeEventListener("ended",this.onvended),this.onvseeking=this.onvended=null),this.keyLoader&&this.keyLoader.detach(),this.media=this.mediaBuffer=null,this.loadedmetadata=!1,this.fragmentTracker.removeAllFragments(),this.stopLoad()}onMediaSeeking(){const{config:e,fragCurrent:t,media:i,mediaBuffer:n,state:r}=this,a=i?i.currentTime:0,o=me.bufferInfo(n||i,a,e.maxBufferHole);if(this.log(`media seeking to ${J(a)?a.toFixed(3):a}, state: ${r}`),this.state===G.ENDED)this.resetLoadingState();else if(t){const l=e.maxFragLookUpTolerance,u=t.start-l,c=t.start+t.duration+l;if(!o.len||c<o.start||u>o.end){const d=a>c;(a<u||d)&&(d&&t.loader&&(this.log("seeking outside of buffer while fragment load in progress, cancel fragment load"),t.abortRequests(),this.resetLoadingState()),this.fragPrevious=null)}}i&&(this.fragmentTracker.removeFragmentsInRange(a,1/0,this.playlistType,!0),this.lastCurrentTime=a),!this.loadedmetadata&&!o.len&&(this.nextLoadPosition=this.startPosition=a),this.tickImmediate()}onMediaEnded(){this.startPosition=this.lastCurrentTime=0}onManifestLoaded(e,t){this.startTimeOffset=t.startTimeOffset,this.initPTS=[]}onHandlerDestroying(){this.hls.off(v.MANIFEST_LOADED,this.onManifestLoaded,this),this.stopLoad(),super.onHandlerDestroying(),this.hls=null}onHandlerDestroyed(){this.state=G.STOPPED,this.fragmentLoader&&this.fragmentLoader.destroy(),this.keyLoader&&this.keyLoader.destroy(),this.decrypter&&this.decrypter.destroy(),this.hls=this.log=this.warn=this.decrypter=this.keyLoader=this.fragmentLoader=this.fragmentTracker=null,super.onHandlerDestroyed()}loadFragment(e,t,i){this._loadFragForPlayback(e,t,i)}_loadFragForPlayback(e,t,i){const n=r=>{if(this.fragContextChanged(e)){this.warn(`Fragment ${e.sn}${r.part?" p: "+r.part.index:""} of level ${e.level} was dropped during download.`),this.fragmentTracker.removeFragment(e);return}e.stats.chunkCount++,this._handleFragmentLoadProgress(r)};this._doFragLoad(e,t,i,n).then(r=>{if(!r)return;const a=this.state;if(this.fragContextChanged(e)){(a===G.FRAG_LOADING||!this.fragCurrent&&a===G.PARSING)&&(this.fragmentTracker.removeFragment(e),this.state=G.IDLE);return}"payload"in r&&(this.log(`Loaded fragment ${e.sn} of level ${e.level}`),this.hls.trigger(v.FRAG_LOADED,r)),this._handleFragmentLoadComplete(r)}).catch(r=>{this.state===G.STOPPED||this.state===G.ERROR||(this.warn(`Frag error: ${r?.message||r}`),this.resetFragmentLoading(e))})}clearTrackerIfNeeded(e){var t;const{fragmentTracker:i}=this;if(i.getState(e)===Le.APPENDING){const r=e.type,a=this.getFwdBufferInfo(this.mediaBuffer,r),o=Math.max(e.duration,a?a.len:this.config.maxBufferLength),l=this.backtrackFragment;((l?e.sn-l.sn:0)===1||this.reduceMaxBufferLength(o,e.duration))&&i.removeFragment(e)}else((t=this.mediaBuffer)==null?void 0:t.buffered.length)===0?i.removeAllFragments():i.hasParts(e.type)&&(i.detectPartialFragments({frag:e,part:null,stats:e.stats,id:e.type}),i.getState(e)===Le.PARTIAL&&i.removeFragment(e))}checkLiveUpdate(e){if(e.updated&&!e.live){const t=e.fragments[e.fragments.length-1];this.fragmentTracker.detectPartialFragments({frag:t,part:null,stats:t.stats,id:t.type})}e.fragments[0]||(e.deltaUpdateFailed=!0)}flushMainBuffer(e,t,i=null){if(!(e-t))return;const n={startOffset:e,endOffset:t,type:i};this.hls.trigger(v.BUFFER_FLUSHING,n)}_loadInitSegment(e,t){this._doFragLoad(e,t).then(i=>{if(!i||this.fragContextChanged(e)||!this.levels)throw new Error("init load aborted");return i}).then(i=>{const{hls:n}=this,{payload:r}=i,a=e.decryptdata;if(r&&r.byteLength>0&&a!=null&&a.key&&a.iv&&a.method==="AES-128"){const o=self.performance.now();return this.decrypter.decrypt(new Uint8Array(r),a.key.buffer,a.iv.buffer).catch(l=>{throw n.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.FRAG_DECRYPT_ERROR,fatal:!1,error:l,reason:l.message,frag:e}),l}).then(l=>{const u=self.performance.now();return n.trigger(v.FRAG_DECRYPTED,{frag:e,payload:l,stats:{tstart:o,tdecrypt:u}}),i.payload=l,this.completeInitSegmentLoad(i)})}return this.completeInitSegmentLoad(i)}).catch(i=>{this.state===G.STOPPED||this.state===G.ERROR||(this.warn(i),this.resetFragmentLoading(e))})}completeInitSegmentLoad(e){const{levels:t}=this;if(!t)throw new Error("init load aborted, missing levels");const i=e.frag.stats;this.state=G.IDLE,e.frag.data=new Uint8Array(e.payload),i.parsing.start=i.buffering.start=self.performance.now(),i.parsing.end=i.buffering.end=self.performance.now(),this.tick()}fragContextChanged(e){const{fragCurrent:t}=this;return!e||!t||e.sn!==t.sn||e.level!==t.level}fragBufferedComplete(e,t){var i,n,r,a;const o=this.mediaBuffer?this.mediaBuffer:this.media;if(this.log(`Buffered ${e.type} sn: ${e.sn}${t?" part: "+t.index:""} of ${this.playlistType===ne.MAIN?"level":"track"} ${e.level} (frag:[${((i=e.startPTS)!=null?i:NaN).toFixed(3)}-${((n=e.endPTS)!=null?n:NaN).toFixed(3)}] > buffer:${o?Ed.toString(me.getBuffered(o)):"(detached)"})`),e.sn!=="initSegment"){var l;if(e.type!==ne.SUBTITLE){const c=e.elementaryStreams;if(!Object.keys(c).some(d=>!!c[d])){this.state=G.IDLE;return}}const u=(l=this.levels)==null?void 0:l[e.level];u!=null&&u.fragmentError&&(this.log(`Resetting level fragment error count of ${u.fragmentError} on frag buffered`),u.fragmentError=0)}this.state=G.IDLE,o&&(!this.loadedmetadata&&e.type==ne.MAIN&&o.buffered.length&&((r=this.fragCurrent)==null?void 0:r.sn)===((a=this.fragPrevious)==null?void 0:a.sn)&&(this.loadedmetadata=!0,this.seekToStartPos()),this.tick())}seekToStartPos(){}_handleFragmentLoadComplete(e){const{transmuxer:t}=this;if(!t)return;const{frag:i,part:n,partsLoaded:r}=e,a=!r||r.length===0||r.some(l=>!l),o=new Pn(i.level,i.sn,i.stats.chunkCount+1,0,n?n.index:-1,!a);t.flush(o)}_handleFragmentLoadProgress(e){}_doFragLoad(e,t,i=null,n){var r;const a=t?.details;if(!this.levels||!a)throw new Error(`frag load aborted, missing level${a?"":" detail"}s`);let o=null;if(e.encrypted&&!((r=e.decryptdata)!=null&&r.key)?(this.log(`Loading key for ${e.sn} of [${a.startSN}-${a.endSN}], ${this.logPrefix==="[stream-controller]"?"level":"track"} ${e.level}`),this.state=G.KEY_LOADING,this.fragCurrent=e,o=this.keyLoader.load(e).then(c=>{if(!this.fragContextChanged(c.frag))return this.hls.trigger(v.KEY_LOADED,c),this.state===G.KEY_LOADING&&(this.state=G.IDLE),c}),this.hls.trigger(v.KEY_LOADING,{frag:e}),this.fragCurrent===null&&(o=Promise.reject(new Error("frag load aborted, context changed in KEY_LOADING")))):!e.encrypted&&a.encryptedFragments.length&&this.keyLoader.loadClear(e,a.encryptedFragments),i=Math.max(e.start,i||0),this.config.lowLatencyMode&&e.sn!=="initSegment"){const c=a.partList;if(c&&n){i>e.end&&a.fragmentHint&&(e=a.fragmentHint);const d=this.getNextPart(c,e,i);if(d>-1){const f=c[d];this.log(`Loading part sn: ${e.sn} p: ${f.index} cc: ${e.cc} of playlist [${a.startSN}-${a.endSN}] parts [0-${d}-${c.length-1}] ${this.logPrefix==="[stream-controller]"?"level":"track"}: ${e.level}, target: ${parseFloat(i.toFixed(3))}`),this.nextLoadPosition=f.start+f.duration,this.state=G.FRAG_LOADING;let h;return o?h=o.then(g=>!g||this.fragContextChanged(g.frag)?null:this.doFragPartsLoad(e,f,t,n)).catch(g=>this.handleFragLoadError(g)):h=this.doFragPartsLoad(e,f,t,n).catch(g=>this.handleFragLoadError(g)),this.hls.trigger(v.FRAG_LOADING,{frag:e,part:f,targetBufferTime:i}),this.fragCurrent===null?Promise.reject(new Error("frag load aborted, context changed in FRAG_LOADING parts")):h}else if(!e.url||this.loadedEndOfParts(c,i))return Promise.resolve(null)}}this.log(`Loading fragment ${e.sn} cc: ${e.cc} ${a?"of ["+a.startSN+"-"+a.endSN+"] ":""}${this.logPrefix==="[stream-controller]"?"level":"track"}: ${e.level}, target: ${parseFloat(i.toFixed(3))}`),J(e.sn)&&!this.bitrateTest&&(this.nextLoadPosition=e.start+e.duration),this.state=G.FRAG_LOADING;const l=this.config.progressive;let u;return l&&o?u=o.then(c=>!c||this.fragContextChanged(c?.frag)?null:this.fragmentLoader.load(e,n)).catch(c=>this.handleFragLoadError(c)):u=Promise.all([this.fragmentLoader.load(e,l?n:void 0),o]).then(([c])=>(!l&&c&&n&&n(c),c)).catch(c=>this.handleFragLoadError(c)),this.hls.trigger(v.FRAG_LOADING,{frag:e,targetBufferTime:i}),this.fragCurrent===null?Promise.reject(new Error("frag load aborted, context changed in FRAG_LOADING")):u}doFragPartsLoad(e,t,i,n){return new Promise((r,a)=>{var o;const l=[],u=(o=i.details)==null?void 0:o.partList,c=d=>{this.fragmentLoader.loadPart(e,d,n).then(f=>{l[d.index]=f;const h=f.part;this.hls.trigger(v.FRAG_LOADED,f);const g=Mr(i,e.sn,d.index+1)||Eo(u,e.sn,d.index+1);if(g)c(g);else return r({frag:e,part:h,partsLoaded:l})}).catch(a)};c(t)})}handleFragLoadError(e){if("data"in e){const t=e.data;e.data&&t.details===O.INTERNAL_ABORTED?this.handleFragLoadAborted(t.frag,t.part):this.hls.trigger(v.ERROR,t)}else this.hls.trigger(v.ERROR,{type:re.OTHER_ERROR,details:O.INTERNAL_EXCEPTION,err:e,error:e,fatal:!0});return null}_handleTransmuxerFlush(e){const t=this.getCurrentContext(e);if(!t||this.state!==G.PARSING){!this.fragCurrent&&this.state!==G.STOPPED&&this.state!==G.ERROR&&(this.state=G.IDLE);return}const{frag:i,part:n,level:r}=t,a=self.performance.now();i.stats.parsing.end=a,n&&(n.stats.parsing.end=a),this.updateLevelTiming(i,n,r,e.partial)}getCurrentContext(e){const{levels:t,fragCurrent:i}=this,{level:n,sn:r,part:a}=e;if(!(t!=null&&t[n]))return this.warn(`Levels object was unset while buffering fragment ${r} of level ${n}. The current chunk will not be buffered.`),null;const o=t[n],l=a>-1?Mr(o,r,a):null,u=l?l.fragment:Hc(o,r,i);return u?(i&&i!==u&&(u.stats=i.stats),{frag:u,part:l,level:o}):null}bufferFragmentData(e,t,i,n,r){var a;if(!e||this.state!==G.PARSING)return;const{data1:o,data2:l}=e;let u=o;if(o&&l&&(u=Ge(o,l)),!((a=u)!=null&&a.length))return;const c={type:e.type,frag:t,part:i,chunkMeta:n,parent:t.type,data:u};if(this.hls.trigger(v.BUFFER_APPENDING,c),e.dropped&&e.independent&&!i){if(r)return;this.flushBufferGap(t)}}flushBufferGap(e){const t=this.media;if(!t)return;if(!me.isBuffered(t,t.currentTime)){this.flushMainBuffer(0,e.start);return}const i=t.currentTime,n=me.bufferInfo(t,i,0),r=e.duration,a=Math.min(this.config.maxFragLookUpTolerance*2,r*.25),o=Math.max(Math.min(e.start-a,n.end-a),i+a);e.start-o>a&&this.flushMainBuffer(o,e.start)}getFwdBufferInfo(e,t){const i=this.getLoadPosition();return J(i)?this.getFwdBufferInfoAtPos(e,i,t):null}getFwdBufferInfoAtPos(e,t,i){const{config:{maxBufferHole:n}}=this,r=me.bufferInfo(e,t,n);if(r.len===0&&r.nextStart!==void 0){const a=this.fragmentTracker.getBufferedFrag(t,i);if(a&&r.nextStart<a.end)return me.bufferInfo(e,t,Math.max(r.nextStart,n))}return r}getMaxBufferLength(e){const{config:t}=this;let i;return e?i=Math.max(8*t.maxBufferSize/e,t.maxBufferLength):i=t.maxBufferLength,Math.min(i,t.maxMaxBufferLength)}reduceMaxBufferLength(e,t){const i=this.config,n=Math.max(Math.min(e-t,i.maxBufferLength),t),r=Math.max(e-t*3,i.maxMaxBufferLength/2,n);return r>=n?(i.maxMaxBufferLength=r,this.warn(`Reduce max buffer length to ${r}s`),!0):!1}getAppendedFrag(e,t=ne.MAIN){const i=this.fragmentTracker.getAppendedFrag(e,ne.MAIN);return i&&"fragment"in i?i.fragment:i}getNextFragment(e,t){const i=t.fragments,n=i.length;if(!n)return null;const{config:r}=this,a=i[0].start;let o;if(t.live){const l=r.initialLiveManifestSize;if(n<l)return this.warn(`Not enough fragments to start playback (have: ${n}, need: ${l})`),null;(!t.PTSKnown&&!this.startFragRequested&&this.startPosition===-1||e<a)&&(o=this.getInitialLiveFragment(t,i),this.startPosition=this.nextLoadPosition=o?this.hls.liveSyncPosition||o.start:e)}else e<=a&&(o=i[0]);if(!o){const l=r.lowLatencyMode?t.partEnd:t.fragmentEnd;o=this.getFragmentAtPosition(e,l,t)}return this.mapToInitFragWhenRequired(o)}isLoopLoading(e,t){const i=this.fragmentTracker.getState(e);return(i===Le.OK||i===Le.PARTIAL&&!!e.gap)&&this.nextLoadPosition>t}getNextFragmentLoopLoading(e,t,i,n,r){const a=e.gap,o=this.getNextFragment(this.nextLoadPosition,t);if(o===null)return o;if(e=o,a&&e&&!e.gap&&i.nextStart){const l=this.getFwdBufferInfoAtPos(this.mediaBuffer?this.mediaBuffer:this.media,i.nextStart,n);if(l!==null&&i.len+l.len>=r)return this.log(`buffer full after gaps in "${n}" playlist starting at sn: ${e.sn}`),null}return e}mapToInitFragWhenRequired(e){return e!=null&&e.initSegment&&!(e!=null&&e.initSegment.data)&&!this.bitrateTest?e.initSegment:e}getNextPart(e,t,i){let n=-1,r=!1,a=!0;for(let o=0,l=e.length;o<l;o++){const u=e[o];if(a=a&&!u.independent,n>-1&&i<u.start)break;const c=u.loaded;c?n=-1:(r||u.independent||a)&&u.fragment===t&&(n=o),r=c}return n}loadedEndOfParts(e,t){const i=e[e.length-1];return i&&t>i.start&&i.loaded}getInitialLiveFragment(e,t){const i=this.fragPrevious;let n=null;if(i){if(e.hasProgramDateTime&&(this.log(`Live playlist, switching playlist, load frag with same PDT: ${i.programDateTime}`),n=Wc(t,i.endProgramDateTime,this.config.maxFragLookUpTolerance)),!n){const r=i.sn+1;if(r>=e.startSN&&r<=e.endSN){const a=t[r-e.startSN];i.cc===a.cc&&(n=a,this.log(`Live playlist, switching playlist, load frag with next SN: ${n.sn}`))}n||(n=jc(t,i.cc),n&&this.log(`Live playlist, switching playlist, load frag with same CC: ${n.sn}`))}}else{const r=this.hls.liveSyncPosition;r!==null&&(n=this.getFragmentAtPosition(r,this.bitrateTest?e.fragmentEnd:e.edge,e))}return n}getFragmentAtPosition(e,t,i){const{config:n}=this;let{fragPrevious:r}=this,{fragments:a,endSN:o}=i;const{fragmentHint:l}=i,{maxFragLookUpTolerance:u}=n,c=i.partList,d=!!(n.lowLatencyMode&&c!=null&&c.length&&l);d&&l&&!this.bitrateTest&&(a=a.concat(l),o=l.sn);let f;if(e<t){const h=e>t-u?0:u;f=os(r,a,e,h)}else f=a[a.length-1];if(f){const h=f.sn-i.startSN,g=this.fragmentTracker.getState(f);if((g===Le.OK||g===Le.PARTIAL&&f.gap)&&(r=f),r&&f.sn===r.sn&&(!d||c[0].fragment.sn>f.sn)&&r&&f.level===r.level){const m=a[h+1];f.sn<o&&this.fragmentTracker.getState(m)!==Le.OK?f=m:f=null}}return f}synchronizeToLiveEdge(e){const{config:t,media:i}=this;if(!i)return;const n=this.hls.liveSyncPosition,r=i.currentTime,a=e.fragments[0].start,o=e.edge,l=r>=a-t.maxFragLookUpTolerance&&r<=o;if(n!==null&&i.duration>n&&(r<n||!l)){const u=t.liveMaxLatencyDuration!==void 0?t.liveMaxLatencyDuration:t.liveMaxLatencyDurationCount*e.targetduration;(!l&&i.readyState<4||r<o-u)&&(this.loadedmetadata||(this.nextLoadPosition=n),i.readyState&&(this.warn(`Playback: ${r.toFixed(3)} is located too far from the end of live sliding playlist: ${o}, reset currentTime to : ${n.toFixed(3)}`),i.currentTime=n))}}alignPlaylists(e,t,i){const n=e.fragments.length;if(!n)return this.warn("No fragments in live playlist"),0;const r=e.fragments[0].start,a=!t,o=e.alignedSliding&&J(r);if(a||!o&&!r){const{fragPrevious:l}=this;gd(l,i,e);const u=e.fragments[0].start;return this.log(`Live playlist sliding: ${u.toFixed(2)} start-sn: ${t?t.startSN:"na"}->${e.startSN} prev-sn: ${l?l.sn:"na"} fragments: ${n}`),u}return r}waitForCdnTuneIn(e){return e.live&&e.canBlockReload&&e.partTarget&&e.tuneInGoal>Math.max(e.partHoldBack,e.partTarget*3)}setStartPosition(e,t){let i=this.startPosition;if(i<t&&(i=-1),i===-1||this.lastCurrentTime===-1){const n=this.startTimeOffset!==null,r=n?this.startTimeOffset:e.startTimeOffset;r!==null&&J(r)?(i=t+r,r<0&&(i+=e.totalduration),i=Math.min(Math.max(t,i),t+e.totalduration),this.log(`Start time offset ${r} found in ${n?"multivariant":"media"} playlist, adjust startPosition to ${i}`),this.startPosition=i):e.live?i=this.hls.liveSyncPosition||t:this.startPosition=i=0,this.lastCurrentTime=i}this.nextLoadPosition=i}getLoadPosition(){const{media:e}=this;let t=0;return this.loadedmetadata&&e?t=e.currentTime:this.nextLoadPosition&&(t=this.nextLoadPosition),t}handleFragLoadAborted(e,t){this.transmuxer&&e.sn!=="initSegment"&&e.stats.aborted&&(this.warn(`Fragment ${e.sn}${t?" part "+t.index:""} of level ${e.level} was aborted`),this.resetFragmentLoading(e))}resetFragmentLoading(e){(!this.fragCurrent||!this.fragContextChanged(e)&&this.state!==G.FRAG_LOADING_WAITING_RETRY)&&(this.state=G.IDLE)}onFragmentOrKeyLoadError(e,t){if(t.chunkMeta&&!t.frag){const c=this.getCurrentContext(t.chunkMeta);c&&(t.frag=c.frag)}const i=t.frag;if(!i||i.type!==e||!this.levels)return;if(this.fragContextChanged(i)){var n;this.warn(`Frag load error must match current frag to retry ${i.url} > ${(n=this.fragCurrent)==null?void 0:n.url}`);return}const r=t.details===O.FRAG_GAP;r&&this.fragmentTracker.fragBuffered(i,!0);const a=t.errorAction,{action:o,retryCount:l=0,retryConfig:u}=a||{};if(a&&o===De.RetryRequest&&u){this.resetStartWhenNotLoaded(this.levelLastLoaded);const c=kn(u,l);this.warn(`Fragment ${i.sn} of ${e} ${i.level} errored with ${t.details}, retrying loading ${l+1}/${u.maxNumRetry} in ${c}ms`),a.resolved=!0,this.retryDate=self.performance.now()+c,this.state=G.FRAG_LOADING_WAITING_RETRY}else if(u&&a)if(this.resetFragmentErrors(e),l<u.maxNumRetry)!r&&o!==De.RemoveAlternatePermanently&&(a.resolved=!0);else{_.warn(`${t.details} reached or exceeded max retry (${l})`);return}else a?.action===De.SendAlternateToPenaltyBox?this.state=G.WAITING_LEVEL:this.state=G.ERROR;this.tickImmediate()}reduceLengthAndFlushBuffer(e){if(this.state===G.PARSING||this.state===G.PARSED){const t=e.frag,i=e.parent,n=this.getFwdBufferInfo(this.mediaBuffer,i),r=n&&n.len>.5;r&&this.reduceMaxBufferLength(n.len,t?.duration||10);const a=!r;return a&&this.warn(`Buffer full error while media.currentTime is not buffered, flush ${i} buffer`),t&&(this.fragmentTracker.removeFragment(t),this.nextLoadPosition=t.start),this.resetLoadingState(),a}return!1}resetFragmentErrors(e){e===ne.AUDIO&&(this.fragCurrent=null),this.loadedmetadata||(this.startFragRequested=!1),this.state!==G.STOPPED&&(this.state=G.IDLE)}afterBufferFlushed(e,t,i){if(!e)return;const n=me.getBuffered(e);this.fragmentTracker.detectEvictedFragments(t,n,i),this.state===G.ENDED&&this.resetLoadingState()}resetLoadingState(){this.log("Reset loading state"),this.fragCurrent=null,this.fragPrevious=null,this.state=G.IDLE}resetStartWhenNotLoaded(e){if(!this.loadedmetadata){this.startFragRequested=!1;const t=e?e.details:null;t!=null&&t.live?(this.startPosition=-1,this.setStartPosition(t,0),this.resetLoadingState()):this.nextLoadPosition=this.startPosition}}resetWhenMissingContext(e){this.warn(`The loading context changed while buffering fragment ${e.sn} of level ${e.level}. This chunk will not be buffered.`),this.removeUnbufferedFrags(),this.resetStartWhenNotLoaded(this.levelLastLoaded),this.resetLoadingState()}removeUnbufferedFrags(e=0){this.fragmentTracker.removeFragmentsInRange(e,1/0,this.playlistType,!1,!0)}updateLevelTiming(e,t,i,n){var r;const a=i.details;if(!a){this.warn("level.details undefined");return}if(!Object.keys(e.elementaryStreams).reduce((l,u)=>{const c=e.elementaryStreams[u];if(c){const d=c.endPTS-c.startPTS;if(d<=0)return this.warn(`Could not parse fragment ${e.sn} ${u} duration reliably (${d})`),l||!1;const f=n?0:To(a,e,c.startPTS,c.endPTS,c.startDTS,c.endDTS);return this.hls.trigger(v.LEVEL_PTS_UPDATED,{details:a,level:i,drift:f,type:u,frag:e,start:c.startPTS,end:c.endPTS}),!0}return l},!1)&&((r=this.transmuxer)==null?void 0:r.error)===null){const l=new Error(`Found no media in fragment ${e.sn} of level ${e.level} resetting transmuxer to fallback to playlist timing`);if(i.fragmentError===0&&(i.fragmentError++,e.gap=!0,this.fragmentTracker.removeFragment(e),this.fragmentTracker.fragBuffered(e,!0)),this.warn(l.message),this.hls.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.FRAG_PARSING_ERROR,fatal:!1,error:l,frag:e,reason:`Found no media in msn ${e.sn} of level "${i.url}"`}),!this.hls)return;this.resetTransmuxer()}this.state=G.PARSED,this.hls.trigger(v.FRAG_PARSED,{frag:e,part:t})}resetTransmuxer(){this.transmuxer&&(this.transmuxer.destroy(),this.transmuxer=null)}recoverWorkerError(e){e.event==="demuxerWorker"&&(this.fragmentTracker.removeAllFragments(),this.resetTransmuxer(),this.resetStartWhenNotLoaded(this.levelLastLoaded),this.resetLoadingState())}set state(e){const t=this._state;t!==e&&(this._state=e,this.log(`${t}->${e}`))}get state(){return this._state}}class Io{constructor(){this.chunks=[],this.dataLength=0}push(e){this.chunks.push(e),this.dataLength+=e.length}flush(){const{chunks:e,dataLength:t}=this;let i;if(e.length)e.length===1?i=e[0]:i=Ad(e,t);else return new Uint8Array(0);return this.reset(),i}reset(){this.chunks.length=0,this.dataLength=0}}function Ad(s,e){const t=new Uint8Array(e);let i=0;for(let n=0;n<s.length;n++){const r=s[n];t.set(r,i),i+=r.length}return t}function bd(){return typeof __HLS_WORKER_BUNDLE__=="function"}function Ld(){const s=new self.Blob([`var exports={};var module={exports:exports};function define(f){f()};define.amd=true;(${__HLS_WORKER_BUNDLE__.toString()})(true);`],{type:"text/javascript"}),e=self.URL.createObjectURL(s);return{worker:new self.Worker(e),objectURL:e}}function Rd(s){const e=new self.URL(s,self.location.href).href;return{worker:new self.Worker(e),scriptURL:e}}function Je(s="",e=9e4){return{type:s,id:-1,pid:-1,inputTimeScale:e,sequenceNumber:-1,samples:[],dropped:0}}class Mn{constructor(){this._audioTrack=void 0,this._id3Track=void 0,this.frameIndex=0,this.cachedData=null,this.basePTS=null,this.initPTS=null,this.lastPTS=null}resetInitSegment(e,t,i,n){this._id3Track={type:"id3",id:3,pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0}}resetTimeStamp(e){this.initPTS=e,this.resetContiguity()}resetContiguity(){this.basePTS=null,this.lastPTS=null,this.frameIndex=0}canParse(e,t){return!1}appendFrame(e,t,i){}demux(e,t){this.cachedData&&(e=Ge(this.cachedData,e),this.cachedData=null);let i=fi(e,0),n=i?i.length:0,r;const a=this._audioTrack,o=this._id3Track,l=i?wn(i):void 0,u=e.length;for((this.basePTS===null||this.frameIndex===0&&J(l))&&(this.basePTS=Id(l,t,this.initPTS),this.lastPTS=this.basePTS),this.lastPTS===null&&(this.lastPTS=this.basePTS),i&&i.length>0&&o.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:i,type:He.audioId3,duration:Number.POSITIVE_INFINITY});n<u;){if(this.canParse(e,n)){const c=this.appendFrame(a,e,n);c?(this.frameIndex++,this.lastPTS=c.sample.pts,n+=c.length,r=n):n=u}else zu(e,n)?(i=fi(e,n),o.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:i,type:He.audioId3,duration:Number.POSITIVE_INFINITY}),n+=i.length,r=n):n++;if(n===u&&r!==u){const c=It(e,r);this.cachedData?this.cachedData=Ge(this.cachedData,c):this.cachedData=c}}return{audioTrack:a,videoTrack:Je(),id3Track:o,textTrack:Je()}}demuxSampleAes(e,t,i){return Promise.reject(new Error(`[${this}] This demuxer does not support Sample-AES decryption`))}flush(e){const t=this.cachedData;return t&&(this.cachedData=null,this.demux(t,0)),{audioTrack:this._audioTrack,videoTrack:Je(),id3Track:this._id3Track,textTrack:Je()}}destroy(){}}const Id=(s,e,t)=>{if(J(s))return s*90;const i=t?t.baseTime*9e4/t.timescale:0;return e*9e4+i};function Cd(s,e,t,i){let n,r,a,o;const l=navigator.userAgent.toLowerCase(),u=i,c=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];n=((e[t+2]&192)>>>6)+1;const d=(e[t+2]&60)>>>2;if(d>c.length-1){const f=new Error(`invalid ADTS sampling index:${d}`);s.emit(v.ERROR,v.ERROR,{type:re.MEDIA_ERROR,details:O.FRAG_PARSING_ERROR,fatal:!0,error:f,reason:f.message});return}return a=(e[t+2]&1)<<2,a|=(e[t+3]&192)>>>6,_.log(`manifest codec:${i}, ADTS type:${n}, samplingIndex:${d}`),/firefox/i.test(l)?d>=6?(n=5,o=new Array(4),r=d-3):(n=2,o=new Array(2),r=d):l.indexOf("android")!==-1?(n=2,o=new Array(2),r=d):(n=5,o=new Array(4),i&&(i.indexOf("mp4a.40.29")!==-1||i.indexOf("mp4a.40.5")!==-1)||!i&&d>=6?r=d-3:((i&&i.indexOf("mp4a.40.2")!==-1&&(d>=6&&a===1||/vivaldi/i.test(l))||!i&&a===1)&&(n=2,o=new Array(2)),r=d)),o[0]=n<<3,o[0]|=(d&14)>>1,o[1]|=(d&1)<<7,o[1]|=a<<3,n===5&&(o[1]|=(r&14)>>1,o[2]=(r&1)<<7,o[2]|=8,o[3]=0),{config:o,samplerate:c[d],channelCount:a,codec:"mp4a.40."+n,manifestCodec:u}}function Co(s,e){return s[e]===255&&(s[e+1]&246)===240}function Do(s,e){return s[e+1]&1?7:9}function Nn(s,e){return(s[e+3]&3)<<11|s[e+4]<<3|(s[e+5]&224)>>>5}function Dd(s,e){return e+5<s.length}function us(s,e){return e+1<s.length&&Co(s,e)}function wd(s,e){return Dd(s,e)&&Co(s,e)&&Nn(s,e)<=s.length-e}function kd(s,e){if(us(s,e)){const t=Do(s,e);if(e+t>=s.length)return!1;const i=Nn(s,e);if(i<=t)return!1;const n=e+i;return n===s.length||us(s,n)}return!1}function wo(s,e,t,i,n){if(!s.samplerate){const r=Cd(e,t,i,n);if(!r)return;s.config=r.config,s.samplerate=r.samplerate,s.channelCount=r.channelCount,s.codec=r.codec,s.manifestCodec=r.manifestCodec,_.log(`parsed codec:${s.codec}, rate:${r.samplerate}, channels:${r.channelCount}`)}}function ko(s){return 1024*9e4/s}function _d(s,e){const t=Do(s,e);if(e+t<=s.length){const i=Nn(s,e)-t;if(i>0)return{headerLength:t,frameLength:i}}}function _o(s,e,t,i,n){const r=ko(s.samplerate),a=i+n*r,o=_d(e,t);let l;if(o){const{frameLength:d,headerLength:f}=o,h=f+d,g=Math.max(0,t+h-e.length);g?(l=new Uint8Array(h-f),l.set(e.subarray(t+f,e.length),0)):l=e.subarray(t+f,t+h);const p={unit:l,pts:a};return g||s.samples.push(p),{sample:p,length:h,missing:g}}const u=e.length-t;return l=new Uint8Array(u),l.set(e.subarray(t,e.length),0),{sample:{unit:l,pts:a},length:u,missing:-1}}let _i=null;const Pd=[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],Fd=[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],Od=[[0,72,144,12],[0,0,0,0],[0,72,144,12],[0,144,144,12]],Md=[0,1,1,4];function Po(s,e,t,i,n){if(t+24>e.length)return;const r=Fo(e,t);if(r&&t+r.frameLength<=e.length){const a=r.samplesPerFrame*9e4/r.sampleRate,o=i+n*a,l={unit:e.subarray(t,t+r.frameLength),pts:o,dts:o};return s.config=[],s.channelCount=r.channelCount,s.samplerate=r.sampleRate,s.samples.push(l),{sample:l,length:r.frameLength,missing:0}}}function Fo(s,e){const t=s[e+1]>>3&3,i=s[e+1]>>1&3,n=s[e+2]>>4&15,r=s[e+2]>>2&3;if(t!==1&&n!==0&&n!==15&&r!==3){const a=s[e+2]>>1&1,o=s[e+3]>>6,l=t===3?3-i:i===3?3:4,u=Pd[l*14+n-1]*1e3,d=Fd[(t===3?0:t===2?1:2)*3+r],f=o===3?1:2,h=Od[t][i],g=Md[i],p=h*8*g,m=Math.floor(h*u/d+a)*g;if(_i===null){const T=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);_i=T?parseInt(T[1]):0}return!!_i&&_i<=87&&i===2&&u>=224e3&&o===0&&(s[e+3]=s[e+3]|128),{sampleRate:d,channelCount:f,frameLength:m,samplesPerFrame:p}}}function Bn(s,e){return s[e]===255&&(s[e+1]&224)===224&&(s[e+1]&6)!==0}function Oo(s,e){return e+1<s.length&&Bn(s,e)}function Nd(s,e){return Bn(s,e)&&4<=s.length-e}function Mo(s,e){if(e+1<s.length&&Bn(s,e)){const i=Fo(s,e);let n=4;i!=null&&i.frameLength&&(n=i.frameLength);const r=e+n;return r===s.length||Oo(s,r)}return!1}class Bd extends Mn{constructor(e,t){super(),this.observer=void 0,this.config=void 0,this.observer=e,this.config=t}resetInitSegment(e,t,i,n){super.resetInitSegment(e,t,i,n),this._audioTrack={container:"audio/adts",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"aac",samples:[],manifestCodec:t,duration:n,inputTimeScale:9e4,dropped:0}}static probe(e){if(!e)return!1;const t=fi(e,0);let i=t?.length||0;if(Mo(e,i))return!1;for(let n=e.length;i<n;i++)if(kd(e,i))return _.log("ADTS sync word found !"),!0;return!1}canParse(e,t){return wd(e,t)}appendFrame(e,t,i){wo(e,this.observer,t,i,e.manifestCodec);const n=_o(e,t,i,this.basePTS,this.frameIndex);if(n&&n.missing===0)return n}}const Ud=/\/emsg[-/]ID3/i;class $d{constructor(e,t){this.remainderData=null,this.timeOffset=0,this.config=void 0,this.videoTrack=void 0,this.audioTrack=void 0,this.id3Track=void 0,this.txtTrack=void 0,this.config=t}resetTimeStamp(){}resetInitSegment(e,t,i,n){const r=this.videoTrack=Je("video",1),a=this.audioTrack=Je("audio",1),o=this.txtTrack=Je("text",1);if(this.id3Track=Je("id3",1),this.timeOffset=0,!(e!=null&&e.byteLength))return;const l=fo(e);if(l.video){const{id:u,timescale:c,codec:d}=l.video;r.id=u,r.timescale=o.timescale=c,r.codec=d}if(l.audio){const{id:u,timescale:c,codec:d}=l.audio;a.id=u,a.timescale=c,a.codec=d}o.id=lo.text,r.sampleDuration=0,r.duration=a.duration=n}resetContiguity(){this.remainderData=null}static probe(e){return sc(e)}demux(e,t){this.timeOffset=t;let i=e;const n=this.videoTrack,r=this.txtTrack;if(this.config.progressive){this.remainderData&&(i=Ge(this.remainderData,e));const o=dc(i);this.remainderData=o.remainder,n.samples=o.valid||new Uint8Array}else n.samples=i;const a=this.extractID3Track(n,t);return r.samples=vr(t,n),{videoTrack:n,audioTrack:this.audioTrack,id3Track:a,textTrack:this.txtTrack}}flush(){const e=this.timeOffset,t=this.videoTrack,i=this.txtTrack;t.samples=this.remainderData||new Uint8Array,this.remainderData=null;const n=this.extractID3Track(t,this.timeOffset);return i.samples=vr(e,t),{videoTrack:t,audioTrack:Je(),id3Track:n,textTrack:Je()}}extractID3Track(e,t){const i=this.id3Track;if(e.samples.length){const n=ae(e.samples,["emsg"]);n&&n.forEach(r=>{const a=gc(r);if(Ud.test(a.schemeIdUri)){const o=J(a.presentationTime)?a.presentationTime/a.timeScale:t+a.presentationTimeDelta/a.timeScale;let l=a.eventDuration===4294967295?Number.POSITIVE_INFINITY:a.eventDuration/a.timeScale;l<=.001&&(l=Number.POSITIVE_INFINITY);const u=a.payload;i.samples.push({data:u,len:u.byteLength,dts:o,pts:o,type:He.emsg,duration:l})}})}return i}demuxSampleAes(e,t,i){return Promise.reject(new Error("The MP4 demuxer does not support SAMPLE-AES decryption"))}destroy(){}}const No=(s,e)=>{let t=0,i=5;e+=i;const n=new Uint32Array(1),r=new Uint32Array(1),a=new Uint8Array(1);for(;i>0;){a[0]=s[e];const o=Math.min(i,8),l=8-o;r[0]=4278190080>>>24+l<<l,n[0]=(a[0]&r[0])>>l,t=t?t<<o|n[0]:n[0],e+=1,i-=o}return t};class Gd extends Mn{constructor(e){super(),this.observer=void 0,this.observer=e}resetInitSegment(e,t,i,n){super.resetInitSegment(e,t,i,n),this._audioTrack={container:"audio/ac-3",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"ac3",samples:[],manifestCodec:t,duration:n,inputTimeScale:9e4,dropped:0}}canParse(e,t){return t+64<e.length}appendFrame(e,t,i){const n=Bo(e,t,i,this.basePTS,this.frameIndex);if(n!==-1)return{sample:e.samples[e.samples.length-1],length:n,missing:0}}static probe(e){if(!e)return!1;const t=fi(e,0);if(!t)return!1;const i=t.length;return e[i]===11&&e[i+1]===119&&wn(t)!==void 0&&No(e,i)<16}}function Bo(s,e,t,i,n){if(t+8>e.length||e[t]!==11||e[t+1]!==119)return-1;const r=e[t+4]>>6;if(r>=3)return-1;const o=[48e3,44100,32e3][r],l=e[t+4]&63,c=[64,69,96,64,70,96,80,87,120,80,88,120,96,104,144,96,105,144,112,121,168,112,122,168,128,139,192,128,140,192,160,174,240,160,175,240,192,208,288,192,209,288,224,243,336,224,244,336,256,278,384,256,279,384,320,348,480,320,349,480,384,417,576,384,418,576,448,487,672,448,488,672,512,557,768,512,558,768,640,696,960,640,697,960,768,835,1152,768,836,1152,896,975,1344,896,976,1344,1024,1114,1536,1024,1115,1536,1152,1253,1728,1152,1254,1728,1280,1393,1920,1280,1394,1920][l*3+r]*2;if(t+c>e.length)return-1;const d=e[t+6]>>5;let f=0;d===2?f+=2:(d&1&&d!==1&&(f+=2),d&4&&(f+=2));const h=(e[t+6]<<8|e[t+7])>>12-f&1,p=[2,1,2,3,3,4,4,5][d]+h,m=e[t+5]>>3,y=e[t+5]&7,S=new Uint8Array([r<<6|m<<1|y>>2,(y&3)<<6|d<<3|h<<2|l>>4,l<<4&224]),T=1536/o*9e4,I=i+n*T,E=e.subarray(t,t+c);return s.config=S,s.channelCount=p,s.samplerate=o,s.samples.push({unit:E,pts:I}),c}class Vd{constructor(){this.VideoSample=null}createVideoSample(e,t,i,n){return{key:e,frame:!1,pts:t,dts:i,units:[],debug:n,length:0}}getLastNalUnit(e){var t;let i=this.VideoSample,n;if((!i||i.units.length===0)&&(i=e[e.length-1]),(t=i)!=null&&t.units){const r=i.units;n=r[r.length-1]}return n}pushAccessUnit(e,t){if(e.units.length&&e.frame){if(e.pts===void 0){const i=t.samples,n=i.length;if(n){const r=i[n-1];e.pts=r.pts,e.dts=r.dts}else{t.dropped++;return}}t.samples.push(e)}e.debug.length&&_.log(e.pts+"/"+e.dts+":"+e.debug)}}class qr{constructor(e){this.data=void 0,this.bytesAvailable=void 0,this.word=void 0,this.bitsAvailable=void 0,this.data=e,this.bytesAvailable=e.byteLength,this.word=0,this.bitsAvailable=0}loadWord(){const e=this.data,t=this.bytesAvailable,i=e.byteLength-t,n=new Uint8Array(4),r=Math.min(4,t);if(r===0)throw new Error("no bytes available");n.set(e.subarray(i,i+r)),this.word=new DataView(n.buffer).getUint32(0),this.bitsAvailable=r*8,this.bytesAvailable-=r}skipBits(e){let t;e=Math.min(e,this.bytesAvailable*8+this.bitsAvailable),this.bitsAvailable>e?(this.word<<=e,this.bitsAvailable-=e):(e-=this.bitsAvailable,t=e>>3,e-=t<<3,this.bytesAvailable-=t,this.loadWord(),this.word<<=e,this.bitsAvailable-=e)}readBits(e){let t=Math.min(this.bitsAvailable,e);const i=this.word>>>32-t;if(e>32&&_.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=t,this.bitsAvailable>0)this.word<<=t;else if(this.bytesAvailable>0)this.loadWord();else throw new Error("no bits available");return t=e-t,t>0&&this.bitsAvailable?i<<t|this.readBits(t):i}skipLZ(){let e;for(e=0;e<this.bitsAvailable;++e)if((this.word&2147483648>>>e)!==0)return this.word<<=e,this.bitsAvailable-=e,e;return this.loadWord(),e+this.skipLZ()}skipUEG(){this.skipBits(1+this.skipLZ())}skipEG(){this.skipBits(1+this.skipLZ())}readUEG(){const e=this.skipLZ();return this.readBits(e+1)-1}readEG(){const e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)}readBoolean(){return this.readBits(1)===1}readUByte(){return this.readBits(8)}readUShort(){return this.readBits(16)}readUInt(){return this.readBits(32)}skipScalingList(e){let t=8,i=8,n;for(let r=0;r<e;r++)i!==0&&(n=this.readEG(),i=(t+n+256)%256),t=i===0?t:i}readSPS(){let e=0,t=0,i=0,n=0,r,a,o;const l=this.readUByte.bind(this),u=this.readBits.bind(this),c=this.readUEG.bind(this),d=this.readBoolean.bind(this),f=this.skipBits.bind(this),h=this.skipEG.bind(this),g=this.skipUEG.bind(this),p=this.skipScalingList.bind(this);l();const m=l();if(u(5),f(3),l(),g(),m===100||m===110||m===122||m===244||m===44||m===83||m===86||m===118||m===128){const P=c();if(P===3&&f(1),g(),g(),f(1),d())for(a=P!==3?8:12,o=0;o<a;o++)d()&&(o<6?p(16):p(64))}g();const y=c();if(y===0)c();else if(y===1)for(f(1),h(),h(),r=c(),o=0;o<r;o++)h();g(),f(1);const S=c(),T=c(),I=u(1);I===0&&f(1),f(1),d()&&(e=c(),t=c(),i=c(),n=c());let E=[1,1];if(d()&&d())switch(l()){case 1:E=[1,1];break;case 2:E=[12,11];break;case 3:E=[10,11];break;case 4:E=[16,11];break;case 5:E=[40,33];break;case 6:E=[24,11];break;case 7:E=[20,11];break;case 8:E=[32,11];break;case 9:E=[80,33];break;case 10:E=[18,11];break;case 11:E=[15,11];break;case 12:E=[64,33];break;case 13:E=[160,99];break;case 14:E=[4,3];break;case 15:E=[3,2];break;case 16:E=[2,1];break;case 255:{E=[l()<<8|l(),l()<<8|l()];break}}return{width:Math.ceil((S+1)*16-e*2-t*2),height:(2-I)*(T+1)*16-(I?2:4)*(i+n),pixelRatio:E}}readSliceType(){return this.readUByte(),this.readUEG(),this.readUEG()}}class Kd extends Vd{parseAVCPES(e,t,i,n,r){const a=this.parseAVCNALu(e,i.data);let o=this.VideoSample,l,u=!1;i.data=null,o&&a.length&&!e.audFound&&(this.pushAccessUnit(o,e),o=this.VideoSample=this.createVideoSample(!1,i.pts,i.dts,"")),a.forEach(c=>{var d;switch(c.type){case 1:{let p=!1;l=!0;const m=c.data;if(u&&m.length>4){const y=new qr(m).readSliceType();(y===2||y===4||y===7||y===9)&&(p=!0)}if(p){var f;(f=o)!=null&&f.frame&&!o.key&&(this.pushAccessUnit(o,e),o=this.VideoSample=null)}o||(o=this.VideoSample=this.createVideoSample(!0,i.pts,i.dts,"")),o.frame=!0,o.key=p;break}case 5:l=!0,(d=o)!=null&&d.frame&&!o.key&&(this.pushAccessUnit(o,e),o=this.VideoSample=null),o||(o=this.VideoSample=this.createVideoSample(!0,i.pts,i.dts,"")),o.key=!0,o.frame=!0;break;case 6:{l=!0,go(c.data,1,i.pts,t.samples);break}case 7:{var h,g;l=!0,u=!0;const p=c.data,y=new qr(p).readSPS();if(!e.sps||e.width!==y.width||e.height!==y.height||((h=e.pixelRatio)==null?void 0:h[0])!==y.pixelRatio[0]||((g=e.pixelRatio)==null?void 0:g[1])!==y.pixelRatio[1]){e.width=y.width,e.height=y.height,e.pixelRatio=y.pixelRatio,e.sps=[p],e.duration=r;const S=p.subarray(1,4);let T="avc1.";for(let I=0;I<3;I++){let E=S[I].toString(16);E.length<2&&(E="0"+E),T+=E}e.codec=T}break}case 8:l=!0,e.pps=[c.data];break;case 9:l=!0,e.audFound=!0,o&&this.pushAccessUnit(o,e),o=this.VideoSample=this.createVideoSample(!1,i.pts,i.dts,"");break;case 12:l=!0;break;default:l=!1,o&&(o.debug+="unknown NAL "+c.type+" ");break}o&&l&&o.units.push(c)}),n&&o&&(this.pushAccessUnit(o,e),this.VideoSample=null)}parseAVCNALu(e,t){const i=t.byteLength;let n=e.naluState||0;const r=n,a=[];let o=0,l,u,c,d=-1,f=0;for(n===-1&&(d=0,f=t[0]&31,n=0,o=1);o<i;){if(l=t[o++],!n){n=l?0:1;continue}if(n===1){n=l?0:2;continue}if(!l)n=3;else if(l===1){if(u=o-n-1,d>=0){const h={data:t.subarray(d,u),type:f};a.push(h)}else{const h=this.getLastNalUnit(e.samples);h&&(r&&o<=4-r&&h.state&&(h.data=h.data.subarray(0,h.data.byteLength-r)),u>0&&(h.data=Ge(h.data,t.subarray(0,u)),h.state=0))}o<i?(c=t[o]&31,d=o,f=c,n=0):n=-1}else n=0}if(d>=0&&n>=0){const h={data:t.subarray(d,i),type:f,state:n};a.push(h)}if(a.length===0){const h=this.getLastNalUnit(e.samples);h&&(h.data=Ge(h.data,t))}return e.naluState=n,a}}class Hd{constructor(e,t,i){this.keyData=void 0,this.decrypter=void 0,this.keyData=i,this.decrypter=new Fn(t,{removePKCS7Padding:!1})}decryptBuffer(e){return this.decrypter.decrypt(e,this.keyData.key.buffer,this.keyData.iv.buffer)}decryptAacSample(e,t,i){const n=e[t].unit;if(n.length<=16)return;const r=n.subarray(16,n.length-n.length%16),a=r.buffer.slice(r.byteOffset,r.byteOffset+r.length);this.decryptBuffer(a).then(o=>{const l=new Uint8Array(o);n.set(l,16),this.decrypter.isSync()||this.decryptAacSamples(e,t+1,i)})}decryptAacSamples(e,t,i){for(;;t++){if(t>=e.length){i();return}if(!(e[t].unit.length<32)&&(this.decryptAacSample(e,t,i),!this.decrypter.isSync()))return}}getAvcEncryptedData(e){const t=Math.floor((e.length-48)/160)*16+16,i=new Int8Array(t);let n=0;for(let r=32;r<e.length-16;r+=160,n+=16)i.set(e.subarray(r,r+16),n);return i}getAvcDecryptedUnit(e,t){const i=new Uint8Array(t);let n=0;for(let r=32;r<e.length-16;r+=160,n+=16)e.set(i.subarray(n,n+16),r);return e}decryptAvcSample(e,t,i,n,r){const a=mo(r.data),o=this.getAvcEncryptedData(a);this.decryptBuffer(o.buffer).then(l=>{r.data=this.getAvcDecryptedUnit(a,l),this.decrypter.isSync()||this.decryptAvcSamples(e,t,i+1,n)})}decryptAvcSamples(e,t,i,n){if(e instanceof Uint8Array)throw new Error("Cannot decrypt samples of type Uint8Array");for(;;t++,i=0){if(t>=e.length){n();return}const r=e[t].units;for(;!(i>=r.length);i++){const a=r[i];if(!(a.data.length<=48||a.type!==1&&a.type!==5)&&(this.decryptAvcSample(e,t,i,n,a),!this.decrypter.isSync()))return}}}}const be=188;class ht{constructor(e,t,i){this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.sampleAes=null,this.pmtParsed=!1,this.audioCodec=void 0,this.videoCodec=void 0,this._duration=0,this._pmtId=-1,this._videoTrack=void 0,this._audioTrack=void 0,this._id3Track=void 0,this._txtTrack=void 0,this.aacOverFlow=null,this.remainderData=null,this.videoParser=void 0,this.observer=e,this.config=t,this.typeSupported=i,this.videoParser=new Kd}static probe(e){const t=ht.syncOffset(e);return t>0&&_.warn(`MPEG2-TS detected but first sync word found @ offset ${t}`),t!==-1}static syncOffset(e){const t=e.length;let i=Math.min(be*5,t-be)+1,n=0;for(;n<i;){let r=!1,a=-1,o=0;for(let l=n;l<t;l+=be)if(e[l]===71&&(t-l===be||e[l+be]===71)){if(o++,a===-1&&(a=l,a!==0&&(i=Math.min(a+be*99,e.length-be)+1)),r||(r=pn(e,l)===0),r&&o>1&&(a===0&&o>2||l+be>i))return a}else{if(o)return-1;break}n++}return-1}static createTrack(e,t){return{container:e==="video"||e==="audio"?"video/mp2t":void 0,type:e,id:lo[e],pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0,duration:e==="audio"?t:void 0}}resetInitSegment(e,t,i,n){this.pmtParsed=!1,this._pmtId=-1,this._videoTrack=ht.createTrack("video"),this._audioTrack=ht.createTrack("audio",n),this._id3Track=ht.createTrack("id3"),this._txtTrack=ht.createTrack("text"),this._audioTrack.segmentCodec="aac",this.aacOverFlow=null,this.remainderData=null,this.audioCodec=t,this.videoCodec=i,this._duration=n}resetTimeStamp(){}resetContiguity(){const{_audioTrack:e,_videoTrack:t,_id3Track:i}=this;e&&(e.pesData=null),t&&(t.pesData=null),i&&(i.pesData=null),this.aacOverFlow=null,this.remainderData=null}demux(e,t,i=!1,n=!1){i||(this.sampleAes=null);let r;const a=this._videoTrack,o=this._audioTrack,l=this._id3Track,u=this._txtTrack;let c=a.pid,d=a.pesData,f=o.pid,h=l.pid,g=o.pesData,p=l.pesData,m=null,y=this.pmtParsed,S=this._pmtId,T=e.length;if(this.remainderData&&(e=Ge(this.remainderData,e),T=e.length,this.remainderData=null),T<be&&!n)return this.remainderData=e,{audioTrack:o,videoTrack:a,id3Track:l,textTrack:u};const I=Math.max(0,ht.syncOffset(e));T-=(T-I)%be,T<e.byteLength&&!n&&(this.remainderData=new Uint8Array(e.buffer,T,e.buffer.byteLength-T));let E=0;for(let R=I;R<T;R+=be)if(e[R]===71){const w=!!(e[R+1]&64),b=pn(e,R),x=(e[R+3]&48)>>4;let D;if(x>1){if(D=R+5+e[R+4],D===R+be)continue}else D=R+4;switch(b){case c:w&&(d&&(r=Wt(d))&&this.videoParser.parseAVCPES(a,u,r,!1,this._duration),d={data:[],size:0}),d&&(d.data.push(e.subarray(D,R+be)),d.size+=R+be-D);break;case f:if(w){if(g&&(r=Wt(g)))switch(o.segmentCodec){case"aac":this.parseAACPES(o,r);break;case"mp3":this.parseMPEGPES(o,r);break;case"ac3":this.parseAC3PES(o,r);break}g={data:[],size:0}}g&&(g.data.push(e.subarray(D,R+be)),g.size+=R+be-D);break;case h:w&&(p&&(r=Wt(p))&&this.parseID3PES(l,r),p={data:[],size:0}),p&&(p.data.push(e.subarray(D,R+be)),p.size+=R+be-D);break;case 0:w&&(D+=e[D]+1),S=this._pmtId=qd(e,D);break;case S:{w&&(D+=e[D]+1);const C=Wd(e,D,this.typeSupported,i,this.observer);c=C.videoPid,c>0&&(a.pid=c,a.segmentCodec=C.segmentVideoCodec),f=C.audioPid,f>0&&(o.pid=f,o.segmentCodec=C.segmentAudioCodec),h=C.id3Pid,h>0&&(l.pid=h),m!==null&&!y&&(_.warn(`MPEG-TS PMT found at ${R} after unknown PID '${m}'. Backtracking to sync byte @${I} to parse all TS packets.`),m=null,R=I-188),y=this.pmtParsed=!0;break}case 17:case 8191:break;default:m=b;break}}else E++;E>0&&cs(this.observer,new Error(`Found ${E} TS packet/s that do not start with 0x47`)),a.pesData=d,o.pesData=g,l.pesData=p;const P={audioTrack:o,videoTrack:a,id3Track:l,textTrack:u};return n&&this.extractRemainingSamples(P),P}flush(){const{remainderData:e}=this;this.remainderData=null;let t;return e?t=this.demux(e,-1,!1,!0):t={videoTrack:this._videoTrack,audioTrack:this._audioTrack,id3Track:this._id3Track,textTrack:this._txtTrack},this.extractRemainingSamples(t),this.sampleAes?this.decrypt(t,this.sampleAes):t}extractRemainingSamples(e){const{audioTrack:t,videoTrack:i,id3Track:n,textTrack:r}=e,a=i.pesData,o=t.pesData,l=n.pesData;let u;if(a&&(u=Wt(a))?(this.videoParser.parseAVCPES(i,r,u,!0,this._duration),i.pesData=null):i.pesData=a,o&&(u=Wt(o))){switch(t.segmentCodec){case"aac":this.parseAACPES(t,u);break;case"mp3":this.parseMPEGPES(t,u);break;case"ac3":this.parseAC3PES(t,u);break}t.pesData=null}else o!=null&&o.size&&_.log("last AAC PES packet truncated,might overlap between fragments"),t.pesData=o;l&&(u=Wt(l))?(this.parseID3PES(n,u),n.pesData=null):n.pesData=l}demuxSampleAes(e,t,i){const n=this.demux(e,i,!0,!this.config.progressive),r=this.sampleAes=new Hd(this.observer,this.config,t);return this.decrypt(n,r)}decrypt(e,t){return new Promise(i=>{const{audioTrack:n,videoTrack:r}=e;n.samples&&n.segmentCodec==="aac"?t.decryptAacSamples(n.samples,0,()=>{r.samples?t.decryptAvcSamples(r.samples,0,0,()=>{i(e)}):i(e)}):r.samples&&t.decryptAvcSamples(r.samples,0,0,()=>{i(e)})})}destroy(){this._duration=0}parseAACPES(e,t){let i=0;const n=this.aacOverFlow;let r=t.data;if(n){this.aacOverFlow=null;const d=n.missing,f=n.sample.unit.byteLength;if(d===-1)r=Ge(n.sample.unit,r);else{const h=f-d;n.sample.unit.set(r.subarray(0,d),h),e.samples.push(n.sample),i=n.missing}}let a,o;for(a=i,o=r.length;a<o-1&&!us(r,a);a++);if(a!==i){let d;const f=a<o-1;if(f?d=`AAC PES did not start with ADTS header,offset:${a}`:d="No ADTS header found in AAC PES",cs(this.observer,new Error(d),f),!f)return}wo(e,this.observer,r,a,this.audioCodec);let l;if(t.pts!==void 0)l=t.pts;else if(n){const d=ko(e.samplerate);l=n.sample.pts+d}else{_.warn("[tsdemuxer]: AAC PES unknown PTS");return}let u=0,c;for(;a<o;)if(c=_o(e,r,a,l,u),a+=c.length,c.missing){this.aacOverFlow=c;break}else for(u++;a<o-1&&!us(r,a);a++);}parseMPEGPES(e,t){const i=t.data,n=i.length;let r=0,a=0;const o=t.pts;if(o===void 0){_.warn("[tsdemuxer]: MPEG PES unknown PTS");return}for(;a<n;)if(Oo(i,a)){const l=Po(e,i,a,o,r);if(l)a+=l.length,r++;else break}else a++}parseAC3PES(e,t){{const i=t.data,n=t.pts;if(n===void 0){_.warn("[tsdemuxer]: AC3 PES unknown PTS");return}const r=i.length;let a=0,o=0,l;for(;o<r&&(l=Bo(e,i,o,n,a++))>0;)o+=l}}parseID3PES(e,t){if(t.pts===void 0){_.warn("[tsdemuxer]: ID3 PES unknown PTS");return}const i=xe({},t,{type:this._videoTrack?He.emsg:He.audioId3,duration:Number.POSITIVE_INFINITY});e.samples.push(i)}}function pn(s,e){return((s[e+1]&31)<<8)+s[e+2]}function qd(s,e){return(s[e+10]&31)<<8|s[e+11]}function Wd(s,e,t,i,n){const r={audioPid:-1,videoPid:-1,id3Pid:-1,segmentVideoCodec:"avc",segmentAudioCodec:"aac"},a=(s[e+1]&15)<<8|s[e+2],o=e+3+a-4,l=(s[e+10]&15)<<8|s[e+11];for(e+=12+l;e<o;){const u=pn(s,e),c=(s[e+3]&15)<<8|s[e+4];switch(s[e]){case 207:if(!i){Us("ADTS AAC");break}case 15:r.audioPid===-1&&(r.audioPid=u);break;case 21:r.id3Pid===-1&&(r.id3Pid=u);break;case 219:if(!i){Us("H.264");break}case 27:r.videoPid===-1&&(r.videoPid=u,r.segmentVideoCodec="avc");break;case 3:case 4:!t.mpeg&&!t.mp3?_.log("MPEG audio found, not supported in this browser"):r.audioPid===-1&&(r.audioPid=u,r.segmentAudioCodec="mp3");break;case 193:if(!i){Us("AC-3");break}case 129:t.ac3?r.audioPid===-1&&(r.audioPid=u,r.segmentAudioCodec="ac3"):_.log("AC-3 audio found, not supported in this browser");break;case 6:if(r.audioPid===-1&&c>0){let d=e+5,f=c;for(;f>2;){switch(s[d]){case 106:t.ac3!==!0?_.log("AC-3 audio found, not supported in this browser for now"):(r.audioPid=u,r.segmentAudioCodec="ac3");break}const g=s[d+1]+2;d+=g,f-=g}}break;case 194:case 135:return cs(n,new Error("Unsupported EC-3 in M2TS found")),r;case 36:return cs(n,new Error("Unsupported HEVC in M2TS found")),r}e+=c+5}return r}function cs(s,e,t){_.warn(`parsing error: ${e.message}`),s.emit(v.ERROR,v.ERROR,{type:re.MEDIA_ERROR,details:O.FRAG_PARSING_ERROR,fatal:!1,levelRetry:t,error:e,reason:e.message})}function Us(s){_.log(`${s} with AES-128-CBC encryption found in unencrypted stream`)}function Wt(s){let e=0,t,i,n,r,a;const o=s.data;if(!s||s.size===0)return null;for(;o[0].length<19&&o.length>1;)o[0]=Ge(o[0],o[1]),o.splice(1,1);if(t=o[0],(t[0]<<16)+(t[1]<<8)+t[2]===1){if(i=(t[4]<<8)+t[5],i&&i>s.size-6)return null;const u=t[7];u&192&&(r=(t[9]&14)*536870912+(t[10]&255)*4194304+(t[11]&254)*16384+(t[12]&255)*128+(t[13]&254)/2,u&64?(a=(t[14]&14)*536870912+(t[15]&255)*4194304+(t[16]&254)*16384+(t[17]&255)*128+(t[18]&254)/2,r-a>60*9e4&&(_.warn(`${Math.round((r-a)/9e4)}s delta between PTS and DTS, align them`),r=a)):a=r),n=t[8];let c=n+9;if(s.size<=c)return null;s.size-=c;const d=new Uint8Array(s.size);for(let f=0,h=o.length;f<h;f++){t=o[f];let g=t.byteLength;if(c)if(c>g){c-=g;continue}else t=t.subarray(c),g-=c,c=0;d.set(t,e),e+=g}return i&&(i-=n+3),{data:d,pts:r,dts:a,len:i}}return null}class Yd extends Mn{resetInitSegment(e,t,i,n){super.resetInitSegment(e,t,i,n),this._audioTrack={container:"audio/mpeg",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"mp3",samples:[],manifestCodec:t,duration:n,inputTimeScale:9e4,dropped:0}}static probe(e){if(!e)return!1;const t=fi(e,0);let i=t?.length||0;if(t&&e[i]===11&&e[i+1]===119&&wn(t)!==void 0&&No(e,i)<=16)return!1;for(let n=e.length;i<n;i++)if(Mo(e,i))return _.log("MPEG Audio sync word found !"),!0;return!1}canParse(e,t){return Nd(e,t)}appendFrame(e,t,i){if(this.basePTS!==null)return Po(e,t,i,this.basePTS,this.frameIndex)}}class Wr{static getSilentFrame(e,t){switch(e){case"mp4a.40.2":if(t===1)return new Uint8Array([0,200,0,128,35,128]);if(t===2)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(t===3)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(t===4)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(t===5)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(t===6)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224]);break;default:if(t===1)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(t===2)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(t===3)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);break}}}const dt=Math.pow(2,32)-1;class F{static init(){F.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],dac3:[],"ac-3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]};let e;for(e in F.types)F.types.hasOwnProperty(e)&&(F.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);const t=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),i=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);F.HDLR_TYPES={video:t,audio:i};const n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),r=new Uint8Array([0,0,0,0,0,0,0,0]);F.STTS=F.STSC=F.STCO=r,F.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),F.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),F.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),F.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);const a=new Uint8Array([105,115,111,109]),o=new Uint8Array([97,118,99,49]),l=new Uint8Array([0,0,0,1]);F.FTYP=F.box(F.types.ftyp,a,l,a,o),F.DINF=F.box(F.types.dinf,F.box(F.types.dref,n))}static box(e,...t){let i=8,n=t.length;const r=n;for(;n--;)i+=t[n].byteLength;const a=new Uint8Array(i);for(a[0]=i>>24&255,a[1]=i>>16&255,a[2]=i>>8&255,a[3]=i&255,a.set(e,4),n=0,i=8;n<r;n++)a.set(t[n],i),i+=t[n].byteLength;return a}static hdlr(e){return F.box(F.types.hdlr,F.HDLR_TYPES[e])}static mdat(e){return F.box(F.types.mdat,e)}static mdhd(e,t){t*=e;const i=Math.floor(t/(dt+1)),n=Math.floor(t%(dt+1));return F.box(F.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,e&255,i>>24,i>>16&255,i>>8&255,i&255,n>>24,n>>16&255,n>>8&255,n&255,85,196,0,0]))}static mdia(e){return F.box(F.types.mdia,F.mdhd(e.timescale,e.duration),F.hdlr(e.type),F.minf(e))}static mfhd(e){return F.box(F.types.mfhd,new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,e&255]))}static minf(e){return e.type==="audio"?F.box(F.types.minf,F.box(F.types.smhd,F.SMHD),F.DINF,F.stbl(e)):F.box(F.types.minf,F.box(F.types.vmhd,F.VMHD),F.DINF,F.stbl(e))}static moof(e,t,i){return F.box(F.types.moof,F.mfhd(e),F.traf(i,t))}static moov(e){let t=e.length;const i=[];for(;t--;)i[t]=F.trak(e[t]);return F.box.apply(null,[F.types.moov,F.mvhd(e[0].timescale,e[0].duration)].concat(i).concat(F.mvex(e)))}static mvex(e){let t=e.length;const i=[];for(;t--;)i[t]=F.trex(e[t]);return F.box.apply(null,[F.types.mvex,...i])}static mvhd(e,t){t*=e;const i=Math.floor(t/(dt+1)),n=Math.floor(t%(dt+1)),r=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,e&255,i>>24,i>>16&255,i>>8&255,i&255,n>>24,n>>16&255,n>>8&255,n&255,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return F.box(F.types.mvhd,r)}static sdtp(e){const t=e.samples||[],i=new Uint8Array(4+t.length);let n,r;for(n=0;n<t.length;n++)r=t[n].flags,i[n+4]=r.dependsOn<<4|r.isDependedOn<<2|r.hasRedundancy;return F.box(F.types.sdtp,i)}static stbl(e){return F.box(F.types.stbl,F.stsd(e),F.box(F.types.stts,F.STTS),F.box(F.types.stsc,F.STSC),F.box(F.types.stsz,F.STSZ),F.box(F.types.stco,F.STCO))}static avc1(e){let t=[],i=[],n,r,a;for(n=0;n<e.sps.length;n++)r=e.sps[n],a=r.byteLength,t.push(a>>>8&255),t.push(a&255),t=t.concat(Array.prototype.slice.call(r));for(n=0;n<e.pps.length;n++)r=e.pps[n],a=r.byteLength,i.push(a>>>8&255),i.push(a&255),i=i.concat(Array.prototype.slice.call(r));const o=F.box(F.types.avcC,new Uint8Array([1,t[3],t[4],t[5],255,224|e.sps.length].concat(t).concat([e.pps.length]).concat(i))),l=e.width,u=e.height,c=e.pixelRatio[0],d=e.pixelRatio[1];return F.box(F.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,l>>8&255,l&255,u>>8&255,u&255,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),o,F.box(F.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),F.box(F.types.pasp,new Uint8Array([c>>24,c>>16&255,c>>8&255,c&255,d>>24,d>>16&255,d>>8&255,d&255])))}static esds(e){const t=e.config.length;return new Uint8Array([0,0,0,0,3,23+t,0,1,0,4,15+t,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([t]).concat(e.config).concat([6,1,2]))}static audioStsd(e){const t=e.samplerate;return new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount,0,16,0,0,0,0,t>>8&255,t&255,0,0])}static mp4a(e){return F.box(F.types.mp4a,F.audioStsd(e),F.box(F.types.esds,F.esds(e)))}static mp3(e){return F.box(F.types[".mp3"],F.audioStsd(e))}static ac3(e){return F.box(F.types["ac-3"],F.audioStsd(e),F.box(F.types.dac3,e.config))}static stsd(e){return e.type==="audio"?e.segmentCodec==="mp3"&&e.codec==="mp3"?F.box(F.types.stsd,F.STSD,F.mp3(e)):e.segmentCodec==="ac3"?F.box(F.types.stsd,F.STSD,F.ac3(e)):F.box(F.types.stsd,F.STSD,F.mp4a(e)):F.box(F.types.stsd,F.STSD,F.avc1(e))}static tkhd(e){const t=e.id,i=e.duration*e.timescale,n=e.width,r=e.height,a=Math.floor(i/(dt+1)),o=Math.floor(i%(dt+1));return F.box(F.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,t>>24&255,t>>16&255,t>>8&255,t&255,0,0,0,0,a>>24,a>>16&255,a>>8&255,a&255,o>>24,o>>16&255,o>>8&255,o&255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,n>>8&255,n&255,0,0,r>>8&255,r&255,0,0]))}static traf(e,t){const i=F.sdtp(e),n=e.id,r=Math.floor(t/(dt+1)),a=Math.floor(t%(dt+1));return F.box(F.types.traf,F.box(F.types.tfhd,new Uint8Array([0,0,0,0,n>>24,n>>16&255,n>>8&255,n&255])),F.box(F.types.tfdt,new Uint8Array([1,0,0,0,r>>24,r>>16&255,r>>8&255,r&255,a>>24,a>>16&255,a>>8&255,a&255])),F.trun(e,i.length+16+20+8+16+8+8),i)}static trak(e){return e.duration=e.duration||4294967295,F.box(F.types.trak,F.tkhd(e),F.mdia(e))}static trex(e){const t=e.id;return F.box(F.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,t&255,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))}static trun(e,t){const i=e.samples||[],n=i.length,r=12+16*n,a=new Uint8Array(r);let o,l,u,c,d,f;for(t+=8+r,a.set([e.type==="video"?1:0,0,15,1,n>>>24&255,n>>>16&255,n>>>8&255,n&255,t>>>24&255,t>>>16&255,t>>>8&255,t&255],0),o=0;o<n;o++)l=i[o],u=l.duration,c=l.size,d=l.flags,f=l.cts,a.set([u>>>24&255,u>>>16&255,u>>>8&255,u&255,c>>>24&255,c>>>16&255,c>>>8&255,c&255,d.isLeading<<2|d.dependsOn,d.isDependedOn<<6|d.hasRedundancy<<4|d.paddingValue<<1|d.isNonSync,d.degradPrio&61440,d.degradPrio&15,f>>>24&255,f>>>16&255,f>>>8&255,f&255],12+16*o);return F.box(F.types.trun,a)}static initSegment(e){F.types||F.init();const t=F.moov(e);return Ge(F.FTYP,t)}}F.types=void 0;F.HDLR_TYPES=void 0;F.STTS=void 0;F.STSC=void 0;F.STCO=void 0;F.STSZ=void 0;F.VMHD=void 0;F.SMHD=void 0;F.STSD=void 0;F.FTYP=void 0;F.DINF=void 0;const Uo=9e4;function Un(s,e,t=1,i=!1){const n=s*e*t;return i?Math.round(n):n}function zd(s,e,t=1,i=!1){return Un(s,e,1/t,i)}function ai(s,e=!1){return Un(s,1e3,1/Uo,e)}function jd(s,e=1){return Un(s,Uo,1/e)}const Xd=10*1e3,Yr=1024,Qd=1152,Zd=1536;let Yt=null,$s=null;class zi{constructor(e,t,i,n=""){if(this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.ISGenerated=!1,this._initPTS=null,this._initDTS=null,this.nextAvcDts=null,this.nextAudioPts=null,this.videoSampleDuration=null,this.isAudioContiguous=!1,this.isVideoContiguous=!1,this.videoTrackConfig=void 0,this.observer=e,this.config=t,this.typeSupported=i,this.ISGenerated=!1,Yt===null){const a=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);Yt=a?parseInt(a[1]):0}if($s===null){const r=navigator.userAgent.match(/Safari\/(\d+)/i);$s=r?parseInt(r[1]):0}}destroy(){this.config=this.videoTrackConfig=this._initPTS=this._initDTS=null}resetTimeStamp(e){_.log("[mp4-remuxer]: initPTS & initDTS reset"),this._initPTS=this._initDTS=e}resetNextTimestamp(){_.log("[mp4-remuxer]: reset next timestamp"),this.isVideoContiguous=!1,this.isAudioContiguous=!1}resetInitSegment(){_.log("[mp4-remuxer]: ISGenerated flag reset"),this.ISGenerated=!1,this.videoTrackConfig=void 0}getVideoStartPts(e){let t=!1;const i=e[0].pts,n=e.reduce((r,a)=>{let o=a.pts,l=o-r;return l<-4294967296&&(t=!0,o=$e(o,i),l=o-r),l>0?r:o},i);return t&&_.debug("PTS rollover detected"),n}remux(e,t,i,n,r,a,o,l){let u,c,d,f,h,g,p=r,m=r;const y=e.pid>-1,S=t.pid>-1,T=t.samples.length,I=e.samples.length>0,E=o&&T>0||T>1;if((!y||I)&&(!S||E)||this.ISGenerated||o){if(this.ISGenerated){var R,w,b,x;const N=this.videoTrackConfig;N&&(t.width!==N.width||t.height!==N.height||((R=t.pixelRatio)==null?void 0:R[0])!==((w=N.pixelRatio)==null?void 0:w[0])||((b=t.pixelRatio)==null?void 0:b[1])!==((x=N.pixelRatio)==null?void 0:x[1]))&&this.resetInitSegment()}else d=this.generateIS(e,t,r,a);const D=this.isVideoContiguous;let C=-1,M;if(E&&(C=Jd(t.samples),!D&&this.config.forceKeyFrameOnDiscontinuity))if(g=!0,C>0){_.warn(`[mp4-remuxer]: Dropped ${C} out of ${T} video samples due to a missing keyframe`);const N=this.getVideoStartPts(t.samples);t.samples=t.samples.slice(C),t.dropped+=C,m+=(t.samples[0].pts-N)/t.inputTimeScale,M=m}else C===-1&&(_.warn(`[mp4-remuxer]: No keyframe found out of ${T} video samples`),g=!1);if(this.ISGenerated){if(I&&E){const N=this.getVideoStartPts(t.samples),K=($e(e.samples[0].pts,N)-N)/t.inputTimeScale;p+=Math.max(0,K),m+=Math.max(0,-K)}if(I){if(e.samplerate||(_.warn("[mp4-remuxer]: regenerate InitSegment as audio detected"),d=this.generateIS(e,t,r,a)),c=this.remuxAudio(e,p,this.isAudioContiguous,a,S||E||l===ne.AUDIO?m:void 0),E){const N=c?c.endPTS-c.startPTS:0;t.inputTimeScale||(_.warn("[mp4-remuxer]: regenerate InitSegment as video detected"),d=this.generateIS(e,t,r,a)),u=this.remuxVideo(t,m,D,N)}}else E&&(u=this.remuxVideo(t,m,D,0));u&&(u.firstKeyFrame=C,u.independent=C!==-1,u.firstKeyFramePTS=M)}}return this.ISGenerated&&this._initPTS&&this._initDTS&&(i.samples.length&&(h=$o(i,r,this._initPTS,this._initDTS)),n.samples.length&&(f=Go(n,r,this._initPTS))),{audio:c,video:u,initSegment:d,independent:g,text:f,id3:h}}generateIS(e,t,i,n){const r=e.samples,a=t.samples,o=this.typeSupported,l={},u=this._initPTS;let c=!u||n,d="audio/mp4",f,h,g;if(c&&(f=h=1/0),e.config&&r.length){switch(e.timescale=e.samplerate,e.segmentCodec){case"mp3":o.mpeg?(d="audio/mpeg",e.codec=""):o.mp3&&(e.codec="mp3");break;case"ac3":e.codec="ac-3";break}l.audio={id:"audio",container:d,codec:e.codec,initSegment:e.segmentCodec==="mp3"&&o.mpeg?new Uint8Array(0):F.initSegment([e]),metadata:{channelCount:e.channelCount}},c&&(g=e.inputTimeScale,!u||g!==u.timescale?f=h=r[0].pts-Math.round(g*i):c=!1)}if(t.sps&&t.pps&&a.length){if(t.timescale=t.inputTimeScale,l.video={id:"main",container:"video/mp4",codec:t.codec,initSegment:F.initSegment([t]),metadata:{width:t.width,height:t.height}},c)if(g=t.inputTimeScale,!u||g!==u.timescale){const p=this.getVideoStartPts(a),m=Math.round(g*i);h=Math.min(h,$e(a[0].dts,p)-m),f=Math.min(f,p-m)}else c=!1;this.videoTrackConfig={width:t.width,height:t.height,pixelRatio:t.pixelRatio}}if(Object.keys(l).length)return this.ISGenerated=!0,c?(this._initPTS={baseTime:f,timescale:g},this._initDTS={baseTime:h,timescale:g}):f=g=void 0,{tracks:l,initPTS:f,timescale:g}}remuxVideo(e,t,i,n){const r=e.inputTimeScale,a=e.samples,o=[],l=a.length,u=this._initPTS;let c=this.nextAvcDts,d=8,f=this.videoSampleDuration,h,g,p=Number.POSITIVE_INFINITY,m=Number.NEGATIVE_INFINITY,y=!1;if(!i||c===null){const B=t*r,$=a[0].pts-$e(a[0].dts,a[0].pts);Yt&&c!==null&&Math.abs(B-$-c)<15e3?i=!0:c=B-$}const S=u.baseTime*r/u.timescale;for(let B=0;B<l;B++){const $=a[B];$.pts=$e($.pts-S,c),$.dts=$e($.dts-S,c),$.dts<a[B>0?B-1:B].dts&&(y=!0)}y&&a.sort(function(B,$){const k=B.dts-$.dts,L=B.pts-$.pts;return k||L}),h=a[0].dts,g=a[a.length-1].dts;const T=g-h,I=T?Math.round(T/(l-1)):f||e.inputTimeScale/30;if(i){const B=h-c,$=B>I,k=B<-1;if(($||k)&&($?_.warn(`AVC: ${ai(B,!0)} ms (${B}dts) hole between fragments detected at ${t.toFixed(3)}`):_.warn(`AVC: ${ai(-B,!0)} ms (${B}dts) overlapping between fragments detected at ${t.toFixed(3)}`),!k||c>=a[0].pts||Yt)){h=c;const L=a[0].pts-B;if($)a[0].dts=h,a[0].pts=L;else for(let q=0;q<a.length&&!(a[q].dts>L);q++)a[q].dts-=B,a[q].pts-=B;_.log(`Video: Initial PTS/DTS adjusted: ${ai(L,!0)}/${ai(h,!0)}, delta: ${ai(B,!0)} ms`)}}h=Math.max(0,h);let E=0,P=0,R=h;for(let B=0;B<l;B++){const $=a[B],k=$.units,L=k.length;let q=0;for(let ee=0;ee<L;ee++)q+=k[ee].data.length;P+=q,E+=L,$.length=q,$.dts<R?($.dts=R,R+=I/4|0||1):R=$.dts,p=Math.min($.pts,p),m=Math.max($.pts,m)}g=a[l-1].dts;const w=P+4*E+8;let b;try{b=new Uint8Array(w)}catch(B){this.observer.emit(v.ERROR,v.ERROR,{type:re.MUX_ERROR,details:O.REMUX_ALLOC_ERROR,fatal:!1,error:B,bytes:w,reason:`fail allocating video mdat ${w}`});return}const x=new DataView(b.buffer);x.setUint32(0,w),b.set(F.types.mdat,4);let D=!1,C=Number.POSITIVE_INFINITY,M=Number.POSITIVE_INFINITY,N=Number.NEGATIVE_INFINITY,Y=Number.NEGATIVE_INFINITY;for(let B=0;B<l;B++){const $=a[B],k=$.units;let L=0;for(let ie=0,H=k.length;ie<H;ie++){const j=k[ie],ue=j.data,X=j.data.byteLength;x.setUint32(d,X),d+=4,b.set(ue,d),d+=X,L+=4+X}let q;if(B<l-1)f=a[B+1].dts-$.dts,q=a[B+1].pts-$.pts;else{const ie=this.config,H=B>0?$.dts-a[B-1].dts:I;if(q=B>0?$.pts-a[B-1].pts:I,ie.stretchShortVideoTrack&&this.nextAudioPts!==null){const j=Math.floor(ie.maxBufferHole*r),ue=(n?p+n*r:this.nextAudioPts)-$.pts;ue>j?(f=ue-H,f<0?f=H:D=!0,_.log(`[mp4-remuxer]: It is approximately ${ue/90} ms to the next segment; using duration ${f/90} ms for the last video frame.`)):f=H}else f=H}const ee=Math.round($.pts-$.dts);C=Math.min(C,f),N=Math.max(N,f),M=Math.min(M,q),Y=Math.max(Y,q),o.push(new zr($.key,f,L,ee))}if(o.length){if(Yt){if(Yt<70){const B=o[0].flags;B.dependsOn=2,B.isNonSync=0}}else if($s&&Y-M<N-C&&I/N<.025&&o[0].cts===0){_.warn("Found irregular gaps in sample duration. Using PTS instead of DTS to determine MP4 sample duration.");let B=h;for(let $=0,k=o.length;$<k;$++){const L=B+o[$].duration,q=B+o[$].cts;if($<k-1){const ee=L+o[$+1].cts;o[$].duration=ee-q}else o[$].duration=$?o[$-1].duration:I;o[$].cts=0,B=L}}}f=D||!f?I:f,this.nextAvcDts=c=g+f,this.videoSampleDuration=f,this.isVideoContiguous=!0;const V={data1:F.moof(e.sequenceNumber++,h,xe({},e,{samples:o})),data2:b,startPTS:p/r,endPTS:(m+f)/r,startDTS:h/r,endDTS:c/r,type:"video",hasAudio:!1,hasVideo:!0,nb:o.length,dropped:e.dropped};return e.samples=[],e.dropped=0,V}getSamplesPerFrame(e){switch(e.segmentCodec){case"mp3":return Qd;case"ac3":return Zd;default:return Yr}}remuxAudio(e,t,i,n,r){const a=e.inputTimeScale,o=e.samplerate?e.samplerate:a,l=a/o,u=this.getSamplesPerFrame(e),c=u*l,d=this._initPTS,f=e.segmentCodec==="mp3"&&this.typeSupported.mpeg,h=[],g=r!==void 0;let p=e.samples,m=f?0:8,y=this.nextAudioPts||-1;const S=t*a,T=d.baseTime*a/d.timescale;if(this.isAudioContiguous=i=i||p.length&&y>0&&(n&&Math.abs(S-y)<9e3||Math.abs($e(p[0].pts-T,S)-y)<20*c),p.forEach(function(K){K.pts=$e(K.pts-T,S)}),!i||y<0){if(p=p.filter(K=>K.pts>=0),!p.length)return;r===0?y=0:n&&!g?y=Math.max(0,S):y=p[0].pts}if(e.segmentCodec==="aac"){const K=this.config.maxAudioFramesDrift;for(let z=0,V=y;z<p.length;z++){const B=p[z],$=B.pts,k=$-V,L=Math.abs(1e3*k/a);if(k<=-K*c&&g)z===0&&(_.warn(`Audio frame @ ${($/a).toFixed(3)}s overlaps nextAudioPts by ${Math.round(1e3*k/a)} ms.`),this.nextAudioPts=y=V=$);else if(k>=K*c&&L<Xd&&g){let q=Math.round(k/c);V=$-q*c,V<0&&(q--,V+=c),z===0&&(this.nextAudioPts=y=V),_.warn(`[mp4-remuxer]: Injecting ${q} audio frame @ ${(V/a).toFixed(3)}s due to ${Math.round(1e3*k/a)} ms gap.`);for(let ee=0;ee<q;ee++){const ie=Math.max(V,0);let H=Wr.getSilentFrame(e.manifestCodec||e.codec,e.channelCount);H||(_.log("[mp4-remuxer]: Unable to get silent frame for given audio codec; duplicating last frame instead."),H=B.unit.subarray()),p.splice(z,0,{unit:H,pts:ie}),V+=c,z++}}B.pts=V,V+=c}}let I=null,E=null,P,R=0,w=p.length;for(;w--;)R+=p[w].unit.byteLength;for(let K=0,z=p.length;K<z;K++){const V=p[K],B=V.unit;let $=V.pts;if(E!==null){const L=h[K-1];L.duration=Math.round(($-E)/l)}else if(i&&e.segmentCodec==="aac"&&($=y),I=$,R>0){R+=m;try{P=new Uint8Array(R)}catch(L){this.observer.emit(v.ERROR,v.ERROR,{type:re.MUX_ERROR,details:O.REMUX_ALLOC_ERROR,fatal:!1,error:L,bytes:R,reason:`fail allocating audio mdat ${R}`});return}f||(new DataView(P.buffer).setUint32(0,R),P.set(F.types.mdat,4))}else return;P.set(B,m);const k=B.byteLength;m+=k,h.push(new zr(!0,u,k,0)),E=$}const b=h.length;if(!b)return;const x=h[h.length-1];this.nextAudioPts=y=E+l*x.duration;const D=f?new Uint8Array(0):F.moof(e.sequenceNumber++,I/l,xe({},e,{samples:h}));e.samples=[];const C=I/a,M=y/a,Y={data1:D,data2:P,startPTS:C,endPTS:M,startDTS:C,endDTS:M,type:"audio",hasAudio:!0,hasVideo:!1,nb:b};return this.isAudioContiguous=!0,Y}remuxEmptyAudio(e,t,i,n){const r=e.inputTimeScale,a=e.samplerate?e.samplerate:r,o=r/a,l=this.nextAudioPts,u=this._initDTS,c=u.baseTime*9e4/u.timescale,d=(l!==null?l:n.startDTS*r)+c,f=n.endDTS*r+c,h=o*Yr,g=Math.ceil((f-d)/h),p=Wr.getSilentFrame(e.manifestCodec||e.codec,e.channelCount);if(_.warn("[mp4-remuxer]: remux empty Audio"),!p){_.trace("[mp4-remuxer]: Unable to remuxEmptyAudio since we were unable to get a silent frame for given audio codec");return}const m=[];for(let y=0;y<g;y++){const S=d+y*h;m.push({unit:p,pts:S,dts:S})}return e.samples=m,this.remuxAudio(e,t,i,!1)}}function $e(s,e){let t;if(e===null)return s;for(e<s?t=-8589934592:t=8589934592;Math.abs(s-e)>4294967296;)s+=t;return s}function Jd(s){for(let e=0;e<s.length;e++)if(s[e].key)return e;return-1}function $o(s,e,t,i){const n=s.samples.length;if(!n)return;const r=s.inputTimeScale;for(let o=0;o<n;o++){const l=s.samples[o];l.pts=$e(l.pts-t.baseTime*r/t.timescale,e*r)/r,l.dts=$e(l.dts-i.baseTime*r/i.timescale,e*r)/r}const a=s.samples;return s.samples=[],{samples:a}}function Go(s,e,t){const i=s.samples.length;if(!i)return;const n=s.inputTimeScale;for(let a=0;a<i;a++){const o=s.samples[a];o.pts=$e(o.pts-t.baseTime*n/t.timescale,e*n)/n}s.samples.sort((a,o)=>a.pts-o.pts);const r=s.samples;return s.samples=[],{samples:r}}class zr{constructor(e,t,i,n){this.size=void 0,this.duration=void 0,this.cts=void 0,this.flags=void 0,this.duration=t,this.size=i,this.cts=n,this.flags={isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:e?2:1,isNonSync:e?0:1}}}class ef{constructor(){this.emitInitSegment=!1,this.audioCodec=void 0,this.videoCodec=void 0,this.initData=void 0,this.initPTS=null,this.initTracks=void 0,this.lastEndTime=null}destroy(){}resetTimeStamp(e){this.initPTS=e,this.lastEndTime=null}resetNextTimestamp(){this.lastEndTime=null}resetInitSegment(e,t,i,n){this.audioCodec=t,this.videoCodec=i,this.generateInitSegment(ac(e,n)),this.emitInitSegment=!0}generateInitSegment(e){let{audioCodec:t,videoCodec:i}=this;if(!(e!=null&&e.byteLength)){this.initTracks=void 0,this.initData=void 0;return}const n=this.initData=fo(e);n.audio&&(t=jr(n.audio,fe.AUDIO)),n.video&&(i=jr(n.video,fe.VIDEO));const r={};n.audio&&n.video?r.audiovideo={container:"video/mp4",codec:t+","+i,initSegment:e,id:"main"}:n.audio?r.audio={container:"audio/mp4",codec:t,initSegment:e,id:"audio"}:n.video?r.video={container:"video/mp4",codec:i,initSegment:e,id:"main"}:_.warn("[passthrough-remuxer.ts]: initSegment does not contain moov or trak boxes."),this.initTracks=r}remux(e,t,i,n,r,a){var o,l;let{initPTS:u,lastEndTime:c}=this;const d={audio:void 0,video:void 0,text:n,id3:i,initSegment:void 0};J(c)||(c=this.lastEndTime=r||0);const f=t.samples;if(!(f!=null&&f.length))return d;const h={initPTS:void 0,timescale:1};let g=this.initData;if((o=g)!=null&&o.length||(this.generateInitSegment(f),g=this.initData),!((l=g)!=null&&l.length))return _.warn("[passthrough-remuxer.ts]: Failed to generate initSegment."),d;this.emitInitSegment&&(h.tracks=this.initTracks,this.emitInitSegment=!1);const p=lc(f,g),m=oc(g,f),y=m===null?r:m;(tf(u,y,r,p)||h.timescale!==u.timescale&&a)&&(h.initPTS=y-r,u&&u.timescale===1&&_.warn(`Adjusting initPTS by ${h.initPTS-u.baseTime}`),this.initPTS=u={baseTime:h.initPTS,timescale:1});const S=e?y-u.baseTime/u.timescale:c,T=S+p;cc(g,f,u.baseTime/u.timescale),p>0?this.lastEndTime=T:(_.warn("Duration parsed from mp4 should be greater than zero"),this.resetNextTimestamp());const I=!!g.audio,E=!!g.video;let P="";I&&(P+="audio"),E&&(P+="video");const R={data1:f,startPTS:S,startDTS:S,endPTS:T,endDTS:T,type:P,hasAudio:I,hasVideo:E,nb:1,dropped:0};return d.audio=R.type==="audio"?R:void 0,d.video=R.type!=="audio"?R:void 0,d.initSegment=h,d.id3=$o(i,r,u,u),n.samples.length&&(d.text=Go(n,r,u)),d}}function tf(s,e,t,i){if(s===null)return!0;const n=Math.max(i,1),r=e-s.baseTime/s.timescale;return Math.abs(r-t)>n}function jr(s,e){const t=s?.codec;if(t&&t.length>4)return t;if(e===fe.AUDIO){if(t==="ec-3"||t==="ac-3"||t==="alac")return t;if(t==="fLaC"||t==="Opus")return ss(t,!1);const i="mp4a.40.5";return _.info(`Parsed audio codec "${t}" or audio object type not handled. Using "${i}"`),i}return _.warn(`Unhandled video codec "${t}"`),t==="hvc1"||t==="hev1"?"hvc1.1.6.L120.90":t==="av01"?"av01.0.04M.08":"avc1.42e01e"}let ct;try{ct=self.performance.now.bind(self.performance)}catch{_.debug("Unable to use Performance API on this environment"),ct=Zt?.Date.now}const ji=[{demux:$d,remux:ef},{demux:ht,remux:zi},{demux:Bd,remux:zi},{demux:Yd,remux:zi}];ji.splice(2,0,{demux:Gd,remux:zi});class Xr{constructor(e,t,i,n,r){this.async=!1,this.observer=void 0,this.typeSupported=void 0,this.config=void 0,this.vendor=void 0,this.id=void 0,this.demuxer=void 0,this.remuxer=void 0,this.decrypter=void 0,this.probe=void 0,this.decryptionPromise=null,this.transmuxConfig=void 0,this.currentTransmuxState=void 0,this.observer=e,this.typeSupported=t,this.config=i,this.vendor=n,this.id=r}configure(e){this.transmuxConfig=e,this.decrypter&&this.decrypter.reset()}push(e,t,i,n){const r=i.transmuxing;r.executeStart=ct();let a=new Uint8Array(e);const{currentTransmuxState:o,transmuxConfig:l}=this;n&&(this.currentTransmuxState=n);const{contiguous:u,discontinuity:c,trackSwitch:d,accurateTimeOffset:f,timeOffset:h,initSegmentChange:g}=n||o,{audioCodec:p,videoCodec:m,defaultInitPts:y,duration:S,initSegmentData:T}=l,I=sf(a,t);if(I&&I.method==="AES-128"){const w=this.getDecrypter();if(w.isSync()){let b=w.softwareDecrypt(a,I.key.buffer,I.iv.buffer);if(i.part>-1&&(b=w.flush()),!b)return r.executeEnd=ct(),Gs(i);a=new Uint8Array(b)}else return this.decryptionPromise=w.webCryptoDecrypt(a,I.key.buffer,I.iv.buffer).then(b=>{const x=this.push(b,null,i);return this.decryptionPromise=null,x}),this.decryptionPromise}const E=this.needsProbing(c,d);if(E){const w=this.configureTransmuxer(a);if(w)return _.warn(`[transmuxer] ${w.message}`),this.observer.emit(v.ERROR,v.ERROR,{type:re.MEDIA_ERROR,details:O.FRAG_PARSING_ERROR,fatal:!1,error:w,reason:w.message}),r.executeEnd=ct(),Gs(i)}(c||d||g||E)&&this.resetInitSegment(T,p,m,S,t),(c||g||E)&&this.resetInitialTimestamp(y),u||this.resetContiguity();const P=this.transmux(a,I,h,f,i),R=this.currentTransmuxState;return R.contiguous=!0,R.discontinuity=!1,R.trackSwitch=!1,r.executeEnd=ct(),P}flush(e){const t=e.transmuxing;t.executeStart=ct();const{decrypter:i,currentTransmuxState:n,decryptionPromise:r}=this;if(r)return r.then(()=>this.flush(e));const a=[],{timeOffset:o}=n;if(i){const d=i.flush();d&&a.push(this.push(d,null,e))}const{demuxer:l,remuxer:u}=this;if(!l||!u)return t.executeEnd=ct(),[Gs(e)];const c=l.flush(o);return Xi(c)?c.then(d=>(this.flushRemux(a,d,e),a)):(this.flushRemux(a,c,e),a)}flushRemux(e,t,i){const{audioTrack:n,videoTrack:r,id3Track:a,textTrack:o}=t,{accurateTimeOffset:l,timeOffset:u}=this.currentTransmuxState;_.log(`[transmuxer.ts]: Flushed fragment ${i.sn}${i.part>-1?" p: "+i.part:""} of level ${i.level}`);const c=this.remuxer.remux(n,r,a,o,u,l,!0,this.id);e.push({remuxResult:c,chunkMeta:i}),i.transmuxing.executeEnd=ct()}resetInitialTimestamp(e){const{demuxer:t,remuxer:i}=this;!t||!i||(t.resetTimeStamp(e),i.resetTimeStamp(e))}resetContiguity(){const{demuxer:e,remuxer:t}=this;!e||!t||(e.resetContiguity(),t.resetNextTimestamp())}resetInitSegment(e,t,i,n,r){const{demuxer:a,remuxer:o}=this;!a||!o||(a.resetInitSegment(e,t,i,n),o.resetInitSegment(e,t,i,r))}destroy(){this.demuxer&&(this.demuxer.destroy(),this.demuxer=void 0),this.remuxer&&(this.remuxer.destroy(),this.remuxer=void 0)}transmux(e,t,i,n,r){let a;return t&&t.method==="SAMPLE-AES"?a=this.transmuxSampleAes(e,t,i,n,r):a=this.transmuxUnencrypted(e,i,n,r),a}transmuxUnencrypted(e,t,i,n){const{audioTrack:r,videoTrack:a,id3Track:o,textTrack:l}=this.demuxer.demux(e,t,!1,!this.config.progressive);return{remuxResult:this.remuxer.remux(r,a,o,l,t,i,!1,this.id),chunkMeta:n}}transmuxSampleAes(e,t,i,n,r){return this.demuxer.demuxSampleAes(e,t,i).then(a=>({remuxResult:this.remuxer.remux(a.audioTrack,a.videoTrack,a.id3Track,a.textTrack,i,n,!1,this.id),chunkMeta:r}))}configureTransmuxer(e){const{config:t,observer:i,typeSupported:n,vendor:r}=this;let a;for(let f=0,h=ji.length;f<h;f++){var o;if((o=ji[f].demux)!=null&&o.probe(e)){a=ji[f];break}}if(!a)return new Error("Failed to find demuxer by probing fragment data");const l=this.demuxer,u=this.remuxer,c=a.remux,d=a.demux;(!u||!(u instanceof c))&&(this.remuxer=new c(i,t,n,r)),(!l||!(l instanceof d))&&(this.demuxer=new d(i,t,n),this.probe=d.probe)}needsProbing(e,t){return!this.demuxer||!this.remuxer||e||t}getDecrypter(){let e=this.decrypter;return e||(e=this.decrypter=new Fn(this.config)),e}}function sf(s,e){let t=null;return s.byteLength>0&&e?.key!=null&&e.iv!==null&&e.method!=null&&(t=e),t}const Gs=s=>({remuxResult:{},chunkMeta:s});function Xi(s){return"then"in s&&s.then instanceof Function}class nf{constructor(e,t,i,n,r){this.audioCodec=void 0,this.videoCodec=void 0,this.initSegmentData=void 0,this.duration=void 0,this.defaultInitPts=void 0,this.audioCodec=e,this.videoCodec=t,this.initSegmentData=i,this.duration=n,this.defaultInitPts=r||null}}class rf{constructor(e,t,i,n,r,a){this.discontinuity=void 0,this.contiguous=void 0,this.accurateTimeOffset=void 0,this.trackSwitch=void 0,this.timeOffset=void 0,this.initSegmentChange=void 0,this.discontinuity=e,this.contiguous=t,this.accurateTimeOffset=i,this.trackSwitch=n,this.timeOffset=r,this.initSegmentChange=a}}var Vo={exports:{}};(function(s){var e=Object.prototype.hasOwnProperty,t="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(t=!1));function n(l,u,c){this.fn=l,this.context=u,this.once=c||!1}function r(l,u,c,d,f){if(typeof c!="function")throw new TypeError("The listener must be a function");var h=new n(c,d||l,f),g=t?t+u:u;return l._events[g]?l._events[g].fn?l._events[g]=[l._events[g],h]:l._events[g].push(h):(l._events[g]=h,l._eventsCount++),l}function a(l,u){--l._eventsCount===0?l._events=new i:delete l._events[u]}function o(){this._events=new i,this._eventsCount=0}o.prototype.eventNames=function(){var u=[],c,d;if(this._eventsCount===0)return u;for(d in c=this._events)e.call(c,d)&&u.push(t?d.slice(1):d);return Object.getOwnPropertySymbols?u.concat(Object.getOwnPropertySymbols(c)):u},o.prototype.listeners=function(u){var c=t?t+u:u,d=this._events[c];if(!d)return[];if(d.fn)return[d.fn];for(var f=0,h=d.length,g=new Array(h);f<h;f++)g[f]=d[f].fn;return g},o.prototype.listenerCount=function(u){var c=t?t+u:u,d=this._events[c];return d?d.fn?1:d.length:0},o.prototype.emit=function(u,c,d,f,h,g){var p=t?t+u:u;if(!this._events[p])return!1;var m=this._events[p],y=arguments.length,S,T;if(m.fn){switch(m.once&&this.removeListener(u,m.fn,void 0,!0),y){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,c),!0;case 3:return m.fn.call(m.context,c,d),!0;case 4:return m.fn.call(m.context,c,d,f),!0;case 5:return m.fn.call(m.context,c,d,f,h),!0;case 6:return m.fn.call(m.context,c,d,f,h,g),!0}for(T=1,S=new Array(y-1);T<y;T++)S[T-1]=arguments[T];m.fn.apply(m.context,S)}else{var I=m.length,E;for(T=0;T<I;T++)switch(m[T].once&&this.removeListener(u,m[T].fn,void 0,!0),y){case 1:m[T].fn.call(m[T].context);break;case 2:m[T].fn.call(m[T].context,c);break;case 3:m[T].fn.call(m[T].context,c,d);break;case 4:m[T].fn.call(m[T].context,c,d,f);break;default:if(!S)for(E=1,S=new Array(y-1);E<y;E++)S[E-1]=arguments[E];m[T].fn.apply(m[T].context,S)}}return!0},o.prototype.on=function(u,c,d){return r(this,u,c,d,!1)},o.prototype.once=function(u,c,d){return r(this,u,c,d,!0)},o.prototype.removeListener=function(u,c,d,f){var h=t?t+u:u;if(!this._events[h])return this;if(!c)return a(this,h),this;var g=this._events[h];if(g.fn)g.fn===c&&(!f||g.once)&&(!d||g.context===d)&&a(this,h);else{for(var p=0,m=[],y=g.length;p<y;p++)(g[p].fn!==c||f&&!g[p].once||d&&g[p].context!==d)&&m.push(g[p]);m.length?this._events[h]=m.length===1?m[0]:m:a(this,h)}return this},o.prototype.removeAllListeners=function(u){var c;return u?(c=t?t+u:u,this._events[c]&&a(this,c)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=t,o.EventEmitter=o,s.exports=o})(Vo);var af=Vo.exports,$n=Cu(af);class Ko{constructor(e,t,i,n){this.error=null,this.hls=void 0,this.id=void 0,this.observer=void 0,this.frag=null,this.part=null,this.useWorker=void 0,this.workerContext=null,this.onwmsg=void 0,this.transmuxer=null,this.onTransmuxComplete=void 0,this.onFlush=void 0;const r=e.config;this.hls=e,this.id=t,this.useWorker=!!r.enableWorker,this.onTransmuxComplete=i,this.onFlush=n;const a=(u,c)=>{c=c||{},c.frag=this.frag,c.id=this.id,u===v.ERROR&&(this.error=c.error),this.hls.trigger(u,c)};this.observer=new $n,this.observer.on(v.FRAG_DECRYPTED,a),this.observer.on(v.ERROR,a);const o=_t(r.preferManagedMediaSource)||{isTypeSupported:()=>!1},l={mpeg:o.isTypeSupported("audio/mpeg"),mp3:o.isTypeSupported('audio/mp4; codecs="mp3"'),ac3:o.isTypeSupported('audio/mp4; codecs="ac-3"')};if(this.useWorker&&typeof Worker<"u"&&(r.workerPath||bd())){try{r.workerPath?(_.log(`loading Web Worker ${r.workerPath} for "${t}"`),this.workerContext=Rd(r.workerPath)):(_.log(`injecting Web Worker for "${t}"`),this.workerContext=Ld()),this.onwmsg=d=>this.onWorkerMessage(d);const{worker:c}=this.workerContext;c.addEventListener("message",this.onwmsg),c.onerror=d=>{const f=new Error(`${d.message}  (${d.filename}:${d.lineno})`);r.enableWorker=!1,_.warn(`Error in "${t}" Web Worker, fallback to inline`),this.hls.trigger(v.ERROR,{type:re.OTHER_ERROR,details:O.INTERNAL_EXCEPTION,fatal:!1,event:"demuxerWorker",error:f})},c.postMessage({cmd:"init",typeSupported:l,vendor:"",id:t,config:JSON.stringify(r)})}catch(c){_.warn(`Error setting up "${t}" Web Worker, fallback to inline`,c),this.resetWorker(),this.error=null,this.transmuxer=new Xr(this.observer,l,r,"",t)}return}this.transmuxer=new Xr(this.observer,l,r,"",t)}resetWorker(){if(this.workerContext){const{worker:e,objectURL:t}=this.workerContext;t&&self.URL.revokeObjectURL(t),e.removeEventListener("message",this.onwmsg),e.onerror=null,e.terminate(),this.workerContext=null}}destroy(){if(this.workerContext)this.resetWorker(),this.onwmsg=void 0;else{const t=this.transmuxer;t&&(t.destroy(),this.transmuxer=null)}const e=this.observer;e&&e.removeAllListeners(),this.frag=null,this.observer=null,this.hls=null}push(e,t,i,n,r,a,o,l,u,c){var d,f;u.transmuxing.start=self.performance.now();const{transmuxer:h}=this,g=a?a.start:r.start,p=r.decryptdata,m=this.frag,y=!(m&&r.cc===m.cc),S=!(m&&u.level===m.level),T=m?u.sn-m.sn:-1,I=this.part?u.part-this.part.index:-1,E=T===0&&u.id>1&&u.id===m?.stats.chunkCount,P=!S&&(T===1||T===0&&(I===1||E&&I<=0)),R=self.performance.now();(S||T||r.stats.parsing.start===0)&&(r.stats.parsing.start=R),a&&(I||!P)&&(a.stats.parsing.start=R);const w=!(m&&((d=r.initSegment)==null?void 0:d.url)===((f=m.initSegment)==null?void 0:f.url)),b=new rf(y,P,l,S,g,w);if(!P||y||w){_.log(`[transmuxer-interface, ${r.type}]: Starting new transmux session for sn: ${u.sn} p: ${u.part} level: ${u.level} id: ${u.id}
        discontinuity: ${y}
        trackSwitch: ${S}
        contiguous: ${P}
        accurateTimeOffset: ${l}
        timeOffset: ${g}
        initSegmentChange: ${w}`);const x=new nf(i,n,t,o,c);this.configureTransmuxer(x)}if(this.frag=r,this.part=a,this.workerContext)this.workerContext.worker.postMessage({cmd:"demux",data:e,decryptdata:p,chunkMeta:u,state:b},e instanceof ArrayBuffer?[e]:[]);else if(h){const x=h.push(e,p,u,b);Xi(x)?(h.async=!0,x.then(D=>{this.handleTransmuxComplete(D)}).catch(D=>{this.transmuxerError(D,u,"transmuxer-interface push error")})):(h.async=!1,this.handleTransmuxComplete(x))}}flush(e){e.transmuxing.start=self.performance.now();const{transmuxer:t}=this;if(this.workerContext)this.workerContext.worker.postMessage({cmd:"flush",chunkMeta:e});else if(t){let i=t.flush(e);Xi(i)||t.async?(Xi(i)||(i=Promise.resolve(i)),i.then(r=>{this.handleFlushResult(r,e)}).catch(r=>{this.transmuxerError(r,e,"transmuxer-interface flush error")})):this.handleFlushResult(i,e)}}transmuxerError(e,t,i){this.hls&&(this.error=e,this.hls.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.FRAG_PARSING_ERROR,chunkMeta:t,frag:this.frag||void 0,fatal:!1,error:e,err:e,reason:i}))}handleFlushResult(e,t){e.forEach(i=>{this.handleTransmuxComplete(i)}),this.onFlush(t)}onWorkerMessage(e){const t=e.data;if(!(t!=null&&t.event)){_.warn(`worker message received with no ${t?"event name":"data"}`);return}const i=this.hls;if(this.hls)switch(t.event){case"init":{var n;const r=(n=this.workerContext)==null?void 0:n.objectURL;r&&self.URL.revokeObjectURL(r);break}case"transmuxComplete":{this.handleTransmuxComplete(t.data);break}case"flush":{this.onFlush(t.data);break}case"workerLog":_[t.data.logType]&&_[t.data.logType](t.data.message);break;default:{t.data=t.data||{},t.data.frag=this.frag,t.data.id=this.id,i.trigger(t.event,t.data);break}}}configureTransmuxer(e){const{transmuxer:t}=this;this.workerContext?this.workerContext.worker.postMessage({cmd:"configure",config:e}):t&&t.configure(e)}handleTransmuxComplete(e){e.chunkMeta.transmuxing.end=self.performance.now(),this.onTransmuxComplete(e)}}const Qr=100;class of extends On{constructor(e,t,i){super(e,t,i,"[audio-stream-controller]",ne.AUDIO),this.videoBuffer=null,this.videoTrackCC=-1,this.waitingVideoCC=-1,this.bufferedTrack=null,this.switchingTrack=null,this.trackId=-1,this.waitingData=null,this.mainDetails=null,this.flushing=!1,this.bufferFlushed=!1,this.cachedTrackLoadedData=null,this._registerListeners()}onHandlerDestroying(){this._unregisterListeners(),super.onHandlerDestroying(),this.mainDetails=null,this.bufferedTrack=null,this.switchingTrack=null}_registerListeners(){const{hls:e}=this;e.on(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.LEVEL_LOADED,this.onLevelLoaded,this),e.on(v.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),e.on(v.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),e.on(v.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),e.on(v.ERROR,this.onError,this),e.on(v.BUFFER_RESET,this.onBufferReset,this),e.on(v.BUFFER_CREATED,this.onBufferCreated,this),e.on(v.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(v.BUFFER_FLUSHED,this.onBufferFlushed,this),e.on(v.INIT_PTS_FOUND,this.onInitPtsFound,this),e.on(v.FRAG_BUFFERED,this.onFragBuffered,this)}_unregisterListeners(){const{hls:e}=this;e.off(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.LEVEL_LOADED,this.onLevelLoaded,this),e.off(v.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),e.off(v.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),e.off(v.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),e.off(v.ERROR,this.onError,this),e.off(v.BUFFER_RESET,this.onBufferReset,this),e.off(v.BUFFER_CREATED,this.onBufferCreated,this),e.off(v.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(v.BUFFER_FLUSHED,this.onBufferFlushed,this),e.off(v.INIT_PTS_FOUND,this.onInitPtsFound,this),e.off(v.FRAG_BUFFERED,this.onFragBuffered,this)}onInitPtsFound(e,{frag:t,id:i,initPTS:n,timescale:r}){if(i==="main"){const a=t.cc;this.initPTS[t.cc]={baseTime:n,timescale:r},this.log(`InitPTS for cc: ${a} found from main: ${n}`),this.videoTrackCC=a,this.state===G.WAITING_INIT_PTS&&this.tick()}}startLoad(e){if(!this.levels){this.startPosition=e,this.state=G.STOPPED;return}const t=this.lastCurrentTime;this.stopLoad(),this.setInterval(Qr),t>0&&e===-1?(this.log(`Override startPosition with lastCurrentTime @${t.toFixed(3)}`),e=t,this.state=G.IDLE):(this.loadedmetadata=!1,this.state=G.WAITING_TRACK),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=e,this.tick()}doTick(){switch(this.state){case G.IDLE:this.doTickIdle();break;case G.WAITING_TRACK:{var e;const{levels:i,trackId:n}=this,r=i==null||(e=i[n])==null?void 0:e.details;if(r){if(this.waitForCdnTuneIn(r))break;this.state=G.WAITING_INIT_PTS}break}case G.FRAG_LOADING_WAITING_RETRY:{var t;const i=performance.now(),n=this.retryDate;if(!n||i>=n||(t=this.media)!=null&&t.seeking){const{levels:r,trackId:a}=this;this.log("RetryDate reached, switch back to IDLE state"),this.resetStartWhenNotLoaded(r?.[a]||null),this.state=G.IDLE}break}case G.WAITING_INIT_PTS:{const i=this.waitingData;if(i){const{frag:n,part:r,cache:a,complete:o}=i;if(this.initPTS[n.cc]!==void 0){this.waitingData=null,this.waitingVideoCC=-1,this.state=G.FRAG_LOADING;const l=a.flush(),u={frag:n,part:r,payload:l,networkDetails:null};this._handleFragmentLoadProgress(u),o&&super._handleFragmentLoadComplete(u)}else if(this.videoTrackCC!==this.waitingVideoCC)this.log(`Waiting fragment cc (${n.cc}) cancelled because video is at cc ${this.videoTrackCC}`),this.clearWaitingFragment();else{const l=this.getLoadPosition(),u=me.bufferInfo(this.mediaBuffer,l,this.config.maxBufferHole);mn(u.end,this.config.maxFragLookUpTolerance,n)<0&&(this.log(`Waiting fragment cc (${n.cc}) @ ${n.start} cancelled because another fragment at ${u.end} is needed`),this.clearWaitingFragment())}}else this.state=G.IDLE}}this.onTickEnd()}clearWaitingFragment(){const e=this.waitingData;e&&(this.fragmentTracker.removeFragment(e.frag),this.waitingData=null,this.waitingVideoCC=-1,this.state=G.IDLE)}resetLoadingState(){this.clearWaitingFragment(),super.resetLoadingState()}onTickEnd(){const{media:e}=this;e!=null&&e.readyState&&(this.lastCurrentTime=e.currentTime)}doTickIdle(){const{hls:e,levels:t,media:i,trackId:n}=this,r=e.config;if(!this.buffering||!i&&(this.startFragRequested||!r.startFragPrefetch)||!(t!=null&&t[n]))return;const a=t[n],o=a.details;if(!o||o.live&&this.levelLastLoaded!==a||this.waitForCdnTuneIn(o)){this.state=G.WAITING_TRACK;return}const l=this.mediaBuffer?this.mediaBuffer:this.media;this.bufferFlushed&&l&&(this.bufferFlushed=!1,this.afterBufferFlushed(l,fe.AUDIO,ne.AUDIO));const u=this.getFwdBufferInfo(l,ne.AUDIO);if(u===null)return;if(!this.switchingTrack&&this._streamEnded(u,o)){e.trigger(v.BUFFER_EOS,{type:"audio"}),this.state=G.ENDED;return}const c=this.getFwdBufferInfo(this.videoBuffer?this.videoBuffer:this.media,ne.MAIN),d=u.len,f=this.getMaxBufferLength(c?.len),h=o.fragments,g=h[0].start,p=this.getLoadPosition(),m=this.flushing?p:u.end;if(this.switchingTrack&&i){const I=p;o.PTSKnown&&I<g&&(u.end>g||u.nextStart)&&(this.log("Alt audio track ahead of main track, seek to start of alt audio track"),i.currentTime=g+.05)}if(d>=f&&!this.switchingTrack&&m<h[h.length-1].start)return;let y=this.getNextFragment(m,o),S=!1;if(y&&this.isLoopLoading(y,m)&&(S=!!y.gap,y=this.getNextFragmentLoopLoading(y,o,u,ne.MAIN,f)),!y){this.bufferFlushed=!0;return}const T=c&&y.start>c.end+o.targetduration;if(T||!(c!=null&&c.len)&&u.len){const I=this.getAppendedFrag(y.start,ne.MAIN);if(I===null||(S||(S=!!I.gap||!!T&&c.len===0),T&&!S||S&&u.nextStart&&u.nextStart<I.end))return}this.loadFragment(y,a,m)}getMaxBufferLength(e){const t=super.getMaxBufferLength();return e?Math.min(Math.max(t,e),this.config.maxMaxBufferLength):t}onMediaDetaching(){this.videoBuffer=null,this.bufferFlushed=this.flushing=!1,super.onMediaDetaching()}onAudioTracksUpdated(e,{audioTracks:t}){this.resetTransmuxer(),this.levels=t.map(i=>new Jt(i))}onAudioTrackSwitching(e,t){const i=!!t.url;this.trackId=t.id;const{fragCurrent:n}=this;n&&(n.abortRequests(),this.removeUnbufferedFrags(n.start)),this.resetLoadingState(),i?this.setInterval(Qr):this.resetTransmuxer(),i?(this.switchingTrack=t,this.state=G.IDLE,this.flushAudioIfNeeded(t)):(this.switchingTrack=null,this.bufferedTrack=t,this.state=G.STOPPED),this.tick()}onManifestLoading(){this.fragmentTracker.removeAllFragments(),this.startPosition=this.lastCurrentTime=0,this.bufferFlushed=this.flushing=!1,this.levels=this.mainDetails=this.waitingData=this.bufferedTrack=this.cachedTrackLoadedData=this.switchingTrack=null,this.startFragRequested=!1,this.trackId=this.videoTrackCC=this.waitingVideoCC=-1}onLevelLoaded(e,t){this.mainDetails=t.details,this.cachedTrackLoadedData!==null&&(this.hls.trigger(v.AUDIO_TRACK_LOADED,this.cachedTrackLoadedData),this.cachedTrackLoadedData=null)}onAudioTrackLoaded(e,t){var i;if(this.mainDetails==null){this.cachedTrackLoadedData=t;return}const{levels:n}=this,{details:r,id:a}=t;if(!n){this.warn(`Audio tracks were reset while loading level ${a}`);return}this.log(`Audio track ${a} loaded [${r.startSN},${r.endSN}]${r.lastPartSn?`[part-${r.lastPartSn}-${r.lastPartIndex}]`:""},duration:${r.totalduration}`);const o=n[a];let l=0;if(r.live||(i=o.details)!=null&&i.live){this.checkLiveUpdate(r);const c=this.mainDetails;if(r.deltaUpdateFailed||!c)return;if(!o.details&&r.hasProgramDateTime&&c.hasProgramDateTime)ls(r,c),l=r.fragments[0].start;else{var u;l=this.alignPlaylists(r,o.details,(u=this.levelLastLoaded)==null?void 0:u.details)}}o.details=r,this.levelLastLoaded=o,!this.startFragRequested&&(this.mainDetails||!r.live)&&this.setStartPosition(this.mainDetails||r,l),this.state===G.WAITING_TRACK&&!this.waitForCdnTuneIn(r)&&(this.state=G.IDLE),this.tick()}_handleFragmentLoadProgress(e){var t;const{frag:i,part:n,payload:r}=e,{config:a,trackId:o,levels:l}=this;if(!l){this.warn(`Audio tracks were reset while fragment load was in progress. Fragment ${i.sn} of level ${i.level} will not be buffered`);return}const u=l[o];if(!u){this.warn("Audio track is undefined on fragment load progress");return}const c=u.details;if(!c){this.warn("Audio track details undefined on fragment load progress"),this.removeUnbufferedFrags(i.start);return}const d=a.defaultAudioCodec||u.audioCodec||"mp4a.40.2";let f=this.transmuxer;f||(f=this.transmuxer=new Ko(this.hls,ne.AUDIO,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)));const h=this.initPTS[i.cc],g=(t=i.initSegment)==null?void 0:t.data;if(h!==void 0){const m=n?n.index:-1,y=m!==-1,S=new Pn(i.level,i.sn,i.stats.chunkCount,r.byteLength,m,y);f.push(r,g,d,"",i,n,c.totalduration,!1,S,h)}else{this.log(`Unknown video PTS for cc ${i.cc}, waiting for video PTS before demuxing audio frag ${i.sn} of [${c.startSN} ,${c.endSN}],track ${o}`);const{cache:p}=this.waitingData=this.waitingData||{frag:i,part:n,cache:new Io,complete:!1};p.push(new Uint8Array(r)),this.waitingVideoCC=this.videoTrackCC,this.state=G.WAITING_INIT_PTS}}_handleFragmentLoadComplete(e){if(this.waitingData){this.waitingData.complete=!0;return}super._handleFragmentLoadComplete(e)}onBufferReset(){this.mediaBuffer=this.videoBuffer=null,this.loadedmetadata=!1}onBufferCreated(e,t){const i=t.tracks.audio;i&&(this.mediaBuffer=i.buffer||null),t.tracks.video&&(this.videoBuffer=t.tracks.video.buffer||null)}onFragBuffered(e,t){const{frag:i,part:n}=t;if(i.type!==ne.AUDIO){if(!this.loadedmetadata&&i.type===ne.MAIN){const r=this.videoBuffer||this.media;r&&me.getBuffered(r).length&&(this.loadedmetadata=!0)}return}if(this.fragContextChanged(i)){this.warn(`Fragment ${i.sn}${n?" p: "+n.index:""} of level ${i.level} finished buffering, but was aborted. state: ${this.state}, audioSwitch: ${this.switchingTrack?this.switchingTrack.name:"false"}`);return}if(i.sn!=="initSegment"){this.fragPrevious=i;const r=this.switchingTrack;r&&(this.bufferedTrack=r,this.switchingTrack=null,this.hls.trigger(v.AUDIO_TRACK_SWITCHED,Re({},r)))}this.fragBufferedComplete(i,n)}onError(e,t){var i;if(t.fatal){this.state=G.ERROR;return}switch(t.details){case O.FRAG_GAP:case O.FRAG_PARSING_ERROR:case O.FRAG_DECRYPT_ERROR:case O.FRAG_LOAD_ERROR:case O.FRAG_LOAD_TIMEOUT:case O.KEY_LOAD_ERROR:case O.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(ne.AUDIO,t);break;case O.AUDIO_TRACK_LOAD_ERROR:case O.AUDIO_TRACK_LOAD_TIMEOUT:case O.LEVEL_PARSING_ERROR:!t.levelRetry&&this.state===G.WAITING_TRACK&&((i=t.context)==null?void 0:i.type)===ce.AUDIO_TRACK&&(this.state=G.IDLE);break;case O.BUFFER_APPEND_ERROR:case O.BUFFER_FULL_ERROR:if(!t.parent||t.parent!=="audio")return;if(t.details===O.BUFFER_APPEND_ERROR){this.resetLoadingState();return}this.reduceLengthAndFlushBuffer(t)&&(this.bufferedTrack=null,super.flushMainBuffer(0,Number.POSITIVE_INFINITY,"audio"));break;case O.INTERNAL_EXCEPTION:this.recoverWorkerError(t);break}}onBufferFlushing(e,{type:t}){t!==fe.VIDEO&&(this.flushing=!0)}onBufferFlushed(e,{type:t}){if(t!==fe.VIDEO){this.flushing=!1,this.bufferFlushed=!0,this.state===G.ENDED&&(this.state=G.IDLE);const i=this.mediaBuffer||this.media;i&&(this.afterBufferFlushed(i,t,ne.AUDIO),this.tick())}}_handleTransmuxComplete(e){var t;const i="audio",{hls:n}=this,{remuxResult:r,chunkMeta:a}=e,o=this.getCurrentContext(a);if(!o){this.resetWhenMissingContext(a);return}const{frag:l,part:u,level:c}=o,{details:d}=c,{audio:f,text:h,id3:g,initSegment:p}=r;if(this.fragContextChanged(l)||!d){this.fragmentTracker.removeFragment(l);return}if(this.state=G.PARSING,this.switchingTrack&&f&&this.completeAudioSwitch(this.switchingTrack),p!=null&&p.tracks){const m=l.initSegment||l;this._bufferInitSegment(c,p.tracks,m,a),n.trigger(v.FRAG_PARSING_INIT_SEGMENT,{frag:m,id:i,tracks:p.tracks})}if(f){const{startPTS:m,endPTS:y,startDTS:S,endDTS:T}=f;u&&(u.elementaryStreams[fe.AUDIO]={startPTS:m,endPTS:y,startDTS:S,endDTS:T}),l.setElementaryStreamInfo(fe.AUDIO,m,y,S,T),this.bufferFragmentData(f,l,u,a)}if(g!=null&&(t=g.samples)!=null&&t.length){const m=xe({id:i,frag:l,details:d},g);n.trigger(v.FRAG_PARSING_METADATA,m)}if(h){const m=xe({id:i,frag:l,details:d},h);n.trigger(v.FRAG_PARSING_USERDATA,m)}}_bufferInitSegment(e,t,i,n){if(this.state!==G.PARSING)return;t.video&&delete t.video;const r=t.audio;if(!r)return;r.id="audio";const a=e.audioCodec;this.log(`Init audio buffer, container:${r.container}, codecs[level/parsed]=[${a}/${r.codec}]`),a&&a.split(",").length===1&&(r.levelCodec=a),this.hls.trigger(v.BUFFER_CODECS,t);const o=r.initSegment;if(o!=null&&o.byteLength){const l={type:"audio",frag:i,part:null,chunkMeta:n,parent:i.type,data:o};this.hls.trigger(v.BUFFER_APPENDING,l)}this.tickImmediate()}loadFragment(e,t,i){const n=this.fragmentTracker.getState(e);if(this.fragCurrent=e,this.switchingTrack||n===Le.NOT_LOADED||n===Le.PARTIAL){var r;if(e.sn==="initSegment")this._loadInitSegment(e,t);else if((r=t.details)!=null&&r.live&&!this.initPTS[e.cc]){this.log(`Waiting for video PTS in continuity counter ${e.cc} of live stream before loading audio fragment ${e.sn} of level ${this.trackId}`),this.state=G.WAITING_INIT_PTS;const a=this.mainDetails;a&&a.fragments[0].start!==t.details.fragments[0].start&&ls(t.details,a)}else this.startFragRequested=!0,super.loadFragment(e,t,i)}else this.clearTrackerIfNeeded(e)}flushAudioIfNeeded(e){if(this.media&&this.bufferedTrack){const{name:t,lang:i,assocLang:n,characteristics:r,audioCodec:a,channels:o}=this.bufferedTrack;Ct({name:t,lang:i,assocLang:n,characteristics:r,audioCodec:a,channels:o},e,Et)||(this.log("Switching audio track : flushing all audio"),super.flushMainBuffer(0,Number.POSITIVE_INFINITY,"audio"),this.bufferedTrack=null)}}completeAudioSwitch(e){const{hls:t}=this;this.flushAudioIfNeeded(e),this.bufferedTrack=e,this.switchingTrack=null,t.trigger(v.AUDIO_TRACK_SWITCHED,Re({},e))}}function Ho(s,e){if(s.length!==e.length)return!1;for(let t=0;t<s.length;t++)if(!mi(s[t].attrs,e[t].attrs))return!1;return!0}function mi(s,e,t){const i=s["STABLE-RENDITION-ID"];return i&&!t?i===e["STABLE-RENDITION-ID"]:!(t||["LANGUAGE","NAME","CHARACTERISTICS","AUTOSELECT","DEFAULT","FORCED","ASSOC-LANGUAGE"]).some(n=>s[n]!==e[n])}function vn(s,e){return e.label.toLowerCase()===s.name.toLowerCase()&&(!e.language||e.language.toLowerCase()===(s.lang||"").toLowerCase())}class lf extends _n{constructor(e){super(e,"[audio-track-controller]"),this.tracks=[],this.groupIds=null,this.tracksInGroup=[],this.trackId=-1,this.currentTrack=null,this.selectDefaultTrack=!0,this.registerListeners()}registerListeners(){const{hls:e}=this;e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.MANIFEST_PARSED,this.onManifestParsed,this),e.on(v.LEVEL_LOADING,this.onLevelLoading,this),e.on(v.LEVEL_SWITCHING,this.onLevelSwitching,this),e.on(v.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),e.on(v.ERROR,this.onError,this)}unregisterListeners(){const{hls:e}=this;e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.MANIFEST_PARSED,this.onManifestParsed,this),e.off(v.LEVEL_LOADING,this.onLevelLoading,this),e.off(v.LEVEL_SWITCHING,this.onLevelSwitching,this),e.off(v.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),e.off(v.ERROR,this.onError,this)}destroy(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.currentTrack=null,super.destroy()}onManifestLoading(){this.tracks=[],this.tracksInGroup=[],this.groupIds=null,this.currentTrack=null,this.trackId=-1,this.selectDefaultTrack=!0}onManifestParsed(e,t){this.tracks=t.audioTracks||[]}onAudioTrackLoaded(e,t){const{id:i,groupId:n,details:r}=t,a=this.tracksInGroup[i];if(!a||a.groupId!==n){this.warn(`Audio track with id:${i} and group:${n} not found in active group ${a?.groupId}`);return}const o=a.details;a.details=t.details,this.log(`Audio track ${i} "${a.name}" lang:${a.lang} group:${n} loaded [${r.startSN}-${r.endSN}]`),i===this.trackId&&this.playlistLoaded(i,t,o)}onLevelLoading(e,t){this.switchLevel(t.level)}onLevelSwitching(e,t){this.switchLevel(t.level)}switchLevel(e){const t=this.hls.levels[e];if(!t)return;const i=t.audioGroups||null,n=this.groupIds;let r=this.currentTrack;if(!i||n?.length!==i?.length||i!=null&&i.some(o=>n?.indexOf(o)===-1)){this.groupIds=i,this.trackId=-1,this.currentTrack=null;const o=this.tracks.filter(f=>!i||i.indexOf(f.groupId)!==-1);if(o.length)this.selectDefaultTrack&&!o.some(f=>f.default)&&(this.selectDefaultTrack=!1),o.forEach((f,h)=>{f.id=h});else if(!r&&!this.tracksInGroup.length)return;this.tracksInGroup=o;const l=this.hls.config.audioPreference;if(!r&&l){const f=st(l,o,Et);if(f>-1)r=o[f];else{const h=st(l,this.tracks);r=this.tracks[h]}}let u=this.findTrackId(r);u===-1&&r&&(u=this.findTrackId(null));const c={audioTracks:o};this.log(`Updating audio tracks, ${o.length} track(s) found in group(s): ${i?.join(",")}`),this.hls.trigger(v.AUDIO_TRACKS_UPDATED,c);const d=this.trackId;if(u!==-1&&d===-1)this.setAudioTrack(u);else if(o.length&&d===-1){var a;const f=new Error(`No audio track selected for current audio group-ID(s): ${(a=this.groupIds)==null?void 0:a.join(",")} track count: ${o.length}`);this.warn(f.message),this.hls.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.AUDIO_TRACK_LOAD_ERROR,fatal:!0,error:f})}}else this.shouldReloadPlaylist(r)&&this.setAudioTrack(this.trackId)}onError(e,t){t.fatal||!t.context||t.context.type===ce.AUDIO_TRACK&&t.context.id===this.trackId&&(!this.groupIds||this.groupIds.indexOf(t.context.groupId)!==-1)&&(this.requestScheduled=-1,this.checkRetry(t))}get allAudioTracks(){return this.tracks}get audioTracks(){return this.tracksInGroup}get audioTrack(){return this.trackId}set audioTrack(e){this.selectDefaultTrack=!1,this.setAudioTrack(e)}setAudioOption(e){const t=this.hls;if(t.config.audioPreference=e,e){const i=this.allAudioTracks;if(this.selectDefaultTrack=!1,i.length){const n=this.currentTrack;if(n&&Ct(e,n,Et))return n;const r=st(e,this.tracksInGroup,Et);if(r>-1){const a=this.tracksInGroup[r];return this.setAudioTrack(r),a}else if(n){let a=t.loadLevel;a===-1&&(a=t.firstAutoLevel);const o=od(e,t.levels,i,a,Et);if(o===-1)return null;t.nextLoadLevel=o}if(e.channels||e.audioCodec){const a=st(e,i);if(a>-1)return i[a]}}}return null}setAudioTrack(e){const t=this.tracksInGroup;if(e<0||e>=t.length){this.warn(`Invalid audio track id: ${e}`);return}this.clearTimer(),this.selectDefaultTrack=!1;const i=this.currentTrack,n=t[e],r=n.details&&!n.details.live;if(e===this.trackId&&n===i&&r||(this.log(`Switching to audio-track ${e} "${n.name}" lang:${n.lang} group:${n.groupId} channels:${n.channels}`),this.trackId=e,this.currentTrack=n,this.hls.trigger(v.AUDIO_TRACK_SWITCHING,Re({},n)),r))return;const a=this.switchParams(n.url,i?.details,n.details);this.loadPlaylist(a)}findTrackId(e){const t=this.tracksInGroup;for(let i=0;i<t.length;i++){const n=t[i];if(!(this.selectDefaultTrack&&!n.default)&&(!e||Ct(e,n,Et)))return i}if(e){const{name:i,lang:n,assocLang:r,characteristics:a,audioCodec:o,channels:l}=e;for(let u=0;u<t.length;u++){const c=t[u];if(Ct({name:i,lang:n,assocLang:r,characteristics:a,audioCodec:o,channels:l},c,Et))return u}for(let u=0;u<t.length;u++){const c=t[u];if(mi(e.attrs,c.attrs,["LANGUAGE","ASSOC-LANGUAGE","CHARACTERISTICS"]))return u}for(let u=0;u<t.length;u++){const c=t[u];if(mi(e.attrs,c.attrs,["LANGUAGE"]))return u}}return-1}loadPlaylist(e){const t=this.currentTrack;if(this.shouldLoadPlaylist(t)&&t){super.loadPlaylist();const i=t.id,n=t.groupId;let r=t.url;if(e)try{r=e.addDirectives(r)}catch(a){this.warn(`Could not construct new URL with HLS Delivery Directives: ${a}`)}this.log(`loading audio-track playlist ${i} "${t.name}" lang:${t.lang} group:${n}`),this.clearTimer(),this.hls.trigger(v.AUDIO_TRACK_LOADING,{url:r,id:i,groupId:n,deliveryDirectives:e||null})}}}const Zr=500;class uf extends On{constructor(e,t,i){super(e,t,i,"[subtitle-stream-controller]",ne.SUBTITLE),this.currentTrackId=-1,this.tracksBuffered=[],this.mainDetails=null,this._registerListeners()}onHandlerDestroying(){this._unregisterListeners(),super.onHandlerDestroying(),this.mainDetails=null}_registerListeners(){const{hls:e}=this;e.on(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.LEVEL_LOADED,this.onLevelLoaded,this),e.on(v.ERROR,this.onError,this),e.on(v.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),e.on(v.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),e.on(v.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),e.on(v.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),e.on(v.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(v.FRAG_BUFFERED,this.onFragBuffered,this)}_unregisterListeners(){const{hls:e}=this;e.off(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.LEVEL_LOADED,this.onLevelLoaded,this),e.off(v.ERROR,this.onError,this),e.off(v.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),e.off(v.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),e.off(v.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),e.off(v.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),e.off(v.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(v.FRAG_BUFFERED,this.onFragBuffered,this)}startLoad(e){this.stopLoad(),this.state=G.IDLE,this.setInterval(Zr),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=e,this.tick()}onManifestLoading(){this.mainDetails=null,this.fragmentTracker.removeAllFragments()}onMediaDetaching(){this.tracksBuffered=[],super.onMediaDetaching()}onLevelLoaded(e,t){this.mainDetails=t.details}onSubtitleFragProcessed(e,t){const{frag:i,success:n}=t;if(this.fragPrevious=i,this.state=G.IDLE,!n)return;const r=this.tracksBuffered[this.currentTrackId];if(!r)return;let a;const o=i.start;for(let u=0;u<r.length;u++)if(o>=r[u].start&&o<=r[u].end){a=r[u];break}const l=i.start+i.duration;a?a.end=l:(a={start:o,end:l},r.push(a)),this.fragmentTracker.fragBuffered(i),this.fragBufferedComplete(i,null)}onBufferFlushing(e,t){const{startOffset:i,endOffset:n}=t;if(i===0&&n!==Number.POSITIVE_INFINITY){const r=n-1;if(r<=0)return;t.endOffsetSubtitles=Math.max(0,r),this.tracksBuffered.forEach(a=>{for(let o=0;o<a.length;){if(a[o].end<=r){a.shift();continue}else if(a[o].start<r)a[o].start=r;else break;o++}}),this.fragmentTracker.removeFragmentsInRange(i,r,ne.SUBTITLE)}}onFragBuffered(e,t){if(!this.loadedmetadata&&t.frag.type===ne.MAIN){var i;(i=this.media)!=null&&i.buffered.length&&(this.loadedmetadata=!0)}}onError(e,t){const i=t.frag;i?.type===ne.SUBTITLE&&(t.details===O.FRAG_GAP&&this.fragmentTracker.fragBuffered(i,!0),this.fragCurrent&&this.fragCurrent.abortRequests(),this.state!==G.STOPPED&&(this.state=G.IDLE))}onSubtitleTracksUpdated(e,{subtitleTracks:t}){if(this.levels&&Ho(this.levels,t)){this.levels=t.map(i=>new Jt(i));return}this.tracksBuffered=[],this.levels=t.map(i=>{const n=new Jt(i);return this.tracksBuffered[n.id]=[],n}),this.fragmentTracker.removeFragmentsInRange(0,Number.POSITIVE_INFINITY,ne.SUBTITLE),this.fragPrevious=null,this.mediaBuffer=null}onSubtitleTrackSwitch(e,t){var i;if(this.currentTrackId=t.id,!((i=this.levels)!=null&&i.length)||this.currentTrackId===-1){this.clearInterval();return}const n=this.levels[this.currentTrackId];n!=null&&n.details?this.mediaBuffer=this.mediaBufferTimeRanges:this.mediaBuffer=null,n&&this.setInterval(Zr)}onSubtitleTrackLoaded(e,t){var i;const{currentTrackId:n,levels:r}=this,{details:a,id:o}=t;if(!r){this.warn(`Subtitle tracks were reset while loading level ${o}`);return}const l=r[o];if(o>=r.length||!l)return;this.log(`Subtitle track ${o} loaded [${a.startSN},${a.endSN}]${a.lastPartSn?`[part-${a.lastPartSn}-${a.lastPartIndex}]`:""},duration:${a.totalduration}`),this.mediaBuffer=this.mediaBufferTimeRanges;let u=0;if(a.live||(i=l.details)!=null&&i.live){const d=this.mainDetails;if(a.deltaUpdateFailed||!d)return;const f=d.fragments[0];if(!l.details)a.hasProgramDateTime&&d.hasProgramDateTime?(ls(a,d),u=a.fragments[0].start):f&&(u=f.start,gn(a,u));else{var c;u=this.alignPlaylists(a,l.details,(c=this.levelLastLoaded)==null?void 0:c.details),u===0&&f&&(u=f.start,gn(a,u))}}l.details=a,this.levelLastLoaded=l,o===n&&(!this.startFragRequested&&(this.mainDetails||!a.live)&&this.setStartPosition(this.mainDetails||a,u),this.tick(),a.live&&!this.fragCurrent&&this.media&&this.state===G.IDLE&&(os(null,a.fragments,this.media.currentTime,0)||(this.warn("Subtitle playlist not aligned with playback"),l.details=void 0)))}_handleFragmentLoadComplete(e){const{frag:t,payload:i}=e,n=t.decryptdata,r=this.hls;if(!this.fragContextChanged(t)&&i&&i.byteLength>0&&n!=null&&n.key&&n.iv&&n.method==="AES-128"){const a=performance.now();this.decrypter.decrypt(new Uint8Array(i),n.key.buffer,n.iv.buffer).catch(o=>{throw r.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.FRAG_DECRYPT_ERROR,fatal:!1,error:o,reason:o.message,frag:t}),o}).then(o=>{const l=performance.now();r.trigger(v.FRAG_DECRYPTED,{frag:t,payload:o,stats:{tstart:a,tdecrypt:l}})}).catch(o=>{this.warn(`${o.name}: ${o.message}`),this.state=G.IDLE})}}doTick(){if(!this.media){this.state=G.IDLE;return}if(this.state===G.IDLE){const{currentTrackId:e,levels:t}=this,i=t?.[e];if(!i||!t.length||!i.details)return;const{config:n}=this,r=this.getLoadPosition(),a=me.bufferedInfo(this.tracksBuffered[this.currentTrackId]||[],r,n.maxBufferHole),{end:o,len:l}=a,u=this.getFwdBufferInfo(this.media,ne.MAIN),c=i.details,d=this.getMaxBufferLength(u?.len)+c.levelTargetDuration;if(l>d)return;const f=c.fragments,h=f.length,g=c.edge;let p=null;const m=this.fragPrevious;if(o<g){const y=n.maxFragLookUpTolerance,S=o>g-y?0:y;p=os(m,f,Math.max(f[0].start,o),S),!p&&m&&m.start<f[0].start&&(p=f[0])}else p=f[h-1];if(!p)return;if(p=this.mapToInitFragWhenRequired(p),p.sn!=="initSegment"){const y=p.sn-c.startSN,S=f[y-1];S&&S.cc===p.cc&&this.fragmentTracker.getState(S)===Le.NOT_LOADED&&(p=S)}this.fragmentTracker.getState(p)===Le.NOT_LOADED&&this.loadFragment(p,i,o)}}getMaxBufferLength(e){const t=super.getMaxBufferLength();return e?Math.max(t,e):t}loadFragment(e,t,i){this.fragCurrent=e,e.sn==="initSegment"?this._loadInitSegment(e,t):(this.startFragRequested=!0,super.loadFragment(e,t,i))}get mediaBufferTimeRanges(){return new cf(this.tracksBuffered[this.currentTrackId]||[])}}class cf{constructor(e){this.buffered=void 0;const t=(i,n,r)=>{if(n=n>>>0,n>r-1)throw new DOMException(`Failed to execute '${i}' on 'TimeRanges': The index provided (${n}) is greater than the maximum bound (${r})`);return e[n][i]};this.buffered={get length(){return e.length},end(i){return t("end",i,e.length)},start(i){return t("start",i,e.length)}}}}class df extends _n{constructor(e){super(e,"[subtitle-track-controller]"),this.media=null,this.tracks=[],this.groupIds=null,this.tracksInGroup=[],this.trackId=-1,this.currentTrack=null,this.selectDefaultTrack=!0,this.queuedDefaultTrack=-1,this.asyncPollTrackChange=()=>this.pollTrackChange(0),this.useTextTrackPolling=!1,this.subtitlePollingInterval=-1,this._subtitleDisplay=!0,this.onTextTracksChanged=()=>{if(this.useTextTrackPolling||self.clearInterval(this.subtitlePollingInterval),!this.media||!this.hls.config.renderTextTracksNatively)return;let t=null;const i=qi(this.media.textTracks);for(let r=0;r<i.length;r++)if(i[r].mode==="hidden")t=i[r];else if(i[r].mode==="showing"){t=i[r];break}const n=this.findTrackForTextTrack(t);this.subtitleTrack!==n&&this.setSubtitleTrack(n)},this.registerListeners()}destroy(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.currentTrack=null,this.onTextTracksChanged=this.asyncPollTrackChange=null,super.destroy()}get subtitleDisplay(){return this._subtitleDisplay}set subtitleDisplay(e){this._subtitleDisplay=e,this.trackId>-1&&this.toggleTrackModes()}registerListeners(){const{hls:e}=this;e.on(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.MANIFEST_PARSED,this.onManifestParsed,this),e.on(v.LEVEL_LOADING,this.onLevelLoading,this),e.on(v.LEVEL_SWITCHING,this.onLevelSwitching,this),e.on(v.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),e.on(v.ERROR,this.onError,this)}unregisterListeners(){const{hls:e}=this;e.off(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.MANIFEST_PARSED,this.onManifestParsed,this),e.off(v.LEVEL_LOADING,this.onLevelLoading,this),e.off(v.LEVEL_SWITCHING,this.onLevelSwitching,this),e.off(v.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),e.off(v.ERROR,this.onError,this)}onMediaAttached(e,t){this.media=t.media,this.media&&(this.queuedDefaultTrack>-1&&(this.subtitleTrack=this.queuedDefaultTrack,this.queuedDefaultTrack=-1),this.useTextTrackPolling=!(this.media.textTracks&&"onchange"in this.media.textTracks),this.useTextTrackPolling?this.pollTrackChange(500):this.media.textTracks.addEventListener("change",this.asyncPollTrackChange))}pollTrackChange(e){self.clearInterval(this.subtitlePollingInterval),this.subtitlePollingInterval=self.setInterval(this.onTextTracksChanged,e)}onMediaDetaching(){if(!this.media)return;self.clearInterval(this.subtitlePollingInterval),this.useTextTrackPolling||this.media.textTracks.removeEventListener("change",this.asyncPollTrackChange),this.trackId>-1&&(this.queuedDefaultTrack=this.trackId),qi(this.media.textTracks).forEach(t=>{zt(t)}),this.subtitleTrack=-1,this.media=null}onManifestLoading(){this.tracks=[],this.groupIds=null,this.tracksInGroup=[],this.trackId=-1,this.currentTrack=null,this.selectDefaultTrack=!0}onManifestParsed(e,t){this.tracks=t.subtitleTracks}onSubtitleTrackLoaded(e,t){const{id:i,groupId:n,details:r}=t,a=this.tracksInGroup[i];if(!a||a.groupId!==n){this.warn(`Subtitle track with id:${i} and group:${n} not found in active group ${a?.groupId}`);return}const o=a.details;a.details=t.details,this.log(`Subtitle track ${i} "${a.name}" lang:${a.lang} group:${n} loaded [${r.startSN}-${r.endSN}]`),i===this.trackId&&this.playlistLoaded(i,t,o)}onLevelLoading(e,t){this.switchLevel(t.level)}onLevelSwitching(e,t){this.switchLevel(t.level)}switchLevel(e){const t=this.hls.levels[e];if(!t)return;const i=t.subtitleGroups||null,n=this.groupIds;let r=this.currentTrack;if(!i||n?.length!==i?.length||i!=null&&i.some(a=>n?.indexOf(a)===-1)){this.groupIds=i,this.trackId=-1,this.currentTrack=null;const a=this.tracks.filter(c=>!i||i.indexOf(c.groupId)!==-1);if(a.length)this.selectDefaultTrack&&!a.some(c=>c.default)&&(this.selectDefaultTrack=!1),a.forEach((c,d)=>{c.id=d});else if(!r&&!this.tracksInGroup.length)return;this.tracksInGroup=a;const o=this.hls.config.subtitlePreference;if(!r&&o){this.selectDefaultTrack=!1;const c=st(o,a);if(c>-1)r=a[c];else{const d=st(o,this.tracks);r=this.tracks[d]}}let l=this.findTrackId(r);l===-1&&r&&(l=this.findTrackId(null));const u={subtitleTracks:a};this.log(`Updating subtitle tracks, ${a.length} track(s) found in "${i?.join(",")}" group-id`),this.hls.trigger(v.SUBTITLE_TRACKS_UPDATED,u),l!==-1&&this.trackId===-1&&this.setSubtitleTrack(l)}else this.shouldReloadPlaylist(r)&&this.setSubtitleTrack(this.trackId)}findTrackId(e){const t=this.tracksInGroup,i=this.selectDefaultTrack;for(let n=0;n<t.length;n++){const r=t[n];if(!(i&&!r.default||!i&&!e)&&(!e||Ct(r,e)))return n}if(e){for(let n=0;n<t.length;n++){const r=t[n];if(mi(e.attrs,r.attrs,["LANGUAGE","ASSOC-LANGUAGE","CHARACTERISTICS"]))return n}for(let n=0;n<t.length;n++){const r=t[n];if(mi(e.attrs,r.attrs,["LANGUAGE"]))return n}}return-1}findTrackForTextTrack(e){if(e){const t=this.tracksInGroup;for(let i=0;i<t.length;i++){const n=t[i];if(vn(n,e))return i}}return-1}onError(e,t){t.fatal||!t.context||t.context.type===ce.SUBTITLE_TRACK&&t.context.id===this.trackId&&(!this.groupIds||this.groupIds.indexOf(t.context.groupId)!==-1)&&this.checkRetry(t)}get allSubtitleTracks(){return this.tracks}get subtitleTracks(){return this.tracksInGroup}get subtitleTrack(){return this.trackId}set subtitleTrack(e){this.selectDefaultTrack=!1,this.setSubtitleTrack(e)}setSubtitleOption(e){if(this.hls.config.subtitlePreference=e,e){const t=this.allSubtitleTracks;if(this.selectDefaultTrack=!1,t.length){const i=this.currentTrack;if(i&&Ct(e,i))return i;const n=st(e,this.tracksInGroup);if(n>-1){const r=this.tracksInGroup[n];return this.setSubtitleTrack(n),r}else{if(i)return null;{const r=st(e,t);if(r>-1)return t[r]}}}}return null}loadPlaylist(e){super.loadPlaylist();const t=this.currentTrack;if(this.shouldLoadPlaylist(t)&&t){const i=t.id,n=t.groupId;let r=t.url;if(e)try{r=e.addDirectives(r)}catch(a){this.warn(`Could not construct new URL with HLS Delivery Directives: ${a}`)}this.log(`Loading subtitle playlist for id ${i}`),this.hls.trigger(v.SUBTITLE_TRACK_LOADING,{url:r,id:i,groupId:n,deliveryDirectives:e||null})}}toggleTrackModes(){const{media:e}=this;if(!e)return;const t=qi(e.textTracks),i=this.currentTrack;let n;if(i&&(n=t.filter(r=>vn(i,r))[0],n||this.warn(`Unable to find subtitle TextTrack with name "${i.name}" and language "${i.lang}"`)),[].slice.call(t).forEach(r=>{r.mode!=="disabled"&&r!==n&&(r.mode="disabled")}),n){const r=this.subtitleDisplay?"showing":"hidden";n.mode!==r&&(n.mode=r)}}setSubtitleTrack(e){const t=this.tracksInGroup;if(!this.media){this.queuedDefaultTrack=e;return}if(e<-1||e>=t.length||!J(e)){this.warn(`Invalid subtitle track id: ${e}`);return}this.clearTimer(),this.selectDefaultTrack=!1;const i=this.currentTrack,n=t[e]||null;if(this.trackId=e,this.currentTrack=n,this.toggleTrackModes(),!n){this.hls.trigger(v.SUBTITLE_TRACK_SWITCH,{id:e});return}const r=!!n.details&&!n.details.live;if(e===this.trackId&&n===i&&r)return;this.log(`Switching to subtitle-track ${e}`+(n?` "${n.name}" lang:${n.lang} group:${n.groupId}`:""));const{id:a,groupId:o="",name:l,type:u,url:c}=n;this.hls.trigger(v.SUBTITLE_TRACK_SWITCH,{id:a,groupId:o,name:l,type:u,url:c});const d=this.switchParams(n.url,i?.details,n.details);this.loadPlaylist(d)}}class ff{constructor(e){this.buffers=void 0,this.queues={video:[],audio:[],audiovideo:[]},this.buffers=e}append(e,t,i){const n=this.queues[t];n.push(e),n.length===1&&!i&&this.executeNext(t)}insertAbort(e,t){this.queues[t].unshift(e),this.executeNext(t)}appendBlocker(e){let t;const i=new Promise(r=>{t=r}),n={execute:t,onStart:()=>{},onComplete:()=>{},onError:()=>{}};return this.append(n,e),i}executeNext(e){const t=this.queues[e];if(t.length){const i=t[0];try{i.execute()}catch(n){_.warn(`[buffer-operation-queue]: Exception executing "${e}" SourceBuffer operation: ${n}`),i.onError(n);const r=this.buffers[e];r!=null&&r.updating||this.shiftAndExecuteNext(e)}}}shiftAndExecuteNext(e){this.queues[e].shift(),this.executeNext(e)}current(e){return this.queues[e][0]}}const Jr=/(avc[1234]|hvc1|hev1|dvh[1e]|vp09|av01)(?:\.[^.,]+)+/;class hf{constructor(e){this.details=null,this._objectUrl=null,this.operationQueue=void 0,this.listeners=void 0,this.hls=void 0,this.bufferCodecEventsExpected=0,this._bufferCodecEventsTotal=0,this.media=null,this.mediaSource=null,this.lastMpegAudioChunk=null,this.appendSource=void 0,this.appendErrors={audio:0,video:0,audiovideo:0},this.tracks={},this.pendingTracks={},this.sourceBuffer=void 0,this.log=void 0,this.warn=void 0,this.error=void 0,this._onEndStreaming=i=>{this.hls&&this.hls.pauseBuffering()},this._onStartStreaming=i=>{this.hls&&this.hls.resumeBuffering()},this._onMediaSourceOpen=()=>{const{media:i,mediaSource:n}=this;this.log("Media source opened"),i&&(i.removeEventListener("emptied",this._onMediaEmptied),this.updateMediaElementDuration(),this.hls.trigger(v.MEDIA_ATTACHED,{media:i,mediaSource:n})),n&&n.removeEventListener("sourceopen",this._onMediaSourceOpen),this.checkPendingTracks()},this._onMediaSourceClose=()=>{this.log("Media source closed")},this._onMediaSourceEnded=()=>{this.log("Media source ended")},this._onMediaEmptied=()=>{const{mediaSrc:i,_objectUrl:n}=this;i!==n&&_.error(`Media element src was set while attaching MediaSource (${n} > ${i})`)},this.hls=e;const t="[buffer-controller]";this.appendSource=xc(_t(e.config.preferManagedMediaSource)),this.log=_.log.bind(_,t),this.warn=_.warn.bind(_,t),this.error=_.error.bind(_,t),this._initSourceBuffer(),this.registerListeners()}hasSourceTypes(){return this.getSourceBufferTypes().length>0||Object.keys(this.pendingTracks).length>0}destroy(){this.unregisterListeners(),this.details=null,this.lastMpegAudioChunk=null,this.hls=null}registerListeners(){const{hls:e}=this;e.on(v.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.MANIFEST_PARSED,this.onManifestParsed,this),e.on(v.BUFFER_RESET,this.onBufferReset,this),e.on(v.BUFFER_APPENDING,this.onBufferAppending,this),e.on(v.BUFFER_CODECS,this.onBufferCodecs,this),e.on(v.BUFFER_EOS,this.onBufferEos,this),e.on(v.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(v.LEVEL_UPDATED,this.onLevelUpdated,this),e.on(v.FRAG_PARSED,this.onFragParsed,this),e.on(v.FRAG_CHANGED,this.onFragChanged,this)}unregisterListeners(){const{hls:e}=this;e.off(v.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.MANIFEST_PARSED,this.onManifestParsed,this),e.off(v.BUFFER_RESET,this.onBufferReset,this),e.off(v.BUFFER_APPENDING,this.onBufferAppending,this),e.off(v.BUFFER_CODECS,this.onBufferCodecs,this),e.off(v.BUFFER_EOS,this.onBufferEos,this),e.off(v.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(v.LEVEL_UPDATED,this.onLevelUpdated,this),e.off(v.FRAG_PARSED,this.onFragParsed,this),e.off(v.FRAG_CHANGED,this.onFragChanged,this)}_initSourceBuffer(){this.sourceBuffer={},this.operationQueue=new ff(this.sourceBuffer),this.listeners={audio:[],video:[],audiovideo:[]},this.appendErrors={audio:0,video:0,audiovideo:0},this.lastMpegAudioChunk=null}onManifestLoading(){this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=0,this.details=null}onManifestParsed(e,t){let i=2;(t.audio&&!t.video||!t.altAudio)&&(i=1),this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=i,this.log(`${this.bufferCodecEventsExpected} bufferCodec event(s) expected`)}onMediaAttaching(e,t){const i=this.media=t.media,n=_t(this.appendSource);if(i&&n){var r;const a=this.mediaSource=new n;this.log(`created media source: ${(r=a.constructor)==null?void 0:r.name}`),a.addEventListener("sourceopen",this._onMediaSourceOpen),a.addEventListener("sourceended",this._onMediaSourceEnded),a.addEventListener("sourceclose",this._onMediaSourceClose),this.appendSource&&(a.addEventListener("startstreaming",this._onStartStreaming),a.addEventListener("endstreaming",this._onEndStreaming));const o=this._objectUrl=self.URL.createObjectURL(a);if(this.appendSource)try{i.removeAttribute("src");const l=self.ManagedMediaSource;i.disableRemotePlayback=i.disableRemotePlayback||l&&a instanceof l,ea(i),gf(i,o),i.load()}catch{i.src=o}else i.src=o;i.addEventListener("emptied",this._onMediaEmptied)}}onMediaDetaching(){const{media:e,mediaSource:t,_objectUrl:i}=this;if(t){if(this.log("media source detaching"),t.readyState==="open")try{t.endOfStream()}catch(n){this.warn(`onMediaDetaching: ${n.message} while calling endOfStream`)}this.onBufferReset(),t.removeEventListener("sourceopen",this._onMediaSourceOpen),t.removeEventListener("sourceended",this._onMediaSourceEnded),t.removeEventListener("sourceclose",this._onMediaSourceClose),this.appendSource&&(t.removeEventListener("startstreaming",this._onStartStreaming),t.removeEventListener("endstreaming",this._onEndStreaming)),e&&(e.removeEventListener("emptied",this._onMediaEmptied),i&&self.URL.revokeObjectURL(i),this.mediaSrc===i?(e.removeAttribute("src"),this.appendSource&&ea(e),e.load()):this.warn("media|source.src was changed by a third party - skip cleanup")),this.mediaSource=null,this.media=null,this._objectUrl=null,this.bufferCodecEventsExpected=this._bufferCodecEventsTotal,this.pendingTracks={},this.tracks={}}this.hls.trigger(v.MEDIA_DETACHED,void 0)}onBufferReset(){this.getSourceBufferTypes().forEach(e=>{this.resetBuffer(e)}),this._initSourceBuffer(),this.hls.resumeBuffering()}resetBuffer(e){const t=this.sourceBuffer[e];try{if(t){var i;this.removeBufferListeners(e),this.sourceBuffer[e]=void 0,(i=this.mediaSource)!=null&&i.sourceBuffers.length&&this.mediaSource.removeSourceBuffer(t)}}catch(n){this.warn(`onBufferReset ${e}`,n)}}onBufferCodecs(e,t){const i=this.getSourceBufferTypes().length,n=Object.keys(t);if(n.forEach(a=>{if(i){const l=this.tracks[a];if(l&&typeof l.buffer.changeType=="function"){var o;const{id:u,codec:c,levelCodec:d,container:f,metadata:h}=t[a],g=xr(l.codec,l.levelCodec),p=g?.replace(Jr,"$1");let m=xr(c,d);const y=(o=m)==null?void 0:o.replace(Jr,"$1");if(m&&p!==y){a.slice(0,5)==="audio"&&(m=ss(m,this.appendSource));const S=`${f};codecs=${m}`;this.appendChangeType(a,S),this.log(`switching codec ${g} to ${m}`),this.tracks[a]={buffer:l.buffer,codec:c,container:f,levelCodec:d,metadata:h,id:u}}}}else this.pendingTracks[a]=t[a]}),i)return;const r=Math.max(this.bufferCodecEventsExpected-1,0);this.bufferCodecEventsExpected!==r&&(this.log(`${r} bufferCodec event(s) expected ${n.join(",")}`),this.bufferCodecEventsExpected=r),this.mediaSource&&this.mediaSource.readyState==="open"&&this.checkPendingTracks()}appendChangeType(e,t){const{operationQueue:i}=this,n={execute:()=>{const r=this.sourceBuffer[e];r&&(this.log(`changing ${e} sourceBuffer type to ${t}`),r.changeType(t)),i.shiftAndExecuteNext(e)},onStart:()=>{},onComplete:()=>{},onError:r=>{this.warn(`Failed to change ${e} SourceBuffer type`,r)}};i.append(n,e,!!this.pendingTracks[e])}onBufferAppending(e,t){const{hls:i,operationQueue:n,tracks:r}=this,{data:a,type:o,frag:l,part:u,chunkMeta:c}=t,d=c.buffering[o],f=self.performance.now();d.start=f;const h=l.stats.buffering,g=u?u.stats.buffering:null;h.start===0&&(h.start=f),g&&g.start===0&&(g.start=f);const p=r.audio;let m=!1;o==="audio"&&p?.container==="audio/mpeg"&&(m=!this.lastMpegAudioChunk||c.id===1||this.lastMpegAudioChunk.sn!==c.sn,this.lastMpegAudioChunk=c);const y=l.start,S={execute:()=>{if(d.executeStart=self.performance.now(),m){const T=this.sourceBuffer[o];if(T){const I=y-T.timestampOffset;Math.abs(I)>=.1&&(this.log(`Updating audio SourceBuffer timestampOffset to ${y} (delta: ${I}) sn: ${l.sn})`),T.timestampOffset=y)}}this.appendExecutor(a,o)},onStart:()=>{},onComplete:()=>{const T=self.performance.now();d.executeEnd=d.end=T,h.first===0&&(h.first=T),g&&g.first===0&&(g.first=T);const{sourceBuffer:I}=this,E={};for(const P in I)E[P]=me.getBuffered(I[P]);this.appendErrors[o]=0,o==="audio"||o==="video"?this.appendErrors.audiovideo=0:(this.appendErrors.audio=0,this.appendErrors.video=0),this.hls.trigger(v.BUFFER_APPENDED,{type:o,frag:l,part:u,chunkMeta:c,parent:l.type,timeRanges:E})},onError:T=>{const I={type:re.MEDIA_ERROR,parent:l.type,details:O.BUFFER_APPEND_ERROR,sourceBufferName:o,frag:l,part:u,chunkMeta:c,error:T,err:T,fatal:!1};if(T.code===DOMException.QUOTA_EXCEEDED_ERR)I.details=O.BUFFER_FULL_ERROR;else{const E=++this.appendErrors[o];I.details=O.BUFFER_APPEND_ERROR,this.warn(`Failed ${E}/${i.config.appendErrorMaxRetry} times to append segment in "${o}" sourceBuffer`),E>=i.config.appendErrorMaxRetry&&(I.fatal=!0)}i.trigger(v.ERROR,I)}};n.append(S,o,!!this.pendingTracks[o])}onBufferFlushing(e,t){const{operationQueue:i}=this,n=r=>({execute:this.removeExecutor.bind(this,r,t.startOffset,t.endOffset),onStart:()=>{},onComplete:()=>{this.hls.trigger(v.BUFFER_FLUSHED,{type:r})},onError:a=>{this.warn(`Failed to remove from ${r} SourceBuffer`,a)}});t.type?i.append(n(t.type),t.type):this.getSourceBufferTypes().forEach(r=>{i.append(n(r),r)})}onFragParsed(e,t){const{frag:i,part:n}=t,r=[],a=n?n.elementaryStreams:i.elementaryStreams;a[fe.AUDIOVIDEO]?r.push("audiovideo"):(a[fe.AUDIO]&&r.push("audio"),a[fe.VIDEO]&&r.push("video"));const o=()=>{const l=self.performance.now();i.stats.buffering.end=l,n&&(n.stats.buffering.end=l);const u=n?n.stats:i.stats;this.hls.trigger(v.FRAG_BUFFERED,{frag:i,part:n,stats:u,id:i.type})};r.length===0&&this.warn(`Fragments must have at least one ElementaryStreamType set. type: ${i.type} level: ${i.level} sn: ${i.sn}`),this.blockBuffers(o,r)}onFragChanged(e,t){this.trimBuffers()}onBufferEos(e,t){this.getSourceBufferTypes().reduce((n,r)=>{const a=this.sourceBuffer[r];return a&&(!t.type||t.type===r)&&(a.ending=!0,a.ended||(a.ended=!0,this.log(`${r} sourceBuffer now EOS`))),n&&!!(!a||a.ended)},!0)&&(this.log("Queueing mediaSource.endOfStream()"),this.blockBuffers(()=>{this.getSourceBufferTypes().forEach(r=>{const a=this.sourceBuffer[r];a&&(a.ending=!1)});const{mediaSource:n}=this;if(!n||n.readyState!=="open"){n&&this.log(`Could not call mediaSource.endOfStream(). mediaSource.readyState: ${n.readyState}`);return}this.log("Calling mediaSource.endOfStream()"),n.endOfStream()}))}onLevelUpdated(e,{details:t}){t.fragments.length&&(this.details=t,this.getSourceBufferTypes().length?this.blockBuffers(this.updateMediaElementDuration.bind(this)):this.updateMediaElementDuration())}trimBuffers(){const{hls:e,details:t,media:i}=this;if(!i||t===null||!this.getSourceBufferTypes().length)return;const r=e.config,a=i.currentTime,o=t.levelTargetDuration,l=t.live&&r.liveBackBufferLength!==null?r.liveBackBufferLength:r.backBufferLength;if(J(l)&&l>0){const u=Math.max(l,o),c=Math.floor(a/o)*o-u;this.flushBackBuffer(a,o,c)}if(J(r.frontBufferFlushThreshold)&&r.frontBufferFlushThreshold>0){const u=Math.max(r.maxBufferLength,r.frontBufferFlushThreshold),c=Math.max(u,o),d=Math.floor(a/o)*o+c;this.flushFrontBuffer(a,o,d)}}flushBackBuffer(e,t,i){const{details:n,sourceBuffer:r}=this;this.getSourceBufferTypes().forEach(o=>{const l=r[o];if(l){const u=me.getBuffered(l);if(u.length>0&&i>u.start(0)){if(this.hls.trigger(v.BACK_BUFFER_REACHED,{bufferEnd:i}),n!=null&&n.live)this.hls.trigger(v.LIVE_BACK_BUFFER_REACHED,{bufferEnd:i});else if(l.ended&&u.end(u.length-1)-e<t*2){this.log(`Cannot flush ${o} back buffer while SourceBuffer is in ended state`);return}this.hls.trigger(v.BUFFER_FLUSHING,{startOffset:0,endOffset:i,type:o})}}})}flushFrontBuffer(e,t,i){const{sourceBuffer:n}=this;this.getSourceBufferTypes().forEach(a=>{const o=n[a];if(o){const l=me.getBuffered(o),u=l.length;if(u<2)return;const c=l.start(u-1),d=l.end(u-1);if(i>c||e>=c&&e<=d)return;if(o.ended&&e-d<2*t){this.log(`Cannot flush ${a} front buffer while SourceBuffer is in ended state`);return}this.hls.trigger(v.BUFFER_FLUSHING,{startOffset:c,endOffset:1/0,type:a})}})}updateMediaElementDuration(){if(!this.details||!this.media||!this.mediaSource||this.mediaSource.readyState!=="open")return;const{details:e,hls:t,media:i,mediaSource:n}=this,r=e.fragments[0].start+e.totalduration,a=i.duration,o=J(n.duration)?n.duration:0;e.live&&t.config.liveDurationInfinity?(n.duration=1/0,this.updateSeekableRange(e)):(r>o&&r>a||!J(a))&&(this.log(`Updating Media Source duration to ${r.toFixed(3)}`),n.duration=r)}updateSeekableRange(e){const t=this.mediaSource,i=e.fragments;if(i.length&&e.live&&t!=null&&t.setLiveSeekableRange){const r=Math.max(0,i[0].start),a=Math.max(r,r+e.totalduration);this.log(`Media Source duration is set to ${t.duration}. Setting seekable range to ${r}-${a}.`),t.setLiveSeekableRange(r,a)}}checkPendingTracks(){const{bufferCodecEventsExpected:e,operationQueue:t,pendingTracks:i}=this,n=Object.keys(i).length;if(n&&(!e||n===2||"audiovideo"in i)){this.createSourceBuffers(i),this.pendingTracks={};const r=this.getSourceBufferTypes();if(r.length)this.hls.trigger(v.BUFFER_CREATED,{tracks:this.tracks}),r.forEach(a=>{t.executeNext(a)});else{const a=new Error("could not create source buffer for media codec(s)");this.hls.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.BUFFER_INCOMPATIBLE_CODECS_ERROR,fatal:!0,error:a,reason:a.message})}}}createSourceBuffers(e){const{sourceBuffer:t,mediaSource:i}=this;if(!i)throw Error("createSourceBuffers called when mediaSource was null");for(const r in e)if(!t[r]){var n;const a=e[r];if(!a)throw Error(`source buffer exists for track ${r}, however track does not`);let o=((n=a.levelCodec)==null?void 0:n.indexOf(","))===-1?a.levelCodec:a.codec;o&&r.slice(0,5)==="audio"&&(o=ss(o,this.appendSource));const l=`${a.container};codecs=${o}`;this.log(`creating sourceBuffer(${l})`);try{const u=t[r]=i.addSourceBuffer(l),c=r;this.addBufferListener(c,"updatestart",this._onSBUpdateStart),this.addBufferListener(c,"updateend",this._onSBUpdateEnd),this.addBufferListener(c,"error",this._onSBUpdateError),this.appendSource&&this.addBufferListener(c,"bufferedchange",(d,f)=>{const h=f.removedRanges;h!=null&&h.length&&this.hls.trigger(v.BUFFER_FLUSHED,{type:r})}),this.tracks[r]={buffer:u,codec:o,container:a.container,levelCodec:a.levelCodec,metadata:a.metadata,id:a.id}}catch(u){this.error(`error while trying to add sourceBuffer: ${u.message}`),this.hls.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.BUFFER_ADD_CODEC_ERROR,fatal:!1,error:u,sourceBufferName:r,mimeType:l})}}}get mediaSrc(){var e,t;const i=((e=this.media)==null||(t=e.querySelector)==null?void 0:t.call(e,"source"))||this.media;return i?.src}_onSBUpdateStart(e){const{operationQueue:t}=this;t.current(e).onStart()}_onSBUpdateEnd(e){var t;if(((t=this.mediaSource)==null?void 0:t.readyState)==="closed"){this.resetBuffer(e);return}const{operationQueue:i}=this;i.current(e).onComplete(),i.shiftAndExecuteNext(e)}_onSBUpdateError(e,t){var i;const n=new Error(`${e} SourceBuffer error. MediaSource readyState: ${(i=this.mediaSource)==null?void 0:i.readyState}`);this.error(`${n}`,t),this.hls.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.BUFFER_APPENDING_ERROR,sourceBufferName:e,error:n,fatal:!1});const r=this.operationQueue.current(e);r&&r.onError(n)}removeExecutor(e,t,i){const{media:n,mediaSource:r,operationQueue:a,sourceBuffer:o}=this,l=o[e];if(!n||!r||!l){this.warn(`Attempting to remove from the ${e} SourceBuffer, but it does not exist`),a.shiftAndExecuteNext(e);return}const u=J(n.duration)?n.duration:1/0,c=J(r.duration)?r.duration:1/0,d=Math.max(0,t),f=Math.min(i,u,c);f>d&&(!l.ending||l.ended)?(l.ended=!1,this.log(`Removing [${d},${f}] from the ${e} SourceBuffer`),l.remove(d,f)):a.shiftAndExecuteNext(e)}appendExecutor(e,t){const i=this.sourceBuffer[t];if(!i){if(!this.pendingTracks[t])throw new Error(`Attempting to append to the ${t} SourceBuffer, but it does not exist`);return}i.ended=!1,i.appendBuffer(e)}blockBuffers(e,t=this.getSourceBufferTypes()){if(!t.length){this.log("Blocking operation requested, but no SourceBuffers exist"),Promise.resolve().then(e);return}const{operationQueue:i}=this,n=t.map(r=>i.appendBlocker(r));Promise.all(n).then(()=>{e(),t.forEach(r=>{const a=this.sourceBuffer[r];a!=null&&a.updating||i.shiftAndExecuteNext(r)})})}getSourceBufferTypes(){return Object.keys(this.sourceBuffer)}addBufferListener(e,t,i){const n=this.sourceBuffer[e];if(!n)return;const r=i.bind(this,e);this.listeners[e].push({event:t,listener:r}),n.addEventListener(t,r)}removeBufferListeners(e){const t=this.sourceBuffer[e];t&&this.listeners[e].forEach(i=>{t.removeEventListener(i.event,i.listener)})}}function ea(s){const e=s.querySelectorAll("source");[].slice.call(e).forEach(t=>{s.removeChild(t)})}function gf(s,e){const t=self.document.createElement("source");t.type="video/mp4",t.src=e,s.appendChild(t)}const mf={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,128:174,129:176,130:189,131:191,132:8482,133:162,134:163,135:9834,136:224,137:32,138:232,139:226,140:234,141:238,142:244,143:251,144:193,145:201,146:211,147:218,148:220,149:252,150:8216,151:161,152:42,153:8217,154:9473,155:169,156:8480,157:8226,158:8220,159:8221,160:192,161:194,162:199,163:200,164:202,165:203,166:235,167:206,168:207,169:239,170:212,171:217,172:249,173:219,174:171,175:187,176:195,177:227,178:205,179:204,180:236,181:210,182:242,183:213,184:245,185:123,186:125,187:92,188:94,189:95,190:124,191:8764,192:196,193:228,194:214,195:246,196:223,197:165,198:164,199:9475,200:197,201:229,202:216,203:248,204:9487,205:9491,206:9495,207:9499},qo=s=>String.fromCharCode(mf[s]||s),Ke=15,lt=100,pf={17:1,18:3,21:5,22:7,23:9,16:11,19:12,20:14},vf={17:2,18:4,21:6,22:8,23:10,19:13,20:15},yf={25:1,26:3,29:5,30:7,31:9,24:11,27:12,28:14},Sf={25:2,26:4,29:6,30:8,31:10,27:13,28:15},Tf=["white","green","blue","cyan","red","yellow","magenta","black","transparent"];class xf{constructor(){this.time=null,this.verboseLevel=0}log(e,t){if(this.verboseLevel>=e){const i=typeof t=="function"?t():t;_.log(`${this.time} [${e}] ${i}`)}}}const St=function(e){const t=[];for(let i=0;i<e.length;i++)t.push(e[i].toString(16));return t};class Wo{constructor(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1}reset(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1}setStyles(e){const t=["foreground","underline","italics","background","flash"];for(let i=0;i<t.length;i++){const n=t[i];e.hasOwnProperty(n)&&(this[n]=e[n])}}isDefault(){return this.foreground==="white"&&!this.underline&&!this.italics&&this.background==="black"&&!this.flash}equals(e){return this.foreground===e.foreground&&this.underline===e.underline&&this.italics===e.italics&&this.background===e.background&&this.flash===e.flash}copy(e){this.foreground=e.foreground,this.underline=e.underline,this.italics=e.italics,this.background=e.background,this.flash=e.flash}toString(){return"color="+this.foreground+", underline="+this.underline+", italics="+this.italics+", background="+this.background+", flash="+this.flash}}class Ef{constructor(){this.uchar=" ",this.penState=new Wo}reset(){this.uchar=" ",this.penState.reset()}setChar(e,t){this.uchar=e,this.penState.copy(t)}setPenState(e){this.penState.copy(e)}equals(e){return this.uchar===e.uchar&&this.penState.equals(e.penState)}copy(e){this.uchar=e.uchar,this.penState.copy(e.penState)}isEmpty(){return this.uchar===" "&&this.penState.isDefault()}}class Af{constructor(e){this.chars=[],this.pos=0,this.currPenState=new Wo,this.cueStartTime=null,this.logger=void 0;for(let t=0;t<lt;t++)this.chars.push(new Ef);this.logger=e}equals(e){for(let t=0;t<lt;t++)if(!this.chars[t].equals(e.chars[t]))return!1;return!0}copy(e){for(let t=0;t<lt;t++)this.chars[t].copy(e.chars[t])}isEmpty(){let e=!0;for(let t=0;t<lt;t++)if(!this.chars[t].isEmpty()){e=!1;break}return e}setCursor(e){this.pos!==e&&(this.pos=e),this.pos<0?(this.logger.log(3,"Negative cursor position "+this.pos),this.pos=0):this.pos>lt&&(this.logger.log(3,"Too large cursor position "+this.pos),this.pos=lt)}moveCursor(e){const t=this.pos+e;if(e>1)for(let i=this.pos+1;i<t+1;i++)this.chars[i].setPenState(this.currPenState);this.setCursor(t)}backSpace(){this.moveCursor(-1),this.chars[this.pos].setChar(" ",this.currPenState)}insertChar(e){e>=144&&this.backSpace();const t=qo(e);if(this.pos>=lt){this.logger.log(0,()=>"Cannot insert "+e.toString(16)+" ("+t+") at position "+this.pos+". Skipping it!");return}this.chars[this.pos].setChar(t,this.currPenState),this.moveCursor(1)}clearFromPos(e){let t;for(t=e;t<lt;t++)this.chars[t].reset()}clear(){this.clearFromPos(0),this.pos=0,this.currPenState.reset()}clearToEndOfRow(){this.clearFromPos(this.pos)}getTextString(){const e=[];let t=!0;for(let i=0;i<lt;i++){const n=this.chars[i].uchar;n!==" "&&(t=!1),e.push(n)}return t?"":e.join("")}setPenStyles(e){this.currPenState.setStyles(e),this.chars[this.pos].setPenState(this.currPenState)}}class Vs{constructor(e){this.rows=[],this.currRow=Ke-1,this.nrRollUpRows=null,this.lastOutputScreen=null,this.logger=void 0;for(let t=0;t<Ke;t++)this.rows.push(new Af(e));this.logger=e}reset(){for(let e=0;e<Ke;e++)this.rows[e].clear();this.currRow=Ke-1}equals(e){let t=!0;for(let i=0;i<Ke;i++)if(!this.rows[i].equals(e.rows[i])){t=!1;break}return t}copy(e){for(let t=0;t<Ke;t++)this.rows[t].copy(e.rows[t])}isEmpty(){let e=!0;for(let t=0;t<Ke;t++)if(!this.rows[t].isEmpty()){e=!1;break}return e}backSpace(){this.rows[this.currRow].backSpace()}clearToEndOfRow(){this.rows[this.currRow].clearToEndOfRow()}insertChar(e){this.rows[this.currRow].insertChar(e)}setPen(e){this.rows[this.currRow].setPenStyles(e)}moveCursor(e){this.rows[this.currRow].moveCursor(e)}setCursor(e){this.logger.log(2,"setCursor: "+e),this.rows[this.currRow].setCursor(e)}setPAC(e){this.logger.log(2,()=>"pacData = "+JSON.stringify(e));let t=e.row-1;if(this.nrRollUpRows&&t<this.nrRollUpRows-1&&(t=this.nrRollUpRows-1),this.nrRollUpRows&&this.currRow!==t){for(let o=0;o<Ke;o++)this.rows[o].clear();const r=this.currRow+1-this.nrRollUpRows,a=this.lastOutputScreen;if(a){const o=a.rows[r].cueStartTime,l=this.logger.time;if(o!==null&&l!==null&&o<l)for(let u=0;u<this.nrRollUpRows;u++)this.rows[t-this.nrRollUpRows+u+1].copy(a.rows[r+u])}}this.currRow=t;const i=this.rows[this.currRow];if(e.indent!==null){const r=e.indent,a=Math.max(r-1,0);i.setCursor(e.indent),e.color=i.chars[a].penState.foreground}const n={foreground:e.color,underline:e.underline,italics:e.italics,background:"black",flash:!1};this.setPen(n)}setBkgData(e){this.logger.log(2,()=>"bkgData = "+JSON.stringify(e)),this.backSpace(),this.setPen(e),this.insertChar(32)}setRollUpRows(e){this.nrRollUpRows=e}rollUp(){if(this.nrRollUpRows===null){this.logger.log(3,"roll_up but nrRollUpRows not set yet");return}this.logger.log(1,()=>this.getDisplayText());const e=this.currRow+1-this.nrRollUpRows,t=this.rows.splice(e,1)[0];t.clear(),this.rows.splice(this.currRow,0,t),this.logger.log(2,"Rolling up")}getDisplayText(e){e=e||!1;const t=[];let i="",n=-1;for(let r=0;r<Ke;r++){const a=this.rows[r].getTextString();a&&(n=r+1,e?t.push("Row "+n+": '"+a+"'"):t.push(a.trim()))}return t.length>0&&(e?i="["+t.join(" | ")+"]":i=t.join(`
`)),i}getTextAndFormat(){return this.rows}}class ta{constructor(e,t,i){this.chNr=void 0,this.outputFilter=void 0,this.mode=void 0,this.verbose=void 0,this.displayedMemory=void 0,this.nonDisplayedMemory=void 0,this.lastOutputScreen=void 0,this.currRollUpRow=void 0,this.writeScreen=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chNr=e,this.outputFilter=t,this.mode=null,this.verbose=0,this.displayedMemory=new Vs(i),this.nonDisplayedMemory=new Vs(i),this.lastOutputScreen=new Vs(i),this.currRollUpRow=this.displayedMemory.rows[Ke-1],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null,this.logger=i}reset(){this.mode=null,this.displayedMemory.reset(),this.nonDisplayedMemory.reset(),this.lastOutputScreen.reset(),this.outputFilter.reset(),this.currRollUpRow=this.displayedMemory.rows[Ke-1],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null}getHandler(){return this.outputFilter}setHandler(e){this.outputFilter=e}setPAC(e){this.writeScreen.setPAC(e)}setBkgData(e){this.writeScreen.setBkgData(e)}setMode(e){e!==this.mode&&(this.mode=e,this.logger.log(2,()=>"MODE="+e),this.mode==="MODE_POP-ON"?this.writeScreen=this.nonDisplayedMemory:(this.writeScreen=this.displayedMemory,this.writeScreen.reset()),this.mode!=="MODE_ROLL-UP"&&(this.displayedMemory.nrRollUpRows=null,this.nonDisplayedMemory.nrRollUpRows=null),this.mode=e)}insertChars(e){for(let i=0;i<e.length;i++)this.writeScreen.insertChar(e[i]);const t=this.writeScreen===this.displayedMemory?"DISP":"NON_DISP";this.logger.log(2,()=>t+": "+this.writeScreen.getDisplayText(!0)),(this.mode==="MODE_PAINT-ON"||this.mode==="MODE_ROLL-UP")&&(this.logger.log(1,()=>"DISPLAYED: "+this.displayedMemory.getDisplayText(!0)),this.outputDataUpdate())}ccRCL(){this.logger.log(2,"RCL - Resume Caption Loading"),this.setMode("MODE_POP-ON")}ccBS(){this.logger.log(2,"BS - BackSpace"),this.mode!=="MODE_TEXT"&&(this.writeScreen.backSpace(),this.writeScreen===this.displayedMemory&&this.outputDataUpdate())}ccAOF(){}ccAON(){}ccDER(){this.logger.log(2,"DER- Delete to End of Row"),this.writeScreen.clearToEndOfRow(),this.outputDataUpdate()}ccRU(e){this.logger.log(2,"RU("+e+") - Roll Up"),this.writeScreen=this.displayedMemory,this.setMode("MODE_ROLL-UP"),this.writeScreen.setRollUpRows(e)}ccFON(){this.logger.log(2,"FON - Flash On"),this.writeScreen.setPen({flash:!0})}ccRDC(){this.logger.log(2,"RDC - Resume Direct Captioning"),this.setMode("MODE_PAINT-ON")}ccTR(){this.logger.log(2,"TR"),this.setMode("MODE_TEXT")}ccRTD(){this.logger.log(2,"RTD"),this.setMode("MODE_TEXT")}ccEDM(){this.logger.log(2,"EDM - Erase Displayed Memory"),this.displayedMemory.reset(),this.outputDataUpdate(!0)}ccCR(){this.logger.log(2,"CR - Carriage Return"),this.writeScreen.rollUp(),this.outputDataUpdate(!0)}ccENM(){this.logger.log(2,"ENM - Erase Non-displayed Memory"),this.nonDisplayedMemory.reset()}ccEOC(){if(this.logger.log(2,"EOC - End Of Caption"),this.mode==="MODE_POP-ON"){const e=this.displayedMemory;this.displayedMemory=this.nonDisplayedMemory,this.nonDisplayedMemory=e,this.writeScreen=this.nonDisplayedMemory,this.logger.log(1,()=>"DISP: "+this.displayedMemory.getDisplayText())}this.outputDataUpdate(!0)}ccTO(e){this.logger.log(2,"TO("+e+") - Tab Offset"),this.writeScreen.moveCursor(e)}ccMIDROW(e){const t={flash:!1};if(t.underline=e%2===1,t.italics=e>=46,t.italics)t.foreground="white";else{const i=Math.floor(e/2)-16,n=["white","green","blue","cyan","red","yellow","magenta"];t.foreground=n[i]}this.logger.log(2,"MIDROW: "+JSON.stringify(t)),this.writeScreen.setPen(t)}outputDataUpdate(e=!1){const t=this.logger.time;t!==null&&this.outputFilter&&(this.cueStartTime===null&&!this.displayedMemory.isEmpty()?this.cueStartTime=t:this.displayedMemory.equals(this.lastOutputScreen)||(this.outputFilter.newCue(this.cueStartTime,t,this.lastOutputScreen),e&&this.outputFilter.dispatchCue&&this.outputFilter.dispatchCue(),this.cueStartTime=this.displayedMemory.isEmpty()?null:t),this.lastOutputScreen.copy(this.displayedMemory))}cueSplitAtTime(e){this.outputFilter&&(this.displayedMemory.isEmpty()||(this.outputFilter.newCue&&this.outputFilter.newCue(this.cueStartTime,e,this.displayedMemory),this.cueStartTime=e))}}class ia{constructor(e,t,i){this.channels=void 0,this.currentChannel=0,this.cmdHistory=Lf(),this.logger=void 0;const n=this.logger=new xf;this.channels=[null,new ta(e,t,n),new ta(e+1,i,n)]}getHandler(e){return this.channels[e].getHandler()}setHandler(e,t){this.channels[e].setHandler(t)}addData(e,t){this.logger.time=e;for(let i=0;i<t.length;i+=2){const n=t[i]&127,r=t[i+1]&127;let a=!1,o=null;if(n===0&&r===0)continue;this.logger.log(3,()=>"["+St([t[i],t[i+1]])+"] -> ("+St([n,r])+")");const l=this.cmdHistory;if(n>=16&&n<=31){if(bf(n,r,l)){Pi(null,null,l),this.logger.log(3,()=>"Repeated command ("+St([n,r])+") is dropped");continue}Pi(n,r,this.cmdHistory),a=this.parseCmd(n,r),a||(a=this.parseMidrow(n,r)),a||(a=this.parsePAC(n,r)),a||(a=this.parseBackgroundAttributes(n,r))}else Pi(null,null,l);if(!a&&(o=this.parseChars(n,r),o)){const c=this.currentChannel;c&&c>0?this.channels[c].insertChars(o):this.logger.log(2,"No channel found yet. TEXT-MODE?")}!a&&!o&&this.logger.log(2,()=>"Couldn't parse cleaned data "+St([n,r])+" orig: "+St([t[i],t[i+1]]))}}parseCmd(e,t){const i=(e===20||e===28||e===21||e===29)&&t>=32&&t<=47,n=(e===23||e===31)&&t>=33&&t<=35;if(!(i||n))return!1;const r=e===20||e===21||e===23?1:2,a=this.channels[r];return e===20||e===21||e===28||e===29?t===32?a.ccRCL():t===33?a.ccBS():t===34?a.ccAOF():t===35?a.ccAON():t===36?a.ccDER():t===37?a.ccRU(2):t===38?a.ccRU(3):t===39?a.ccRU(4):t===40?a.ccFON():t===41?a.ccRDC():t===42?a.ccTR():t===43?a.ccRTD():t===44?a.ccEDM():t===45?a.ccCR():t===46?a.ccENM():t===47&&a.ccEOC():a.ccTO(t-32),this.currentChannel=r,!0}parseMidrow(e,t){let i=0;if((e===17||e===25)&&t>=32&&t<=47){if(e===17?i=1:i=2,i!==this.currentChannel)return this.logger.log(0,"Mismatch channel in midrow parsing"),!1;const n=this.channels[i];return n?(n.ccMIDROW(t),this.logger.log(3,()=>"MIDROW ("+St([e,t])+")"),!0):!1}return!1}parsePAC(e,t){let i;const n=(e>=17&&e<=23||e>=25&&e<=31)&&t>=64&&t<=127,r=(e===16||e===24)&&t>=64&&t<=95;if(!(n||r))return!1;const a=e<=23?1:2;t>=64&&t<=95?i=a===1?pf[e]:yf[e]:i=a===1?vf[e]:Sf[e];const o=this.channels[a];return o?(o.setPAC(this.interpretPAC(i,t)),this.currentChannel=a,!0):!1}interpretPAC(e,t){let i;const n={color:null,italics:!1,indent:null,underline:!1,row:e};return t>95?i=t-96:i=t-64,n.underline=(i&1)===1,i<=13?n.color=["white","green","blue","cyan","red","yellow","magenta","white"][Math.floor(i/2)]:i<=15?(n.italics=!0,n.color="white"):n.indent=Math.floor((i-16)/2)*4,n}parseChars(e,t){let i,n=null,r=null;if(e>=25?(i=2,r=e-8):(i=1,r=e),r>=17&&r<=19){let a;r===17?a=t+80:r===18?a=t+112:a=t+144,this.logger.log(2,()=>"Special char '"+qo(a)+"' in channel "+i),n=[a]}else e>=32&&e<=127&&(n=t===0?[e]:[e,t]);return n&&this.logger.log(3,()=>"Char codes =  "+St(n).join(",")),n}parseBackgroundAttributes(e,t){const i=(e===16||e===24)&&t>=32&&t<=47,n=(e===23||e===31)&&t>=45&&t<=47;if(!(i||n))return!1;let r;const a={};e===16||e===24?(r=Math.floor((t-32)/2),a.background=Tf[r],t%2===1&&(a.background=a.background+"_semi")):t===45?a.background="transparent":(a.foreground="black",t===47&&(a.underline=!0));const o=e<=23?1:2;return this.channels[o].setBkgData(a),!0}reset(){for(let e=0;e<Object.keys(this.channels).length;e++){const t=this.channels[e];t&&t.reset()}Pi(null,null,this.cmdHistory)}cueSplitAtTime(e){for(let t=0;t<this.channels.length;t++){const i=this.channels[t];i&&i.cueSplitAtTime(e)}}}function Pi(s,e,t){t.a=s,t.b=e}function bf(s,e,t){return t.a===s&&t.b===e}function Lf(){return{a:null,b:null}}class Fi{constructor(e,t){this.timelineController=void 0,this.cueRanges=[],this.trackName=void 0,this.startTime=null,this.endTime=null,this.screen=null,this.timelineController=e,this.trackName=t}dispatchCue(){this.startTime!==null&&(this.timelineController.addCues(this.trackName,this.startTime,this.endTime,this.screen,this.cueRanges),this.startTime=null)}newCue(e,t,i){(this.startTime===null||this.startTime>e)&&(this.startTime=e),this.endTime=t,this.screen=i,this.timelineController.createCaptionsTrack(this.trackName)}reset(){this.cueRanges=[],this.startTime=null}}var Gn=function(){if(Zt!=null&&Zt.VTTCue)return self.VTTCue;const s=["","lr","rl"],e=["start","middle","end","left","right"];function t(o,l){if(typeof l!="string"||!Array.isArray(o))return!1;const u=l.toLowerCase();return~o.indexOf(u)?u:!1}function i(o){return t(s,o)}function n(o){return t(e,o)}function r(o,...l){let u=1;for(;u<arguments.length;u++){const c=arguments[u];for(const d in c)o[d]=c[d]}return o}function a(o,l,u){const c=this,d={enumerable:!0};c.hasBeenReset=!1;let f="",h=!1,g=o,p=l,m=u,y=null,S="",T=!0,I="auto",E="start",P=50,R="middle",w=50,b="middle";Object.defineProperty(c,"id",r({},d,{get:function(){return f},set:function(x){f=""+x}})),Object.defineProperty(c,"pauseOnExit",r({},d,{get:function(){return h},set:function(x){h=!!x}})),Object.defineProperty(c,"startTime",r({},d,{get:function(){return g},set:function(x){if(typeof x!="number")throw new TypeError("Start time must be set to a number.");g=x,this.hasBeenReset=!0}})),Object.defineProperty(c,"endTime",r({},d,{get:function(){return p},set:function(x){if(typeof x!="number")throw new TypeError("End time must be set to a number.");p=x,this.hasBeenReset=!0}})),Object.defineProperty(c,"text",r({},d,{get:function(){return m},set:function(x){m=""+x,this.hasBeenReset=!0}})),Object.defineProperty(c,"region",r({},d,{get:function(){return y},set:function(x){y=x,this.hasBeenReset=!0}})),Object.defineProperty(c,"vertical",r({},d,{get:function(){return S},set:function(x){const D=i(x);if(D===!1)throw new SyntaxError("An invalid or illegal string was specified.");S=D,this.hasBeenReset=!0}})),Object.defineProperty(c,"snapToLines",r({},d,{get:function(){return T},set:function(x){T=!!x,this.hasBeenReset=!0}})),Object.defineProperty(c,"line",r({},d,{get:function(){return I},set:function(x){if(typeof x!="number"&&x!=="auto")throw new SyntaxError("An invalid number or illegal string was specified.");I=x,this.hasBeenReset=!0}})),Object.defineProperty(c,"lineAlign",r({},d,{get:function(){return E},set:function(x){const D=n(x);if(!D)throw new SyntaxError("An invalid or illegal string was specified.");E=D,this.hasBeenReset=!0}})),Object.defineProperty(c,"position",r({},d,{get:function(){return P},set:function(x){if(x<0||x>100)throw new Error("Position must be between 0 and 100.");P=x,this.hasBeenReset=!0}})),Object.defineProperty(c,"positionAlign",r({},d,{get:function(){return R},set:function(x){const D=n(x);if(!D)throw new SyntaxError("An invalid or illegal string was specified.");R=D,this.hasBeenReset=!0}})),Object.defineProperty(c,"size",r({},d,{get:function(){return w},set:function(x){if(x<0||x>100)throw new Error("Size must be between 0 and 100.");w=x,this.hasBeenReset=!0}})),Object.defineProperty(c,"align",r({},d,{get:function(){return b},set:function(x){const D=n(x);if(!D)throw new SyntaxError("An invalid or illegal string was specified.");b=D,this.hasBeenReset=!0}})),c.displayState=void 0}return a.prototype.getCueAsHTML=function(){return self.WebVTT.convertCueToDOMTree(self,this.text)},a}();class Rf{decode(e,t){if(!e)return"";if(typeof e!="string")throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(e))}}function Yo(s){function e(i,n,r,a){return(i|0)*3600+(n|0)*60+(r|0)+parseFloat(a||0)}const t=s.match(/^(?:(\d+):)?(\d{2}):(\d{2})(\.\d+)?/);return t?parseFloat(t[2])>59?e(t[2],t[3],0,t[4]):e(t[1],t[2],t[3],t[4]):null}class If{constructor(){this.values=Object.create(null)}set(e,t){!this.get(e)&&t!==""&&(this.values[e]=t)}get(e,t,i){return i?this.has(e)?this.values[e]:t[i]:this.has(e)?this.values[e]:t}has(e){return e in this.values}alt(e,t,i){for(let n=0;n<i.length;++n)if(t===i[n]){this.set(e,t);break}}integer(e,t){/^-?\d+$/.test(t)&&this.set(e,parseInt(t,10))}percent(e,t){if(/^([\d]{1,3})(\.[\d]*)?%$/.test(t)){const i=parseFloat(t);if(i>=0&&i<=100)return this.set(e,i),!0}return!1}}function zo(s,e,t,i){const n=i?s.split(i):[s];for(const r in n){if(typeof n[r]!="string")continue;const a=n[r].split(t);if(a.length!==2)continue;const o=a[0],l=a[1];e(o,l)}}const yn=new Gn(0,0,""),Oi=yn.align==="middle"?"middle":"center";function Cf(s,e,t){const i=s;function n(){const o=Yo(s);if(o===null)throw new Error("Malformed timestamp: "+i);return s=s.replace(/^[^\sa-zA-Z-]+/,""),o}function r(o,l){const u=new If;zo(o,function(f,h){let g;switch(f){case"region":for(let p=t.length-1;p>=0;p--)if(t[p].id===h){u.set(f,t[p].region);break}break;case"vertical":u.alt(f,h,["rl","lr"]);break;case"line":g=h.split(","),u.integer(f,g[0]),u.percent(f,g[0])&&u.set("snapToLines",!1),u.alt(f,g[0],["auto"]),g.length===2&&u.alt("lineAlign",g[1],["start",Oi,"end"]);break;case"position":g=h.split(","),u.percent(f,g[0]),g.length===2&&u.alt("positionAlign",g[1],["start",Oi,"end","line-left","line-right","auto"]);break;case"size":u.percent(f,h);break;case"align":u.alt(f,h,["start",Oi,"end","left","right"]);break}},/:/,/\s/),l.region=u.get("region",null),l.vertical=u.get("vertical","");let c=u.get("line","auto");c==="auto"&&yn.line===-1&&(c=-1),l.line=c,l.lineAlign=u.get("lineAlign","start"),l.snapToLines=u.get("snapToLines",!0),l.size=u.get("size",100),l.align=u.get("align",Oi);let d=u.get("position","auto");d==="auto"&&yn.position===50&&(d=l.align==="start"||l.align==="left"?0:l.align==="end"||l.align==="right"?100:50),l.position=d}function a(){s=s.replace(/^\s+/,"")}if(a(),e.startTime=n(),a(),s.slice(0,3)!=="-->")throw new Error("Malformed time stamp (time stamps must be separated by '-->'): "+i);s=s.slice(3),a(),e.endTime=n(),a(),r(s,e)}function jo(s){return s.replace(/<br(?: \/)?>/gi,`
`)}class Df{constructor(){this.state="INITIAL",this.buffer="",this.decoder=new Rf,this.regionList=[],this.cue=null,this.oncue=void 0,this.onparsingerror=void 0,this.onflush=void 0}parse(e){const t=this;e&&(t.buffer+=t.decoder.decode(e,{stream:!0}));function i(){let r=t.buffer,a=0;for(r=jo(r);a<r.length&&r[a]!=="\r"&&r[a]!==`
`;)++a;const o=r.slice(0,a);return r[a]==="\r"&&++a,r[a]===`
`&&++a,t.buffer=r.slice(a),o}function n(r){zo(r,function(a,o){},/:/)}try{let r="";if(t.state==="INITIAL"){if(!/\r\n|\n/.test(t.buffer))return this;r=i();const o=r.match(/^(ï»¿)?WEBVTT([ \t].*)?$/);if(!(o!=null&&o[0]))throw new Error("Malformed WebVTT signature.");t.state="HEADER"}let a=!1;for(;t.buffer;){if(!/\r\n|\n/.test(t.buffer))return this;switch(a?a=!1:r=i(),t.state){case"HEADER":/:/.test(r)?n(r):r||(t.state="ID");continue;case"NOTE":r||(t.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(r)){t.state="NOTE";break}if(!r)continue;if(t.cue=new Gn(0,0,""),t.state="CUE",r.indexOf("-->")===-1){t.cue.id=r;continue}case"CUE":if(!t.cue){t.state="BADCUE";continue}try{Cf(r,t.cue,t.regionList)}catch{t.cue=null,t.state="BADCUE";continue}t.state="CUETEXT";continue;case"CUETEXT":{const o=r.indexOf("-->")!==-1;if(!r||o&&(a=!0)){t.oncue&&t.cue&&t.oncue(t.cue),t.cue=null,t.state="ID";continue}if(t.cue===null)continue;t.cue.text&&(t.cue.text+=`
`),t.cue.text+=r}continue;case"BADCUE":r||(t.state="ID")}}}catch{t.state==="CUETEXT"&&t.cue&&t.oncue&&t.oncue(t.cue),t.cue=null,t.state=t.state==="INITIAL"?"BADWEBVTT":"BADCUE"}return this}flush(){const e=this;try{if((e.cue||e.state==="HEADER")&&(e.buffer+=`

`,e.parse()),e.state==="INITIAL"||e.state==="BADWEBVTT")throw new Error("Malformed WebVTT signature.")}catch(t){e.onparsingerror&&e.onparsingerror(t)}return e.onflush&&e.onflush(),this}}const wf=/\r\n|\n\r|\n|\r/g,Ks=function(e,t,i=0){return e.slice(i,i+t.length)===t},kf=function(e){let t=parseInt(e.slice(-3));const i=parseInt(e.slice(-6,-4)),n=parseInt(e.slice(-9,-7)),r=e.length>9?parseInt(e.substring(0,e.indexOf(":"))):0;if(!J(t)||!J(i)||!J(n)||!J(r))throw Error(`Malformed X-TIMESTAMP-MAP: Local:${e}`);return t+=1e3*i,t+=60*1e3*n,t+=60*60*1e3*r,t},Hs=function(e){let t=5381,i=e.length;for(;i;)t=t*33^e.charCodeAt(--i);return(t>>>0).toString()};function Vn(s,e,t){return Hs(s.toString())+Hs(e.toString())+Hs(t)}const _f=function(e,t,i){let n=e[t],r=e[n.prevCC];if(!r||!r.new&&n.new){e.ccOffset=e.presentationOffset=n.start,n.new=!1;return}for(;(a=r)!=null&&a.new;){var a;e.ccOffset+=n.start-r.start,n.new=!1,n=r,r=e[n.prevCC]}e.presentationOffset=i};function Pf(s,e,t,i,n,r,a){const o=new Df,l=nt(new Uint8Array(s)).trim().replace(wf,`
`).split(`
`),u=[],c=e?jd(e.baseTime,e.timescale):0;let d="00:00.000",f=0,h=0,g,p=!0;o.oncue=function(m){const y=t[i];let S=t.ccOffset;const T=(f-c)/9e4;if(y!=null&&y.new&&(h!==void 0?S=t.ccOffset=y.start:_f(t,i,T)),T){if(!e){g=new Error("Missing initPTS for VTT MPEGTS");return}S=T-t.presentationOffset}const I=m.endTime-m.startTime,E=$e((m.startTime+S-h)*9e4,n*9e4)/9e4;m.startTime=Math.max(E,0),m.endTime=Math.max(E+I,0);const P=m.text.trim();m.text=decodeURIComponent(encodeURIComponent(P)),m.id||(m.id=Vn(m.startTime,m.endTime,P)),m.endTime>0&&u.push(m)},o.onparsingerror=function(m){g=m},o.onflush=function(){if(g){a(g);return}r(u)},l.forEach(m=>{if(p)if(Ks(m,"X-TIMESTAMP-MAP=")){p=!1,m.slice(16).split(",").forEach(y=>{Ks(y,"LOCAL:")?d=y.slice(6):Ks(y,"MPEGTS:")&&(f=parseInt(y.slice(7)))});try{h=kf(d)/1e3}catch(y){g=y}return}else m===""&&(p=!1);o.parse(m+`
`)}),o.flush()}const qs="stpp.ttml.im1t",Xo=/^(\d{2,}):(\d{2}):(\d{2}):(\d{2})\.?(\d+)?$/,Qo=/^(\d*(?:\.\d*)?)(h|m|s|ms|f|t)$/,Ff={left:"start",center:"center",right:"end",start:"start",end:"end"};function sa(s,e,t,i){const n=ae(new Uint8Array(s),["mdat"]);if(n.length===0){i(new Error("Could not parse IMSC1 mdat"));return}const r=n.map(o=>nt(o)),a=zd(e.baseTime,1,e.timescale);try{r.forEach(o=>t(Of(o,a)))}catch(o){i(o)}}function Of(s,e){const n=new DOMParser().parseFromString(s,"text/xml").getElementsByTagName("tt")[0];if(!n)throw new Error("Invalid ttml");const r={frameRate:30,subFrameRate:1,frameRateMultiplier:0,tickRate:0},a=Object.keys(r).reduce((d,f)=>(d[f]=n.getAttribute(`ttp:${f}`)||r[f],d),{}),o=n.getAttribute("xml:space")!=="preserve",l=na(Ws(n,"styling","style")),u=na(Ws(n,"layout","region")),c=Ws(n,"body","[begin]");return[].map.call(c,d=>{const f=Zo(d,o);if(!f||!d.hasAttribute("begin"))return null;const h=zs(d.getAttribute("begin"),a),g=zs(d.getAttribute("dur"),a);let p=zs(d.getAttribute("end"),a);if(h===null)throw ra(d);if(p===null){if(g===null)throw ra(d);p=h+g}const m=new Gn(h-e,p-e,f);m.id=Vn(m.startTime,m.endTime,m.text);const y=u[d.getAttribute("region")],S=l[d.getAttribute("style")],T=Mf(y,S,l),{textAlign:I}=T;if(I){const E=Ff[I];E&&(m.lineAlign=E),m.align=I}return xe(m,T),m}).filter(d=>d!==null)}function Ws(s,e,t){const i=s.getElementsByTagName(e)[0];return i?[].slice.call(i.querySelectorAll(t)):[]}function na(s){return s.reduce((e,t)=>{const i=t.getAttribute("xml:id");return i&&(e[i]=t),e},{})}function Zo(s,e){return[].slice.call(s.childNodes).reduce((t,i,n)=>{var r;return i.nodeName==="br"&&n?t+`
`:(r=i.childNodes)!=null&&r.length?Zo(i,e):e?t+i.textContent.trim().replace(/\s+/g," "):t+i.textContent},"")}function Mf(s,e,t){const i="http://www.w3.org/ns/ttml#styling";let n=null;const r=["displayAlign","textAlign","color","backgroundColor","fontSize","fontFamily"],a=s!=null&&s.hasAttribute("style")?s.getAttribute("style"):null;return a&&t.hasOwnProperty(a)&&(n=t[a]),r.reduce((o,l)=>{const u=Ys(e,i,l)||Ys(s,i,l)||Ys(n,i,l);return u&&(o[l]=u),o},{})}function Ys(s,e,t){return s&&s.hasAttributeNS(e,t)?s.getAttributeNS(e,t):null}function ra(s){return new Error(`Could not parse ttml timestamp ${s}`)}function zs(s,e){if(!s)return null;let t=Yo(s);return t===null&&(Xo.test(s)?t=Nf(s,e):Qo.test(s)&&(t=Bf(s,e))),t}function Nf(s,e){const t=Xo.exec(s),i=(t[4]|0)+(t[5]|0)/e.subFrameRate;return(t[1]|0)*3600+(t[2]|0)*60+(t[3]|0)+i/e.frameRate}function Bf(s,e){const t=Qo.exec(s),i=Number(t[1]);switch(t[2]){case"h":return i*3600;case"m":return i*60;case"ms":return i*1e3;case"f":return i/e.frameRate;case"t":return i/e.tickRate}return i}class Uf{constructor(e){this.hls=void 0,this.media=null,this.config=void 0,this.enabled=!0,this.Cues=void 0,this.textTracks=[],this.tracks=[],this.initPTS=[],this.unparsedVttFrags=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.cea608Parser1=void 0,this.cea608Parser2=void 0,this.lastCc=-1,this.lastSn=-1,this.lastPartIndex=-1,this.prevCC=-1,this.vttCCs=oa(),this.captionsProperties=void 0,this.hls=e,this.config=e.config,this.Cues=e.config.cueHandler,this.captionsProperties={textTrack1:{label:this.config.captionsTextTrack1Label,languageCode:this.config.captionsTextTrack1LanguageCode},textTrack2:{label:this.config.captionsTextTrack2Label,languageCode:this.config.captionsTextTrack2LanguageCode},textTrack3:{label:this.config.captionsTextTrack3Label,languageCode:this.config.captionsTextTrack3LanguageCode},textTrack4:{label:this.config.captionsTextTrack4Label,languageCode:this.config.captionsTextTrack4LanguageCode}},e.on(v.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.MANIFEST_LOADED,this.onManifestLoaded,this),e.on(v.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),e.on(v.FRAG_LOADING,this.onFragLoading,this),e.on(v.FRAG_LOADED,this.onFragLoaded,this),e.on(v.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),e.on(v.FRAG_DECRYPTED,this.onFragDecrypted,this),e.on(v.INIT_PTS_FOUND,this.onInitPtsFound,this),e.on(v.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),e.on(v.BUFFER_FLUSHING,this.onBufferFlushing,this)}destroy(){const{hls:e}=this;e.off(v.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.MANIFEST_LOADED,this.onManifestLoaded,this),e.off(v.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),e.off(v.FRAG_LOADING,this.onFragLoading,this),e.off(v.FRAG_LOADED,this.onFragLoaded,this),e.off(v.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),e.off(v.FRAG_DECRYPTED,this.onFragDecrypted,this),e.off(v.INIT_PTS_FOUND,this.onInitPtsFound,this),e.off(v.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),e.off(v.BUFFER_FLUSHING,this.onBufferFlushing,this),this.hls=this.config=null,this.cea608Parser1=this.cea608Parser2=void 0}initCea608Parsers(){if(this.config.enableCEA708Captions&&(!this.cea608Parser1||!this.cea608Parser2)){const e=new Fi(this,"textTrack1"),t=new Fi(this,"textTrack2"),i=new Fi(this,"textTrack3"),n=new Fi(this,"textTrack4");this.cea608Parser1=new ia(1,e,t),this.cea608Parser2=new ia(3,i,n)}}addCues(e,t,i,n,r){let a=!1;for(let o=r.length;o--;){const l=r[o],u=$f(l[0],l[1],t,i);if(u>=0&&(l[0]=Math.min(l[0],t),l[1]=Math.max(l[1],i),a=!0,u/(i-t)>.5))return}if(a||r.push([t,i]),this.config.renderTextTracksNatively){const o=this.captionsTracks[e];this.Cues.newCue(o,t,i,n)}else{const o=this.Cues.newCue(null,t,i,n);this.hls.trigger(v.CUES_PARSED,{type:"captions",cues:o,track:e})}}onInitPtsFound(e,{frag:t,id:i,initPTS:n,timescale:r}){const{unparsedVttFrags:a}=this;i==="main"&&(this.initPTS[t.cc]={baseTime:n,timescale:r}),a.length&&(this.unparsedVttFrags=[],a.forEach(o=>{this.onFragLoaded(v.FRAG_LOADED,o)}))}getExistingTrack(e,t){const{media:i}=this;if(i)for(let n=0;n<i.textTracks.length;n++){const r=i.textTracks[n];if(aa(r,{name:e,lang:t}))return r}return null}createCaptionsTrack(e){this.config.renderTextTracksNatively?this.createNativeTrack(e):this.createNonNativeTrack(e)}createNativeTrack(e){if(this.captionsTracks[e])return;const{captionsProperties:t,captionsTracks:i,media:n}=this,{label:r,languageCode:a}=t[e],o=this.getExistingTrack(r,a);if(o)i[e]=o,zt(i[e]),yo(i[e],n);else{const l=this.createTextTrack("captions",r,a);l&&(l[e]=!0,i[e]=l)}}createNonNativeTrack(e){if(this.nonNativeCaptionsTracks[e])return;const t=this.captionsProperties[e];if(!t)return;const i=t.label,n={_id:e,label:i,kind:"captions",default:t.media?!!t.media.default:!1,closedCaptions:t.media};this.nonNativeCaptionsTracks[e]=n,this.hls.trigger(v.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:[n]})}createTextTrack(e,t,i){const n=this.media;if(n)return n.addTextTrack(e,t,i)}onMediaAttaching(e,t){this.media=t.media,this._cleanTracks()}onMediaDetaching(){const{captionsTracks:e}=this;Object.keys(e).forEach(t=>{zt(e[t]),delete e[t]}),this.nonNativeCaptionsTracks={}}onManifestLoading(){this.lastCc=-1,this.lastSn=-1,this.lastPartIndex=-1,this.prevCC=-1,this.vttCCs=oa(),this._cleanTracks(),this.tracks=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.textTracks=[],this.unparsedVttFrags=[],this.initPTS=[],this.cea608Parser1&&this.cea608Parser2&&(this.cea608Parser1.reset(),this.cea608Parser2.reset())}_cleanTracks(){const{media:e}=this;if(!e)return;const t=e.textTracks;if(t)for(let i=0;i<t.length;i++)zt(t[i])}onSubtitleTracksUpdated(e,t){const i=t.subtitleTracks||[],n=i.some(r=>r.textCodec===qs);if(this.config.enableWebVTT||n&&this.config.enableIMSC1){if(Ho(this.tracks,i)){this.tracks=i;return}if(this.textTracks=[],this.tracks=i,this.config.renderTextTracksNatively){const a=this.media,o=a?qi(a.textTracks):null;if(this.tracks.forEach((l,u)=>{let c;if(o){let d=null;for(let f=0;f<o.length;f++)if(o[f]&&aa(o[f],l)){d=o[f],o[f]=null;break}d&&(c=d)}if(c)zt(c);else{const d=Jo(l);c=this.createTextTrack(d,l.name,l.lang),c&&(c.mode="disabled")}c&&this.textTracks.push(c)}),o!=null&&o.length){const l=o.filter(u=>u!==null).map(u=>u.label);l.length&&_.warn(`Media element contains unused subtitle tracks: ${l.join(", ")}. Replace media element for each source to clear TextTracks and captions menu.`)}}else if(this.tracks.length){const a=this.tracks.map(o=>({label:o.name,kind:o.type.toLowerCase(),default:o.default,subtitleTrack:o}));this.hls.trigger(v.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:a})}}}onManifestLoaded(e,t){this.config.enableCEA708Captions&&t.captions&&t.captions.forEach(i=>{const n=/(?:CC|SERVICE)([1-4])/.exec(i.instreamId);if(!n)return;const r=`textTrack${n[1]}`,a=this.captionsProperties[r];a&&(a.label=i.name,i.lang&&(a.languageCode=i.lang),a.media=i)})}closedCaptionsForLevel(e){const t=this.hls.levels[e.level];return t?.attrs["CLOSED-CAPTIONS"]}onFragLoading(e,t){if(this.enabled&&t.frag.type===ne.MAIN){var i,n;const{cea608Parser1:r,cea608Parser2:a,lastSn:o}=this,{cc:l,sn:u}=t.frag,c=(i=(n=t.part)==null?void 0:n.index)!=null?i:-1;r&&a&&(u!==o+1||u===o&&c!==this.lastPartIndex+1||l!==this.lastCc)&&(r.reset(),a.reset()),this.lastCc=l,this.lastSn=u,this.lastPartIndex=c}}onFragLoaded(e,t){const{frag:i,payload:n}=t;if(i.type===ne.SUBTITLE)if(n.byteLength){const r=i.decryptdata,a="stats"in t;if(r==null||!r.encrypted||a){const o=this.tracks[i.level],l=this.vttCCs;l[i.cc]||(l[i.cc]={start:i.start,prevCC:this.prevCC,new:!0},this.prevCC=i.cc),o&&o.textCodec===qs?this._parseIMSC1(i,n):this._parseVTTs(t)}}else this.hls.trigger(v.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:i,error:new Error("Empty subtitle payload")})}_parseIMSC1(e,t){const i=this.hls;sa(t,this.initPTS[e.cc],n=>{this._appendCues(n,e.level),i.trigger(v.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:e})},n=>{_.log(`Failed to parse IMSC1: ${n}`),i.trigger(v.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:e,error:n})})}_parseVTTs(e){var t;const{frag:i,payload:n}=e,{initPTS:r,unparsedVttFrags:a}=this,o=r.length-1;if(!r[i.cc]&&o===-1){a.push(e);return}const l=this.hls,u=(t=i.initSegment)!=null&&t.data?Ge(i.initSegment.data,new Uint8Array(n)):n;Pf(u,this.initPTS[i.cc],this.vttCCs,i.cc,i.start,c=>{this._appendCues(c,i.level),l.trigger(v.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:i})},c=>{const d=c.message==="Missing initPTS for VTT MPEGTS";d?a.push(e):this._fallbackToIMSC1(i,n),_.log(`Failed to parse VTT cue: ${c}`),!(d&&o>i.cc)&&l.trigger(v.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:i,error:c})})}_fallbackToIMSC1(e,t){const i=this.tracks[e.level];i.textCodec||sa(t,this.initPTS[e.cc],()=>{i.textCodec=qs,this._parseIMSC1(e,t)},()=>{i.textCodec="wvtt"})}_appendCues(e,t){const i=this.hls;if(this.config.renderTextTracksNatively){const n=this.textTracks[t];if(!n||n.mode==="disabled")return;e.forEach(r=>So(n,r))}else{const n=this.tracks[t];if(!n)return;const r=n.default?"default":"subtitles"+t;i.trigger(v.CUES_PARSED,{type:"subtitles",cues:e,track:r})}}onFragDecrypted(e,t){const{frag:i}=t;i.type===ne.SUBTITLE&&this.onFragLoaded(v.FRAG_LOADED,t)}onSubtitleTracksCleared(){this.tracks=[],this.captionsTracks={}}onFragParsingUserdata(e,t){this.initCea608Parsers();const{cea608Parser1:i,cea608Parser2:n}=this;if(!this.enabled||!i||!n)return;const{frag:r,samples:a}=t;if(!(r.type===ne.MAIN&&this.closedCaptionsForLevel(r)==="NONE"))for(let o=0;o<a.length;o++){const l=a[o].bytes;if(l){const u=this.extractCea608Data(l);i.addData(a[o].pts,u[0]),n.addData(a[o].pts,u[1])}}}onBufferFlushing(e,{startOffset:t,endOffset:i,endOffsetSubtitles:n,type:r}){const{media:a}=this;if(!(!a||a.currentTime<i)){if(!r||r==="video"){const{captionsTracks:o}=this;Object.keys(o).forEach(l=>dn(o[l],t,i))}if(this.config.renderTextTracksNatively&&t===0&&n!==void 0){const{textTracks:o}=this;Object.keys(o).forEach(l=>dn(o[l],t,n))}}}extractCea608Data(e){const t=[[],[]],i=e[0]&31;let n=2;for(let r=0;r<i;r++){const a=e[n++],o=127&e[n++],l=127&e[n++];if(o===0&&l===0)continue;if((4&a)!==0){const c=3&a;(c===0||c===1)&&(t[c].push(o),t[c].push(l))}}return t}}function Jo(s){return s.characteristics&&/transcribes-spoken-dialog/gi.test(s.characteristics)&&/describes-music-and-sound/gi.test(s.characteristics)?"captions":"subtitles"}function aa(s,e){return!!s&&s.kind===Jo(e)&&vn(e,s)}function $f(s,e,t,i){return Math.min(e,i)-Math.max(s,t)}function oa(){return{ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!0}}}class Kn{constructor(e){this.hls=void 0,this.autoLevelCapping=void 0,this.firstLevel=void 0,this.media=void 0,this.restrictedLevels=void 0,this.timer=void 0,this.clientRect=void 0,this.streamController=void 0,this.hls=e,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.firstLevel=-1,this.media=null,this.restrictedLevels=[],this.timer=void 0,this.clientRect=null,this.registerListeners()}setStreamController(e){this.streamController=e}destroy(){this.hls&&this.unregisterListener(),this.timer&&this.stopCapping(),this.media=null,this.clientRect=null,this.hls=this.streamController=null}registerListeners(){const{hls:e}=this;e.on(v.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),e.on(v.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(v.MANIFEST_PARSED,this.onManifestParsed,this),e.on(v.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(v.BUFFER_CODECS,this.onBufferCodecs,this),e.on(v.MEDIA_DETACHING,this.onMediaDetaching,this)}unregisterListener(){const{hls:e}=this;e.off(v.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),e.off(v.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(v.MANIFEST_PARSED,this.onManifestParsed,this),e.off(v.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(v.BUFFER_CODECS,this.onBufferCodecs,this),e.off(v.MEDIA_DETACHING,this.onMediaDetaching,this)}onFpsDropLevelCapping(e,t){const i=this.hls.levels[t.droppedLevel];this.isLevelAllowed(i)&&this.restrictedLevels.push({bitrate:i.bitrate,height:i.height,width:i.width})}onMediaAttaching(e,t){this.media=t.media instanceof HTMLVideoElement?t.media:null,this.clientRect=null,this.timer&&this.hls.levels.length&&this.detectPlayerSize()}onManifestParsed(e,t){const i=this.hls;this.restrictedLevels=[],this.firstLevel=t.firstLevel,i.config.capLevelToPlayerSize&&t.video&&this.startCapping()}onLevelsUpdated(e,t){this.timer&&J(this.autoLevelCapping)&&this.detectPlayerSize()}onBufferCodecs(e,t){this.hls.config.capLevelToPlayerSize&&t.video&&this.startCapping()}onMediaDetaching(){this.stopCapping()}detectPlayerSize(){if(this.media){if(this.mediaHeight<=0||this.mediaWidth<=0){this.clientRect=null;return}const e=this.hls.levels;if(e.length){const t=this.hls,i=this.getMaxLevel(e.length-1);i!==this.autoLevelCapping&&_.log(`Setting autoLevelCapping to ${i}: ${e[i].height}p@${e[i].bitrate} for media ${this.mediaWidth}x${this.mediaHeight}`),t.autoLevelCapping=i,t.autoLevelCapping>this.autoLevelCapping&&this.streamController&&this.streamController.nextLevelSwitch(),this.autoLevelCapping=t.autoLevelCapping}}}getMaxLevel(e){const t=this.hls.levels;if(!t.length)return-1;const i=t.filter((n,r)=>this.isLevelAllowed(n)&&r<=e);return this.clientRect=null,Kn.getMaxLevelByMediaSize(i,this.mediaWidth,this.mediaHeight)}startCapping(){this.timer||(this.autoLevelCapping=Number.POSITIVE_INFINITY,self.clearInterval(this.timer),this.timer=self.setInterval(this.detectPlayerSize.bind(this),1e3),this.detectPlayerSize())}stopCapping(){this.restrictedLevels=[],this.firstLevel=-1,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.timer&&(self.clearInterval(this.timer),this.timer=void 0)}getDimensions(){if(this.clientRect)return this.clientRect;const e=this.media,t={width:0,height:0};if(e){const i=e.getBoundingClientRect();t.width=i.width,t.height=i.height,!t.width&&!t.height&&(t.width=i.right-i.left||e.width||0,t.height=i.bottom-i.top||e.height||0)}return this.clientRect=t,t}get mediaWidth(){return this.getDimensions().width*this.contentScaleFactor}get mediaHeight(){return this.getDimensions().height*this.contentScaleFactor}get contentScaleFactor(){let e=1;if(!this.hls.config.ignoreDevicePixelRatio)try{e=self.devicePixelRatio}catch{}return e}isLevelAllowed(e){return!this.restrictedLevels.some(i=>e.bitrate===i.bitrate&&e.width===i.width&&e.height===i.height)}static getMaxLevelByMediaSize(e,t,i){if(!(e!=null&&e.length))return-1;const n=(o,l)=>l?o.width!==l.width||o.height!==l.height:!0;let r=e.length-1;const a=Math.max(t,i);for(let o=0;o<e.length;o+=1){const l=e[o];if((l.width>=a||l.height>=a)&&n(l,e[o+1])){r=o;break}}return r}}class Gf{constructor(e){this.hls=void 0,this.isVideoPlaybackQualityAvailable=!1,this.timer=void 0,this.media=null,this.lastTime=void 0,this.lastDroppedFrames=0,this.lastDecodedFrames=0,this.streamController=void 0,this.hls=e,this.registerListeners()}setStreamController(e){this.streamController=e}registerListeners(){this.hls.on(v.MEDIA_ATTACHING,this.onMediaAttaching,this)}unregisterListeners(){this.hls.off(v.MEDIA_ATTACHING,this.onMediaAttaching,this)}destroy(){this.timer&&clearInterval(this.timer),this.unregisterListeners(),this.isVideoPlaybackQualityAvailable=!1,this.media=null}onMediaAttaching(e,t){const i=this.hls.config;if(i.capLevelOnFPSDrop){const n=t.media instanceof self.HTMLVideoElement?t.media:null;this.media=n,n&&typeof n.getVideoPlaybackQuality=="function"&&(this.isVideoPlaybackQualityAvailable=!0),self.clearInterval(this.timer),this.timer=self.setInterval(this.checkFPSInterval.bind(this),i.fpsDroppedMonitoringPeriod)}}checkFPS(e,t,i){const n=performance.now();if(t){if(this.lastTime){const r=n-this.lastTime,a=i-this.lastDroppedFrames,o=t-this.lastDecodedFrames,l=1e3*a/r,u=this.hls;if(u.trigger(v.FPS_DROP,{currentDropped:a,currentDecoded:o,totalDroppedFrames:i}),l>0&&a>u.config.fpsDroppedMonitoringThreshold*o){let c=u.currentLevel;_.warn("drop FPS ratio greater than max allowed value for currentLevel: "+c),c>0&&(u.autoLevelCapping===-1||u.autoLevelCapping>=c)&&(c=c-1,u.trigger(v.FPS_DROP_LEVEL_CAPPING,{level:c,droppedLevel:u.currentLevel}),u.autoLevelCapping=c,this.streamController.nextLevelSwitch())}}this.lastTime=n,this.lastDroppedFrames=i,this.lastDecodedFrames=t}}checkFPSInterval(){const e=this.media;if(e)if(this.isVideoPlaybackQualityAvailable){const t=e.getVideoPlaybackQuality();this.checkFPS(e,t.totalVideoFrames,t.droppedVideoFrames)}else this.checkFPS(e,e.webkitDecodedFrameCount,e.webkitDroppedFrameCount)}}const Mi="[eme]";class jt{constructor(e){this.hls=void 0,this.config=void 0,this.media=null,this.keyFormatPromise=null,this.keySystemAccessPromises={},this._requestLicenseFailureCount=0,this.mediaKeySessions=[],this.keyIdToKeySessionPromise={},this.setMediaKeysQueue=jt.CDMCleanupPromise?[jt.CDMCleanupPromise]:[],this.debug=_.debug.bind(_,Mi),this.log=_.log.bind(_,Mi),this.warn=_.warn.bind(_,Mi),this.error=_.error.bind(_,Mi),this.onMediaEncrypted=t=>{const{initDataType:i,initData:n}=t,r=`"${t.type}" event: init data type: "${i}"`;if(this.debug(r),n!==null){if(!this.keyFormatPromise){let a=Object.keys(this.keySystemAccessPromises);a.length||(a=Ri(this.config));const o=a.map(ws).filter(l=>!!l);this.keyFormatPromise=this.getKeyFormatPromise(o)}this.keyFormatPromise.then(a=>{const o=Cs(a);let l,u;if(i==="sinf"){if(o!==de.FAIRPLAY){this.warn(`Ignoring unexpected "${t.type}" event with init data type: "${i}" for selected key-system ${o}`);return}const g=Ae(new Uint8Array(n));try{const p=Cn(JSON.parse(g).sinf),m=ho(p);if(!m)throw new Error("'schm' box missing or not cbcs/cenc with schi > tenc");l=m.subarray(8,24),u=de.FAIRPLAY}catch(p){this.warn(`${r} Failed to parse sinf: ${p}`);return}}else{if(o!==de.WIDEVINE&&o!==de.PLAYREADY){this.warn(`Ignoring unexpected "${t.type}" event with init data type: "${i}" for selected key-system ${o}`);return}const g=vc(n),p=g.filter(y=>!!y.systemId&&Ds(y.systemId)===o);p.length>1&&this.warn(`${r} Using first of ${p.length} pssh found for selected key-system ${o}`);const m=p[0];if(!m){g.length===0||g.some(y=>!y.systemId)?this.warn(`${r} contains incomplete or invalid pssh data`):this.log(`ignoring ${r} for ${g.map(y=>Ds(y.systemId)).join(",")} pssh data in favor of playlist keys`);return}if(u=Ds(m.systemId),m.version===0&&m.data)if(u===de.WIDEVINE){const y=m.data.length-22;l=m.data.subarray(y,y+16)}else u===de.PLAYREADY&&(l=no(m.data))}if(!u||!l){this.log(`Unable to handle ${r} with key-system ${o}`);return}const c=Ze.hexDump(l),{keyIdToKeySessionPromise:d,mediaKeySessions:f}=this;let h=d[c];for(let g=0;g<f.length;g++){const p=f[g],m=p.decryptdata;if(!m.keyId)continue;const y=Ze.hexDump(m.keyId);if(c===y||m.uri.replace(/-/g,"").indexOf(c)!==-1){if(h=d[y],m.pssh)break;delete d[y],m.pssh=new Uint8Array(n),m.keyId=l,h=d[c]=h.then(()=>this.generateRequestWithPreferredKeySession(p,i,n,"encrypted-event-key-match")),h.catch(S=>this.handleError(S));break}}if(!h){if(u!==o){this.log(`Ignoring "${r}" with ${u} init data for selected key-system ${o}`);return}h=d[c]=this.getKeySystemSelectionPromise([u]).then(({keySystem:g,mediaKeys:p})=>{var m;this.throwIfDestroyed();const y=new hi("ISO-23001-7",c,(m=ws(g))!=null?m:"");return y.pssh=new Uint8Array(n),y.keyId=l,this.attemptSetMediaKeys(g,p).then(()=>{this.throwIfDestroyed();const S=this.createMediaKeySessionContext({decryptdata:y,keySystem:g,mediaKeys:p});return this.generateRequestWithPreferredKeySession(S,i,n,"encrypted-event-no-match")})}),h.catch(g=>this.handleError(g))}})}},this.onWaitingForKey=t=>{this.log(`"${t.type}" event`)},this.hls=e,this.config=e.config,this.registerListeners()}destroy(){this.unregisterListeners(),this.onMediaDetached();const e=this.config;e.requestMediaKeySystemAccessFunc=null,e.licenseXhrSetup=e.licenseResponseCallback=void 0,e.drmSystems=e.drmSystemOptions={},this.hls=this.config=this.keyIdToKeySessionPromise=null,this.onMediaEncrypted=this.onWaitingForKey=null}registerListeners(){this.hls.on(v.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(v.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.on(v.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(v.MANIFEST_LOADED,this.onManifestLoaded,this)}unregisterListeners(){this.hls.off(v.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(v.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.off(v.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.off(v.MANIFEST_LOADED,this.onManifestLoaded,this)}getLicenseServerUrl(e){const{drmSystems:t,widevineLicenseUrl:i}=this.config,n=t[e];if(n)return n.licenseUrl;if(e===de.WIDEVINE&&i)return i}getLicenseServerUrlOrThrow(e){const t=this.getLicenseServerUrl(e);if(t===void 0)throw new Error(`no license server URL configured for key-system "${e}"`);return t}getServerCertificateUrl(e){const{drmSystems:t}=this.config,i=t[e];if(i)return i.serverCertificateUrl;this.log(`No Server Certificate in config.drmSystems["${e}"]`)}attemptKeySystemAccess(e){const t=this.hls.levels,i=(a,o,l)=>!!a&&l.indexOf(a)===o,n=t.map(a=>a.audioCodec).filter(i),r=t.map(a=>a.videoCodec).filter(i);return n.length+r.length===0&&r.push("avc1.42e01e"),new Promise((a,o)=>{const l=u=>{const c=u.shift();this.getMediaKeysPromise(c,n,r).then(d=>a({keySystem:c,mediaKeys:d})).catch(d=>{u.length?l(u):d instanceof Ue?o(d):o(new Ue({type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_NO_ACCESS,error:d,fatal:!0},d.message))})};l(e)})}requestMediaKeySystemAccess(e,t){const{requestMediaKeySystemAccessFunc:i}=this.config;if(typeof i!="function"){let n=`Configured requestMediaKeySystemAccess is not a function ${i}`;return so===null&&self.location.protocol==="http:"&&(n=`navigator.requestMediaKeySystemAccess is not available over insecure protocol ${location.protocol}`),Promise.reject(new Error(n))}return i(e,t)}getMediaKeysPromise(e,t,i){const n=Wu(e,t,i,this.config.drmSystemOptions),r=this.keySystemAccessPromises[e];let a=r?.keySystemAccess;if(!a){this.log(`Requesting encrypted media "${e}" key-system access with config: ${JSON.stringify(n)}`),a=this.requestMediaKeySystemAccess(e,n);const o=this.keySystemAccessPromises[e]={keySystemAccess:a};return a.catch(l=>{this.log(`Failed to obtain access to key-system "${e}": ${l}`)}),a.then(l=>{this.log(`Access for key-system "${l.keySystem}" obtained`);const u=this.fetchServerCertificate(e);return this.log(`Create media-keys for "${e}"`),o.mediaKeys=l.createMediaKeys().then(c=>(this.log(`Media-keys created for "${e}"`),u.then(d=>d?this.setMediaKeysServerCertificate(c,e,d):c))),o.mediaKeys.catch(c=>{this.error(`Failed to create media-keys for "${e}"}: ${c}`)}),o.mediaKeys})}return a.then(()=>r.mediaKeys)}createMediaKeySessionContext({decryptdata:e,keySystem:t,mediaKeys:i}){this.log(`Creating key-system session "${t}" keyId: ${Ze.hexDump(e.keyId||[])}`);const n=i.createSession(),r={decryptdata:e,keySystem:t,mediaKeys:i,mediaKeysSession:n,keyStatus:"status-pending"};return this.mediaKeySessions.push(r),r}renewKeySession(e){const t=e.decryptdata;if(t.pssh){const i=this.createMediaKeySessionContext(e),n=this.getKeyIdString(t),r="cenc";this.keyIdToKeySessionPromise[n]=this.generateRequestWithPreferredKeySession(i,r,t.pssh,"expired")}else this.warn("Could not renew expired session. Missing pssh initData.");this.removeSession(e)}getKeyIdString(e){if(!e)throw new Error("Could not read keyId of undefined decryptdata");if(e.keyId===null)throw new Error("keyId is null");return Ze.hexDump(e.keyId)}updateKeySession(e,t){var i;const n=e.mediaKeysSession;return this.log(`Updating key-session "${n.sessionId}" for keyID ${Ze.hexDump(((i=e.decryptdata)==null?void 0:i.keyId)||[])}
      } (data length: ${t&&t.byteLength})`),n.update(t)}selectKeySystemFormat(e){const t=Object.keys(e.levelkeys||{});return this.keyFormatPromise||(this.log(`Selecting key-system from fragment (sn: ${e.sn} ${e.type}: ${e.level}) key formats ${t.join(", ")}`),this.keyFormatPromise=this.getKeyFormatPromise(t)),this.keyFormatPromise}getKeyFormatPromise(e){return new Promise((t,i)=>{const n=Ri(this.config),r=e.map(Cs).filter(a=>!!a&&n.indexOf(a)!==-1);return this.getKeySystemSelectionPromise(r).then(({keySystem:a})=>{const o=ws(a);o?t(o):i(new Error(`Unable to find format for key-system "${a}"`))}).catch(i)})}loadKey(e){const t=e.keyInfo.decryptdata,i=this.getKeyIdString(t),n=`(keyId: ${i} format: "${t.keyFormat}" method: ${t.method} uri: ${t.uri})`;this.log(`Starting session for key ${n}`);let r=this.keyIdToKeySessionPromise[i];return r||(r=this.keyIdToKeySessionPromise[i]=this.getKeySystemForKeyPromise(t).then(({keySystem:a,mediaKeys:o})=>(this.throwIfDestroyed(),this.log(`Handle encrypted media sn: ${e.frag.sn} ${e.frag.type}: ${e.frag.level} using key ${n}`),this.attemptSetMediaKeys(a,o).then(()=>{this.throwIfDestroyed();const l=this.createMediaKeySessionContext({keySystem:a,mediaKeys:o,decryptdata:t});return this.generateRequestWithPreferredKeySession(l,"cenc",t.pssh,"playlist-key")}))),r.catch(a=>this.handleError(a))),r}throwIfDestroyed(e="Invalid state"){if(!this.hls)throw new Error("invalid state")}handleError(e){this.hls&&(this.error(e.message),e instanceof Ue?this.hls.trigger(v.ERROR,e.data):this.hls.trigger(v.ERROR,{type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_NO_KEYS,error:e,fatal:!0}))}getKeySystemForKeyPromise(e){const t=this.getKeyIdString(e),i=this.keyIdToKeySessionPromise[t];if(!i){const n=Cs(e.keyFormat),r=n?[n]:Ri(this.config);return this.attemptKeySystemAccess(r)}return i}getKeySystemSelectionPromise(e){if(e.length||(e=Ri(this.config)),e.length===0)throw new Ue({type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_NO_CONFIGURED_LICENSE,fatal:!0},`Missing key-system license configuration options ${JSON.stringify({drmSystems:this.config.drmSystems})}`);return this.attemptKeySystemAccess(e)}attemptSetMediaKeys(e,t){const i=this.setMediaKeysQueue.slice();this.log(`Setting media-keys for "${e}"`);const n=Promise.all(i).then(()=>{if(!this.media)throw new Error("Attempted to set mediaKeys without media element attached");return this.media.setMediaKeys(t)});return this.setMediaKeysQueue.push(n),n.then(()=>{this.log(`Media-keys set for "${e}"`),i.push(n),this.setMediaKeysQueue=this.setMediaKeysQueue.filter(r=>i.indexOf(r)===-1)})}generateRequestWithPreferredKeySession(e,t,i,n){var r,a;const o=(r=this.config.drmSystems)==null||(a=r[e.keySystem])==null?void 0:a.generateRequest;if(o)try{const g=o.call(this.hls,t,i,e);if(!g)throw new Error("Invalid response from configured generateRequest filter");t=g.initDataType,i=e.decryptdata.pssh=g.initData?new Uint8Array(g.initData):null}catch(g){var l;if(this.warn(g.message),(l=this.hls)!=null&&l.config.debug)throw g}if(i===null)return this.log(`Skipping key-session request for "${n}" (no initData)`),Promise.resolve(e);const u=this.getKeyIdString(e.decryptdata);this.log(`Generating key-session request for "${n}": ${u} (init data type: ${t} length: ${i?i.byteLength:null})`);const c=new $n,d=e._onmessage=g=>{const p=e.mediaKeysSession;if(!p){c.emit("error",new Error("invalid state"));return}const{messageType:m,message:y}=g;this.log(`"${m}" message event for session "${p.sessionId}" message size: ${y.byteLength}`),m==="license-request"||m==="license-renewal"?this.renewLicense(e,y).catch(S=>{this.handleError(S),c.emit("error",S)}):m==="license-release"?e.keySystem===de.FAIRPLAY&&(this.updateKeySession(e,un("acknowledged")),this.removeSession(e)):this.warn(`unhandled media key message type "${m}"`)},f=e._onkeystatuseschange=g=>{if(!e.mediaKeysSession){c.emit("error",new Error("invalid state"));return}this.onKeyStatusChange(e);const m=e.keyStatus;c.emit("keyStatus",m),m==="expired"&&(this.warn(`${e.keySystem} expired for key ${u}`),this.renewKeySession(e))};e.mediaKeysSession.addEventListener("message",d),e.mediaKeysSession.addEventListener("keystatuseschange",f);const h=new Promise((g,p)=>{c.on("error",p),c.on("keyStatus",m=>{m.startsWith("usable")?g():m==="output-restricted"?p(new Ue({type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED,fatal:!1},"HDCP level output restricted")):m==="internal-error"?p(new Ue({type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_STATUS_INTERNAL_ERROR,fatal:!0},`key status changed to "${m}"`)):m==="expired"?p(new Error("key expired while generating request")):this.warn(`unhandled key status change "${m}"`)})});return e.mediaKeysSession.generateRequest(t,i).then(()=>{var g;this.log(`Request generated for key-session "${(g=e.mediaKeysSession)==null?void 0:g.sessionId}" keyId: ${u}`)}).catch(g=>{throw new Ue({type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_NO_SESSION,error:g,fatal:!1},`Error generating key-session request: ${g}`)}).then(()=>h).catch(g=>{throw c.removeAllListeners(),this.removeSession(e),g}).then(()=>(c.removeAllListeners(),e))}onKeyStatusChange(e){e.mediaKeysSession.keyStatuses.forEach((t,i)=>{this.log(`key status change "${t}" for keyStatuses keyId: ${Ze.hexDump("buffer"in i?new Uint8Array(i.buffer,i.byteOffset,i.byteLength):new Uint8Array(i))} session keyId: ${Ze.hexDump(new Uint8Array(e.decryptdata.keyId||[]))} uri: ${e.decryptdata.uri}`),e.keyStatus=t})}fetchServerCertificate(e){const t=this.config,i=t.loader,n=new i(t),r=this.getServerCertificateUrl(e);return r?(this.log(`Fetching server certificate for "${e}"`),new Promise((a,o)=>{const l={responseType:"arraybuffer",url:r},u=t.certLoadPolicy.default,c={loadPolicy:u,timeout:u.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0},d={onSuccess:(f,h,g,p)=>{a(f.data)},onError:(f,h,g,p)=>{o(new Ue({type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED,fatal:!0,networkDetails:g,response:Re({url:l.url,data:void 0},f)},`"${e}" certificate request failed (${r}). Status: ${f.code} (${f.text})`))},onTimeout:(f,h,g)=>{o(new Ue({type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED,fatal:!0,networkDetails:g,response:{url:l.url,data:void 0}},`"${e}" certificate request timed out (${r})`))},onAbort:(f,h,g)=>{o(new Error("aborted"))}};n.load(l,c,d)})):Promise.resolve()}setMediaKeysServerCertificate(e,t,i){return new Promise((n,r)=>{e.setServerCertificate(i).then(a=>{this.log(`setServerCertificate ${a?"success":"not supported by CDM"} (${i?.byteLength}) on "${t}"`),n(e)}).catch(a=>{r(new Ue({type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED,error:a,fatal:!0},a.message))})})}renewLicense(e,t){return this.requestLicense(e,new Uint8Array(t)).then(i=>this.updateKeySession(e,new Uint8Array(i)).catch(n=>{throw new Ue({type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_SESSION_UPDATE_FAILED,error:n,fatal:!0},n.message)}))}unpackPlayReadyKeyMessage(e,t){const i=String.fromCharCode.apply(null,new Uint16Array(t.buffer));if(!i.includes("PlayReadyKeyMessage"))return e.setRequestHeader("Content-Type","text/xml; charset=utf-8"),t;const n=new DOMParser().parseFromString(i,"application/xml"),r=n.querySelectorAll("HttpHeader");if(r.length>0){let c;for(let d=0,f=r.length;d<f;d++){var a,o;c=r[d];const h=(a=c.querySelector("name"))==null?void 0:a.textContent,g=(o=c.querySelector("value"))==null?void 0:o.textContent;h&&g&&e.setRequestHeader(h,g)}}const l=n.querySelector("Challenge"),u=l?.textContent;if(!u)throw new Error("Cannot find <Challenge> in key message");return un(atob(u))}setupLicenseXHR(e,t,i,n){const r=this.config.licenseXhrSetup;return r?Promise.resolve().then(()=>{if(!i.decryptdata)throw new Error("Key removed");return r.call(this.hls,e,t,i,n)}).catch(a=>{if(!i.decryptdata)throw a;return e.open("POST",t,!0),r.call(this.hls,e,t,i,n)}).then(a=>(e.readyState||e.open("POST",t,!0),{xhr:e,licenseChallenge:a||n})):(e.open("POST",t,!0),Promise.resolve({xhr:e,licenseChallenge:n}))}requestLicense(e,t){const i=this.config.keyLoadPolicy.default;return new Promise((n,r)=>{const a=this.getLicenseServerUrlOrThrow(e.keySystem);this.log(`Sending license request to URL: ${a}`);const o=new XMLHttpRequest;o.responseType="arraybuffer",o.onreadystatechange=()=>{if(!this.hls||!e.mediaKeysSession)return r(new Error("invalid state"));if(o.readyState===4)if(o.status===200){this._requestLicenseFailureCount=0;let l=o.response;this.log(`License received ${l instanceof ArrayBuffer?l.byteLength:l}`);const u=this.config.licenseResponseCallback;if(u)try{l=u.call(this.hls,o,a,e)}catch(c){this.error(c)}n(l)}else{const l=i.errorRetry,u=l?l.maxNumRetry:0;if(this._requestLicenseFailureCount++,this._requestLicenseFailureCount>u||o.status>=400&&o.status<500)r(new Ue({type:re.KEY_SYSTEM_ERROR,details:O.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0,networkDetails:o,response:{url:a,data:void 0,code:o.status,text:o.statusText}},`License Request XHR failed (${a}). Status: ${o.status} (${o.statusText})`));else{const c=u-this._requestLicenseFailureCount+1;this.warn(`Retrying license request, ${c} attempts left`),this.requestLicense(e,t).then(n,r)}}},e.licenseXhr&&e.licenseXhr.readyState!==XMLHttpRequest.DONE&&e.licenseXhr.abort(),e.licenseXhr=o,this.setupLicenseXHR(o,a,e,t).then(({xhr:l,licenseChallenge:u})=>{e.keySystem==de.PLAYREADY&&(u=this.unpackPlayReadyKeyMessage(l,u)),l.send(u)})})}onMediaAttached(e,t){if(!this.config.emeEnabled)return;const i=t.media;this.media=i,i.removeEventListener("encrypted",this.onMediaEncrypted),i.removeEventListener("waitingforkey",this.onWaitingForKey),i.addEventListener("encrypted",this.onMediaEncrypted),i.addEventListener("waitingforkey",this.onWaitingForKey)}onMediaDetached(){const e=this.media,t=this.mediaKeySessions;e&&(e.removeEventListener("encrypted",this.onMediaEncrypted),e.removeEventListener("waitingforkey",this.onWaitingForKey),this.media=null),this._requestLicenseFailureCount=0,this.setMediaKeysQueue=[],this.mediaKeySessions=[],this.keyIdToKeySessionPromise={},hi.clearKeyUriToKeyIdMap();const i=t.length;jt.CDMCleanupPromise=Promise.all(t.map(n=>this.removeSession(n)).concat(e?.setMediaKeys(null).catch(n=>{this.log(`Could not clear media keys: ${n}`)}))).then(()=>{i&&(this.log("finished closing key sessions and clearing media keys"),t.length=0)}).catch(n=>{this.log(`Could not close sessions and clear media keys: ${n}`)})}onManifestLoading(){this.keyFormatPromise=null}onManifestLoaded(e,{sessionKeys:t}){if(!(!t||!this.config.emeEnabled)&&!this.keyFormatPromise){const i=t.reduce((n,r)=>(n.indexOf(r.keyFormat)===-1&&n.push(r.keyFormat),n),[]);this.log(`Selecting key-system from session-keys ${i.join(", ")}`),this.keyFormatPromise=this.getKeyFormatPromise(i)}}removeSession(e){const{mediaKeysSession:t,licenseXhr:i}=e;if(t){this.log(`Remove licenses and keys and close session ${t.sessionId}`),e._onmessage&&(t.removeEventListener("message",e._onmessage),e._onmessage=void 0),e._onkeystatuseschange&&(t.removeEventListener("keystatuseschange",e._onkeystatuseschange),e._onkeystatuseschange=void 0),i&&i.readyState!==XMLHttpRequest.DONE&&i.abort(),e.mediaKeysSession=e.decryptdata=e.licenseXhr=void 0;const n=this.mediaKeySessions.indexOf(e);return n>-1&&this.mediaKeySessions.splice(n,1),t.remove().catch(r=>{this.log(`Could not remove session: ${r}`)}).then(()=>t.close()).catch(r=>{this.log(`Could not close session: ${r}`)})}}}jt.CDMCleanupPromise=void 0;class Ue extends Error{constructor(e,t){super(t),this.data=void 0,e.error||(e.error=new Error(t)),this.data=e,e.err=e.error}}var we;(function(s){s.MANIFEST="m",s.AUDIO="a",s.VIDEO="v",s.MUXED="av",s.INIT="i",s.CAPTION="c",s.TIMED_TEXT="tt",s.KEY="k",s.OTHER="o"})(we||(we={}));var Sn;(function(s){s.DASH="d",s.HLS="h",s.SMOOTH="s",s.OTHER="o"})(Sn||(Sn={}));var bt;(function(s){s.OBJECT="CMCD-Object",s.REQUEST="CMCD-Request",s.SESSION="CMCD-Session",s.STATUS="CMCD-Status"})(bt||(bt={}));const Vf={[bt.OBJECT]:["br","d","ot","tb"],[bt.REQUEST]:["bl","dl","mtp","nor","nrr","su"],[bt.SESSION]:["cid","pr","sf","sid","st","v"],[bt.STATUS]:["bs","rtp"]};class ei{constructor(e,t){this.value=void 0,this.params=void 0,Array.isArray(e)&&(e=e.map(i=>i instanceof ei?i:new ei(i))),this.value=e,this.params=t}}class el{constructor(e){this.description=void 0,this.description=e}}const Kf="Dict";function Hf(s){return Array.isArray(s)?JSON.stringify(s):s instanceof Map?"Map{}":s instanceof Set?"Set{}":typeof s=="object"?JSON.stringify(s):String(s)}function qf(s,e,t,i){return new Error(`failed to ${s} "${Hf(e)}" as ${t}`,{cause:i})}const la="Bare Item",Wf="Boolean",Yf="Byte Sequence",zf="Decimal",jf="Integer";function Xf(s){return s<-999999999999999||999999999999999<s}const Qf=/[\x00-\x1f\x7f]+/,Zf="Token",Jf="Key";function rt(s,e,t){return qf("serialize",s,e,t)}function eh(s){if(typeof s!="boolean")throw rt(s,Wf);return s?"?1":"?0"}function th(s){return btoa(String.fromCharCode(...s))}function ih(s){if(ArrayBuffer.isView(s)===!1)throw rt(s,Yf);return`:${th(s)}:`}function tl(s){if(Xf(s))throw rt(s,jf);return s.toString()}function sh(s){return`@${tl(s.getTime()/1e3)}`}function il(s,e){if(s<0)return-il(-s,e);const t=Math.pow(10,e);if(Math.abs(s*t%1-.5)<Number.EPSILON){const n=Math.floor(s*t);return(n%2===0?n:n+1)/t}else return Math.round(s*t)/t}function nh(s){const e=il(s,3);if(Math.floor(Math.abs(e)).toString().length>12)throw rt(s,zf);const t=e.toString();return t.includes(".")?t:`${t}.0`}const rh="String";function ah(s){if(Qf.test(s))throw rt(s,rh);return`"${s.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`}function oh(s){return s.description||s.toString().slice(7,-1)}function ua(s){const e=oh(s);if(/^([a-zA-Z*])([!#$%&'*+\-.^_`|~\w:/]*)$/.test(e)===!1)throw rt(e,Zf);return e}function Tn(s){switch(typeof s){case"number":if(!J(s))throw rt(s,la);return Number.isInteger(s)?tl(s):nh(s);case"string":return ah(s);case"symbol":return ua(s);case"boolean":return eh(s);case"object":if(s instanceof Date)return sh(s);if(s instanceof Uint8Array)return ih(s);if(s instanceof el)return ua(s);default:throw rt(s,la)}}function xn(s){if(/^[a-z*][a-z0-9\-_.*]*$/.test(s)===!1)throw rt(s,Jf);return s}function Hn(s){return s==null?"":Object.entries(s).map(([e,t])=>t===!0?`;${xn(e)}`:`;${xn(e)}=${Tn(t)}`).join("")}function sl(s){return s instanceof ei?`${Tn(s.value)}${Hn(s.params)}`:Tn(s)}function lh(s){return`(${s.value.map(sl).join(" ")})${Hn(s.params)}`}function uh(s,e={whitespace:!0}){if(typeof s!="object")throw rt(s,Kf);const t=s instanceof Map?s.entries():Object.entries(s),i=e!=null&&e.whitespace?" ":"";return Array.from(t).map(([n,r])=>{r instanceof ei||(r=new ei(r));let a=xn(n);return r.value===!0?a+=Hn(r.params):(a+="=",Array.isArray(r.value)?a+=lh(r):a+=sl(r)),a}).join(`,${i}`)}function ch(s,e){return uh(s,e)}const dh=s=>s==="ot"||s==="sf"||s==="st",fh=s=>typeof s=="number"?J(s):s!=null&&s!==""&&s!==!1;function hh(s,e){const t=new URL(s),i=new URL(e);if(t.origin!==i.origin)return s;const n=t.pathname.split("/").slice(1),r=i.pathname.split("/").slice(1,-1);for(;n[0]===r[0];)n.shift(),r.shift();for(;r.length;)r.shift(),n.unshift("..");return n.join("/")}function gh(){try{return crypto.randomUUID()}catch{try{const e=URL.createObjectURL(new Blob),t=e.toString();return URL.revokeObjectURL(e),t.slice(t.lastIndexOf("/")+1)}catch{let t=new Date().getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,n=>{const r=(t+Math.random()*16)%16|0;return t=Math.floor(t/16),(n=="x"?r:r&3|8).toString(16)})}}}const Qi=s=>Math.round(s),mh=(s,e)=>(e!=null&&e.baseUrl&&(s=hh(s,e.baseUrl)),encodeURIComponent(s)),Ni=s=>Qi(s/100)*100,ph={br:Qi,d:Qi,bl:Ni,dl:Ni,mtp:Ni,nor:mh,rtp:Ni,tb:Qi};function vh(s,e){const t={};if(s==null||typeof s!="object")return t;const i=Object.keys(s).sort(),n=xe({},ph,e?.formatters),r=e?.filter;return i.forEach(a=>{if(r!=null&&r(a))return;let o=s[a];const l=n[a];l&&(o=l(o,e)),!(a==="v"&&o===1)&&(a=="pr"&&o===1||fh(o)&&(dh(a)&&typeof o=="string"&&(o=new el(o)),t[a]=o))}),t}function nl(s,e={}){return s?ch(vh(s,e),xe({whitespace:!1},e)):""}function yh(s,e={}){if(!s)return{};const t=Object.entries(s),i=Object.entries(Vf).concat(Object.entries(e?.customHeaderMap||{})),n=t.reduce((r,a)=>{var o,l;const[u,c]=a,d=((o=i.find(f=>f[1].includes(u)))==null?void 0:o[0])||bt.REQUEST;return(l=r[d])!=null||(r[d]={}),r[d][u]=c,r},{});return Object.entries(n).reduce((r,[a,o])=>(r[a]=nl(o,e),r),{})}function Sh(s,e,t){return xe(s,yh(e,t))}const Th="CMCD";function xh(s,e={}){if(!s)return"";const t=nl(s,e);return`${Th}=${encodeURIComponent(t)}`}const ca=/CMCD=[^&#]+/;function Eh(s,e,t){const i=xh(e,t);if(!i)return s;if(ca.test(s))return s.replace(ca,i);const n=s.includes("?")?"&":"?";return`${s}${n}${i}`}class Ah{constructor(e){this.hls=void 0,this.config=void 0,this.media=void 0,this.sid=void 0,this.cid=void 0,this.useHeaders=!1,this.includeKeys=void 0,this.initialized=!1,this.starved=!1,this.buffering=!0,this.audioBuffer=void 0,this.videoBuffer=void 0,this.onWaiting=()=>{this.initialized&&(this.starved=!0),this.buffering=!0},this.onPlaying=()=>{this.initialized||(this.initialized=!0),this.buffering=!1},this.applyPlaylistData=n=>{try{this.apply(n,{ot:we.MANIFEST,su:!this.initialized})}catch(r){_.warn("Could not generate manifest CMCD data.",r)}},this.applyFragmentData=n=>{try{const r=n.frag,a=this.hls.levels[r.level],o=this.getObjectType(r),l={d:r.duration*1e3,ot:o};(o===we.VIDEO||o===we.AUDIO||o==we.MUXED)&&(l.br=a.bitrate/1e3,l.tb=this.getTopBandwidth(o)/1e3,l.bl=this.getBufferLength(o)),this.apply(n,l)}catch(r){_.warn("Could not generate segment CMCD data.",r)}},this.hls=e;const t=this.config=e.config,{cmcd:i}=t;i!=null&&(t.pLoader=this.createPlaylistLoader(),t.fLoader=this.createFragmentLoader(),this.sid=i.sessionId||gh(),this.cid=i.contentId,this.useHeaders=i.useHeaders===!0,this.includeKeys=i.includeKeys,this.registerListeners())}registerListeners(){const e=this.hls;e.on(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(v.MEDIA_DETACHED,this.onMediaDetached,this),e.on(v.BUFFER_CREATED,this.onBufferCreated,this)}unregisterListeners(){const e=this.hls;e.off(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(v.MEDIA_DETACHED,this.onMediaDetached,this),e.off(v.BUFFER_CREATED,this.onBufferCreated,this)}destroy(){this.unregisterListeners(),this.onMediaDetached(),this.hls=this.config=this.audioBuffer=this.videoBuffer=null,this.onWaiting=this.onPlaying=null}onMediaAttached(e,t){this.media=t.media,this.media.addEventListener("waiting",this.onWaiting),this.media.addEventListener("playing",this.onPlaying)}onMediaDetached(){this.media&&(this.media.removeEventListener("waiting",this.onWaiting),this.media.removeEventListener("playing",this.onPlaying),this.media=null)}onBufferCreated(e,t){var i,n;this.audioBuffer=(i=t.tracks.audio)==null?void 0:i.buffer,this.videoBuffer=(n=t.tracks.video)==null?void 0:n.buffer}createData(){var e;return{v:1,sf:Sn.HLS,sid:this.sid,cid:this.cid,pr:(e=this.media)==null?void 0:e.playbackRate,mtp:this.hls.bandwidthEstimate/1e3}}apply(e,t={}){xe(t,this.createData());const i=t.ot===we.INIT||t.ot===we.VIDEO||t.ot===we.MUXED;this.starved&&i&&(t.bs=!0,t.su=!0,this.starved=!1),t.su==null&&(t.su=this.buffering);const{includeKeys:n}=this;n&&(t=Object.keys(t).reduce((r,a)=>(n.includes(a)&&(r[a]=t[a]),r),{})),this.useHeaders?(e.headers||(e.headers={}),Sh(e.headers,t)):e.url=Eh(e.url,t)}getObjectType(e){const{type:t}=e;if(t==="subtitle")return we.TIMED_TEXT;if(e.sn==="initSegment")return we.INIT;if(t==="audio")return we.AUDIO;if(t==="main")return this.hls.audioTracks.length?we.VIDEO:we.MUXED}getTopBandwidth(e){let t=0,i;const n=this.hls;if(e===we.AUDIO)i=n.audioTracks;else{const r=n.maxAutoLevel,a=r>-1?r+1:n.levels.length;i=n.levels.slice(0,a)}for(const r of i)r.bitrate>t&&(t=r.bitrate);return t>0?t:NaN}getBufferLength(e){const t=this.hls.media,i=e===we.AUDIO?this.audioBuffer:this.videoBuffer;return!i||!t?NaN:me.bufferInfo(i,t.currentTime,this.config.maxBufferHole).len*1e3}createPlaylistLoader(){const{pLoader:e}=this.config,t=this.applyPlaylistData,i=e||this.config.loader;return class{constructor(r){this.loader=void 0,this.loader=new i(r)}get stats(){return this.loader.stats}get context(){return this.loader.context}destroy(){this.loader.destroy()}abort(){this.loader.abort()}load(r,a,o){t(r),this.loader.load(r,a,o)}}}createFragmentLoader(){const{fLoader:e}=this.config,t=this.applyFragmentData,i=e||this.config.loader;return class{constructor(r){this.loader=void 0,this.loader=new i(r)}get stats(){return this.loader.stats}get context(){return this.loader.context}destroy(){this.loader.destroy()}abort(){this.loader.abort()}load(r,a,o){t(r),this.loader.load(r,a,o)}}}}const bh=3e5;class Lh{constructor(e){this.hls=void 0,this.log=void 0,this.loader=null,this.uri=null,this.pathwayId=".",this.pathwayPriority=null,this.timeToLoad=300,this.reloadTimer=-1,this.updated=0,this.started=!1,this.enabled=!0,this.levels=null,this.audioTracks=null,this.subtitleTracks=null,this.penalizedPathways={},this.hls=e,this.log=_.log.bind(_,"[content-steering]:"),this.registerListeners()}registerListeners(){const e=this.hls;e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.MANIFEST_LOADED,this.onManifestLoaded,this),e.on(v.MANIFEST_PARSED,this.onManifestParsed,this),e.on(v.ERROR,this.onError,this)}unregisterListeners(){const e=this.hls;e&&(e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.MANIFEST_LOADED,this.onManifestLoaded,this),e.off(v.MANIFEST_PARSED,this.onManifestParsed,this),e.off(v.ERROR,this.onError,this))}startLoad(){if(this.started=!0,this.clearTimeout(),this.enabled&&this.uri){if(this.updated){const e=this.timeToLoad*1e3-(performance.now()-this.updated);if(e>0){this.scheduleRefresh(this.uri,e);return}}this.loadSteeringManifest(this.uri)}}stopLoad(){this.started=!1,this.loader&&(this.loader.destroy(),this.loader=null),this.clearTimeout()}clearTimeout(){this.reloadTimer!==-1&&(self.clearTimeout(this.reloadTimer),this.reloadTimer=-1)}destroy(){this.unregisterListeners(),this.stopLoad(),this.hls=null,this.levels=this.audioTracks=this.subtitleTracks=null}removeLevel(e){const t=this.levels;t&&(this.levels=t.filter(i=>i!==e))}onManifestLoading(){this.stopLoad(),this.enabled=!0,this.timeToLoad=300,this.updated=0,this.uri=null,this.pathwayId=".",this.levels=this.audioTracks=this.subtitleTracks=null}onManifestLoaded(e,t){const{contentSteering:i}=t;i!==null&&(this.pathwayId=i.pathwayId,this.uri=i.uri,this.started&&this.startLoad())}onManifestParsed(e,t){this.audioTracks=t.audioTracks,this.subtitleTracks=t.subtitleTracks}onError(e,t){const{errorAction:i}=t;if(i?.action===De.SendAlternateToPenaltyBox&&i.flags===Ve.MoveAllAlternatesMatchingHost){const n=this.levels;let r=this.pathwayPriority,a=this.pathwayId;if(t.context){const{groupId:o,pathwayId:l,type:u}=t.context;o&&n?a=this.getPathwayForGroupId(o,u,a):l&&(a=l)}a in this.penalizedPathways||(this.penalizedPathways[a]=performance.now()),!r&&n&&(r=n.reduce((o,l)=>(o.indexOf(l.pathwayId)===-1&&o.push(l.pathwayId),o),[])),r&&r.length>1&&(this.updatePathwayPriority(r),i.resolved=this.pathwayId!==a),i.resolved||_.warn(`Could not resolve ${t.details} ("${t.error.message}") with content-steering for Pathway: ${a} levels: ${n&&n.length} priorities: ${JSON.stringify(r)} penalized: ${JSON.stringify(this.penalizedPathways)}`)}}filterParsedLevels(e){this.levels=e;let t=this.getLevelsForPathway(this.pathwayId);if(t.length===0){const i=e[0].pathwayId;this.log(`No levels found in Pathway ${this.pathwayId}. Setting initial Pathway to "${i}"`),t=this.getLevelsForPathway(i),this.pathwayId=i}return t.length!==e.length&&this.log(`Found ${t.length}/${e.length} levels in Pathway "${this.pathwayId}"`),t}getLevelsForPathway(e){return this.levels===null?[]:this.levels.filter(t=>e===t.pathwayId)}updatePathwayPriority(e){this.pathwayPriority=e;let t;const i=this.penalizedPathways,n=performance.now();Object.keys(i).forEach(r=>{n-i[r]>bh&&delete i[r]});for(let r=0;r<e.length;r++){const a=e[r];if(a in i)continue;if(a===this.pathwayId)return;const o=this.hls.nextLoadLevel,l=this.hls.levels[o];if(t=this.getLevelsForPathway(a),t.length>0){this.log(`Setting Pathway to "${a}"`),this.pathwayId=a,Ao(t),this.hls.trigger(v.LEVELS_UPDATED,{levels:t});const u=this.hls.levels[o];l&&u&&this.levels&&(u.attrs["STABLE-VARIANT-ID"]!==l.attrs["STABLE-VARIANT-ID"]&&u.bitrate!==l.bitrate&&this.log(`Unstable Pathways change from bitrate ${l.bitrate} to ${u.bitrate}`),this.hls.nextLoadLevel=o);break}}}getPathwayForGroupId(e,t,i){const n=this.getLevelsForPathway(i).concat(this.levels||[]);for(let r=0;r<n.length;r++)if(t===ce.AUDIO_TRACK&&n[r].hasAudioGroup(e)||t===ce.SUBTITLE_TRACK&&n[r].hasSubtitleGroup(e))return n[r].pathwayId;return i}clonePathways(e){const t=this.levels;if(!t)return;const i={},n={};e.forEach(r=>{const{ID:a,"BASE-ID":o,"URI-REPLACEMENT":l}=r;if(t.some(c=>c.pathwayId===a))return;const u=this.getLevelsForPathway(o).map(c=>{const d=new pe(c.attrs);d["PATHWAY-ID"]=a;const f=d.AUDIO&&`${d.AUDIO}_clone_${a}`,h=d.SUBTITLES&&`${d.SUBTITLES}_clone_${a}`;f&&(i[d.AUDIO]=f,d.AUDIO=f),h&&(n[d.SUBTITLES]=h,d.SUBTITLES=h);const g=rl(c.uri,d["STABLE-VARIANT-ID"],"PER-VARIANT-URIS",l),p=new Jt({attrs:d,audioCodec:c.audioCodec,bitrate:c.bitrate,height:c.height,name:c.name,url:g,videoCodec:c.videoCodec,width:c.width});if(c.audioGroups)for(let m=1;m<c.audioGroups.length;m++)p.addGroupId("audio",`${c.audioGroups[m]}_clone_${a}`);if(c.subtitleGroups)for(let m=1;m<c.subtitleGroups.length;m++)p.addGroupId("text",`${c.subtitleGroups[m]}_clone_${a}`);return p});t.push(...u),da(this.audioTracks,i,l,a),da(this.subtitleTracks,n,l,a)})}loadSteeringManifest(e){const t=this.hls.config,i=t.loader;this.loader&&this.loader.destroy(),this.loader=new i(t);let n;try{n=new self.URL(e)}catch{this.enabled=!1,this.log(`Failed to parse Steering Manifest URI: ${e}`);return}if(n.protocol!=="data:"){const c=(this.hls.bandwidthEstimate||t.abrEwmaDefaultEstimate)|0;n.searchParams.set("_HLS_pathway",this.pathwayId),n.searchParams.set("_HLS_throughput",""+c)}const r={responseType:"json",url:n.href},a=t.steeringManifestLoadPolicy.default,o=a.errorRetry||a.timeoutRetry||{},l={loadPolicy:a,timeout:a.maxLoadTimeMs,maxRetry:o.maxNumRetry||0,retryDelay:o.retryDelayMs||0,maxRetryDelay:o.maxRetryDelayMs||0},u={onSuccess:(c,d,f,h)=>{this.log(`Loaded steering manifest: "${n}"`);const g=c.data;if(g.VERSION!==1){this.log(`Steering VERSION ${g.VERSION} not supported!`);return}this.updated=performance.now(),this.timeToLoad=g.TTL;const{"RELOAD-URI":p,"PATHWAY-CLONES":m,"PATHWAY-PRIORITY":y}=g;if(p)try{this.uri=new self.URL(p,n).href}catch{this.enabled=!1,this.log(`Failed to parse Steering Manifest RELOAD-URI: ${p}`);return}this.scheduleRefresh(this.uri||f.url),m&&this.clonePathways(m);const S={steeringManifest:g,url:n.toString()};this.hls.trigger(v.STEERING_MANIFEST_LOADED,S),y&&this.updatePathwayPriority(y)},onError:(c,d,f,h)=>{if(this.log(`Error loading steering manifest: ${c.code} ${c.text} (${d.url})`),this.stopLoad(),c.code===410){this.enabled=!1,this.log(`Steering manifest ${d.url} no longer available`);return}let g=this.timeToLoad*1e3;if(c.code===429){const p=this.loader;if(typeof p?.getResponseHeader=="function"){const m=p.getResponseHeader("Retry-After");m&&(g=parseFloat(m)*1e3)}this.log(`Steering manifest ${d.url} rate limited`);return}this.scheduleRefresh(this.uri||d.url,g)},onTimeout:(c,d,f)=>{this.log(`Timeout loading steering manifest (${d.url})`),this.scheduleRefresh(this.uri||d.url)}};this.log(`Requesting steering manifest: ${n}`),this.loader.load(r,l,u)}scheduleRefresh(e,t=this.timeToLoad*1e3){this.clearTimeout(),this.reloadTimer=self.setTimeout(()=>{var i;const n=(i=this.hls)==null?void 0:i.media;if(n&&!n.ended){this.loadSteeringManifest(e);return}this.scheduleRefresh(e,this.timeToLoad*1e3)},t)}}function da(s,e,t,i){s&&Object.keys(e).forEach(n=>{const r=s.filter(a=>a.groupId===n).map(a=>{const o=xe({},a);return o.details=void 0,o.attrs=new pe(o.attrs),o.url=o.attrs.URI=rl(a.url,a.attrs["STABLE-RENDITION-ID"],"PER-RENDITION-URIS",t),o.groupId=o.attrs["GROUP-ID"]=e[n],o.attrs["PATHWAY-ID"]=i,o});s.push(...r)})}function rl(s,e,t,i){const{HOST:n,PARAMS:r,[t]:a}=i;let o;e&&(o=a?.[e],o&&(s=o));const l=new self.URL(s);return n&&!o&&(l.host=n),r&&Object.keys(r).sort().forEach(u=>{u&&l.searchParams.set(u,r[u])}),l.href}const Rh=/^age:\s*[\d.]+\s*$/im;class al{constructor(e){this.xhrSetup=void 0,this.requestTimeout=void 0,this.retryTimeout=void 0,this.retryDelay=void 0,this.config=null,this.callbacks=null,this.context=null,this.loader=null,this.stats=void 0,this.xhrSetup=e&&e.xhrSetup||null,this.stats=new vs,this.retryDelay=0}destroy(){this.callbacks=null,this.abortInternal(),this.loader=null,this.config=null,this.context=null,this.xhrSetup=null}abortInternal(){const e=this.loader;self.clearTimeout(this.requestTimeout),self.clearTimeout(this.retryTimeout),e&&(e.onreadystatechange=null,e.onprogress=null,e.readyState!==4&&(this.stats.aborted=!0,e.abort()))}abort(){var e;this.abortInternal(),(e=this.callbacks)!=null&&e.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.loader)}load(e,t,i){if(this.stats.loading.start)throw new Error("Loader can only be used once.");this.stats.loading.start=self.performance.now(),this.context=e,this.config=t,this.callbacks=i,this.loadInternal()}loadInternal(){const{config:e,context:t}=this;if(!e||!t)return;const i=this.loader=new self.XMLHttpRequest,n=this.stats;n.loading.first=0,n.loaded=0,n.aborted=!1;const r=this.xhrSetup;r?Promise.resolve().then(()=>{if(!(this.loader!==i||this.stats.aborted))return r(i,t.url)}).catch(a=>{if(!(this.loader!==i||this.stats.aborted))return i.open("GET",t.url,!0),r(i,t.url)}).then(()=>{this.loader!==i||this.stats.aborted||this.openAndSendXhr(i,t,e)}).catch(a=>{this.callbacks.onError({code:i.status,text:a.message},t,i,n)}):this.openAndSendXhr(i,t,e)}openAndSendXhr(e,t,i){e.readyState||e.open("GET",t.url,!0);const n=t.headers,{maxTimeToFirstByteMs:r,maxLoadTimeMs:a}=i.loadPolicy;if(n)for(const o in n)e.setRequestHeader(o,n[o]);t.rangeEnd&&e.setRequestHeader("Range","bytes="+t.rangeStart+"-"+(t.rangeEnd-1)),e.onreadystatechange=this.readystatechange.bind(this),e.onprogress=this.loadprogress.bind(this),e.responseType=t.responseType,self.clearTimeout(this.requestTimeout),i.timeout=r&&J(r)?r:a,this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),i.timeout),e.send()}readystatechange(){const{context:e,loader:t,stats:i}=this;if(!e||!t)return;const n=t.readyState,r=this.config;if(!i.aborted&&n>=2&&(i.loading.first===0&&(i.loading.first=Math.max(self.performance.now(),i.loading.start),r.timeout!==r.loadPolicy.maxLoadTimeMs&&(self.clearTimeout(this.requestTimeout),r.timeout=r.loadPolicy.maxLoadTimeMs,this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),r.loadPolicy.maxLoadTimeMs-(i.loading.first-i.loading.start)))),n===4)){self.clearTimeout(this.requestTimeout),t.onreadystatechange=null,t.onprogress=null;const a=t.status,o=t.responseType==="text"?t.responseText:null;if(a>=200&&a<300){const d=o??t.response;if(d!=null){i.loading.end=Math.max(self.performance.now(),i.loading.first);const f=t.responseType==="arraybuffer"?d.byteLength:d.length;if(i.loaded=i.total=f,i.bwEstimate=i.total*8e3/(i.loading.end-i.loading.first),!this.callbacks)return;const h=this.callbacks.onProgress;if(h&&h(i,e,d,t),!this.callbacks)return;const g={url:t.responseURL,data:d,code:a};this.callbacks.onSuccess(g,i,e,t);return}}const l=r.loadPolicy.errorRetry,u=i.retry,c={url:e.url,data:void 0,code:a};as(l,u,!1,c)?this.retry(l):(_.error(`${a} while loading ${e.url}`),this.callbacks.onError({code:a,text:t.statusText},e,t,i))}}loadtimeout(){if(!this.config)return;const e=this.config.loadPolicy.timeoutRetry,t=this.stats.retry;if(as(e,t,!0))this.retry(e);else{var i;_.warn(`timeout while loading ${(i=this.context)==null?void 0:i.url}`);const n=this.callbacks;n&&(this.abortInternal(),n.onTimeout(this.stats,this.context,this.loader))}}retry(e){const{context:t,stats:i}=this;this.retryDelay=kn(e,i.retry),i.retry++,_.warn(`${status?"HTTP Status "+status:"Timeout"} while loading ${t?.url}, retrying ${i.retry}/${e.maxNumRetry} in ${this.retryDelay}ms`),this.abortInternal(),this.loader=null,self.clearTimeout(this.retryTimeout),this.retryTimeout=self.setTimeout(this.loadInternal.bind(this),this.retryDelay)}loadprogress(e){const t=this.stats;t.loaded=e.loaded,e.lengthComputable&&(t.total=e.total)}getCacheAge(){let e=null;if(this.loader&&Rh.test(this.loader.getAllResponseHeaders())){const t=this.loader.getResponseHeader("age");e=t?parseFloat(t):null}return e}getResponseHeader(e){return this.loader&&new RegExp(`^${e}:\\s*[\\d.]+\\s*$`,"im").test(this.loader.getAllResponseHeaders())?this.loader.getResponseHeader(e):null}}function Ih(){if(self.fetch&&self.AbortController&&self.ReadableStream&&self.Request)try{return new self.ReadableStream({}),!0}catch{}return!1}const Ch=/(\d+)-(\d+)\/(\d+)/;class fa{constructor(e){this.fetchSetup=void 0,this.requestTimeout=void 0,this.request=null,this.response=null,this.controller=void 0,this.context=null,this.config=null,this.callbacks=null,this.stats=void 0,this.loader=null,this.fetchSetup=e.fetchSetup||_h,this.controller=new self.AbortController,this.stats=new vs}destroy(){this.loader=this.callbacks=this.context=this.config=this.request=null,this.abortInternal(),this.response=null,this.fetchSetup=this.controller=this.stats=null}abortInternal(){this.controller&&!this.stats.loading.end&&(this.stats.aborted=!0,this.controller.abort())}abort(){var e;this.abortInternal(),(e=this.callbacks)!=null&&e.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.response)}load(e,t,i){const n=this.stats;if(n.loading.start)throw new Error("Loader can only be used once.");n.loading.start=self.performance.now();const r=Dh(e,this.controller.signal),a=i.onProgress,o=e.responseType==="arraybuffer",l=o?"byteLength":"length",{maxTimeToFirstByteMs:u,maxLoadTimeMs:c}=t.loadPolicy;this.context=e,this.config=t,this.callbacks=i,this.request=this.fetchSetup(e,r),self.clearTimeout(this.requestTimeout),t.timeout=u&&J(u)?u:c,this.requestTimeout=self.setTimeout(()=>{this.abortInternal(),i.onTimeout(n,e,this.response)},t.timeout),self.fetch(this.request).then(d=>{this.response=this.loader=d;const f=Math.max(self.performance.now(),n.loading.start);if(self.clearTimeout(this.requestTimeout),t.timeout=c,this.requestTimeout=self.setTimeout(()=>{this.abortInternal(),i.onTimeout(n,e,this.response)},c-(f-n.loading.start)),!d.ok){const{status:h,statusText:g}=d;throw new Ph(g||"fetch, bad network response",h,d)}return n.loading.first=f,n.total=kh(d.headers)||n.total,a&&J(t.highWaterMark)?this.loadProgressively(d,n,e,t.highWaterMark,a):o?d.arrayBuffer():e.responseType==="json"?d.json():d.text()}).then(d=>{const f=this.response;if(!f)throw new Error("loader destroyed");self.clearTimeout(this.requestTimeout),n.loading.end=Math.max(self.performance.now(),n.loading.first);const h=d[l];h&&(n.loaded=n.total=h);const g={url:f.url,data:d,code:f.status};a&&!J(t.highWaterMark)&&a(n,e,d,f),i.onSuccess(g,n,e,f)}).catch(d=>{if(self.clearTimeout(this.requestTimeout),n.aborted)return;const f=d&&d.code||0,h=d?d.message:null;i.onError({code:f,text:h},e,d?d.details:null,n)})}getCacheAge(){let e=null;if(this.response){const t=this.response.headers.get("age");e=t?parseFloat(t):null}return e}getResponseHeader(e){return this.response?this.response.headers.get(e):null}loadProgressively(e,t,i,n=0,r){const a=new Io,o=e.body.getReader(),l=()=>o.read().then(u=>{if(u.done)return a.dataLength&&r(t,i,a.flush(),e),Promise.resolve(new ArrayBuffer(0));const c=u.value,d=c.length;return t.loaded+=d,d<n||a.dataLength?(a.push(c),a.dataLength>=n&&r(t,i,a.flush(),e)):r(t,i,c,e),l()}).catch(()=>Promise.reject());return l()}}function Dh(s,e){const t={method:"GET",mode:"cors",credentials:"same-origin",signal:e,headers:new self.Headers(xe({},s.headers))};return s.rangeEnd&&t.headers.set("Range","bytes="+s.rangeStart+"-"+String(s.rangeEnd-1)),t}function wh(s){const e=Ch.exec(s);if(e)return parseInt(e[2])-parseInt(e[1])+1}function kh(s){const e=s.get("Content-Range");if(e){const i=wh(e);if(J(i))return i}const t=s.get("Content-Length");if(t)return parseInt(t)}function _h(s,e){return new self.Request(s.url,e)}class Ph extends Error{constructor(e,t,i){super(e),this.code=void 0,this.details=void 0,this.code=t,this.details=i}}const Fh=/\s/,Oh={newCue(s,e,t,i){const n=[];let r,a,o,l,u;const c=self.VTTCue||self.TextTrackCue;for(let f=0;f<i.rows.length;f++)if(r=i.rows[f],o=!0,l=0,u="",!r.isEmpty()){var d;for(let p=0;p<r.chars.length;p++)Fh.test(r.chars[p].uchar)&&o?l++:(u+=r.chars[p].uchar,o=!1);r.cueStartTime=e,e===t&&(t+=1e-4),l>=16?l--:l++;const h=jo(u.trim()),g=Vn(e,t,h);s!=null&&(d=s.cues)!=null&&d.getCueById(g)||(a=new c(e,t,h),a.id=g,a.line=f+1,a.align="left",a.position=10+Math.min(80,Math.floor(l*8/32)*10),n.push(a))}return s&&n.length&&(n.sort((f,h)=>f.line==="auto"||h.line==="auto"?0:f.line>8&&h.line>8?h.line-f.line:f.line-h.line),n.forEach(f=>So(s,f))),n}},Mh={maxTimeToFirstByteMs:8e3,maxLoadTimeMs:2e4,timeoutRetry:null,errorRetry:null},ol=Re(Re({autoStartLoad:!0,startPosition:-1,defaultAudioCodec:void 0,debug:!1,capLevelOnFPSDrop:!1,capLevelToPlayerSize:!1,ignoreDevicePixelRatio:!1,preferManagedMediaSource:!0,initialLiveManifestSize:1,maxBufferLength:30,backBufferLength:1/0,frontBufferFlushThreshold:1/0,maxBufferSize:60*1e3*1e3,maxBufferHole:.1,highBufferWatchdogPeriod:2,nudgeOffset:.1,nudgeMaxRetry:3,maxFragLookUpTolerance:.25,liveSyncDurationCount:3,liveMaxLatencyDurationCount:1/0,liveSyncDuration:void 0,liveMaxLatencyDuration:void 0,maxLiveSyncPlaybackRate:1,liveDurationInfinity:!1,liveBackBufferLength:null,maxMaxBufferLength:600,enableWorker:!0,workerPath:null,enableSoftwareAES:!0,startLevel:void 0,startFragPrefetch:!1,fpsDroppedMonitoringPeriod:5e3,fpsDroppedMonitoringThreshold:.2,appendErrorMaxRetry:3,loader:al,fLoader:void 0,pLoader:void 0,xhrSetup:void 0,licenseXhrSetup:void 0,licenseResponseCallback:void 0,abrController:ld,bufferController:hf,capLevelController:Kn,errorController:Xc,fpsController:Gf,stretchShortVideoTrack:!1,maxAudioFramesDrift:1,forceKeyFrameOnDiscontinuity:!0,abrEwmaFastLive:3,abrEwmaSlowLive:9,abrEwmaFastVoD:3,abrEwmaSlowVoD:9,abrEwmaDefaultEstimate:5e5,abrEwmaDefaultEstimateMax:5e6,abrBandWidthFactor:.95,abrBandWidthUpFactor:.7,abrMaxWithRealBitrate:!1,maxStarvationDelay:4,maxLoadingDelay:4,minAutoBitrate:0,emeEnabled:!1,widevineLicenseUrl:void 0,drmSystems:{},drmSystemOptions:{},requestMediaKeySystemAccessFunc:so,testBandwidth:!0,progressive:!1,lowLatencyMode:!0,cmcd:void 0,enableDateRangeMetadataCues:!0,enableEmsgMetadataCues:!0,enableID3MetadataCues:!0,useMediaCapabilities:!0,certLoadPolicy:{default:Mh},keyLoadPolicy:{default:{maxTimeToFirstByteMs:8e3,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:2e4,backoff:"linear"},errorRetry:{maxNumRetry:8,retryDelayMs:1e3,maxRetryDelayMs:2e4,backoff:"linear"}}},manifestLoadPolicy:{default:{maxTimeToFirstByteMs:1/0,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},playlistLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:2,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},fragLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:12e4,timeoutRetry:{maxNumRetry:4,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:6,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},steeringManifestLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},manifestLoadingTimeOut:1e4,manifestLoadingMaxRetry:1,manifestLoadingRetryDelay:1e3,manifestLoadingMaxRetryTimeout:64e3,levelLoadingTimeOut:1e4,levelLoadingMaxRetry:4,levelLoadingRetryDelay:1e3,levelLoadingMaxRetryTimeout:64e3,fragLoadingTimeOut:2e4,fragLoadingMaxRetry:6,fragLoadingRetryDelay:1e3,fragLoadingMaxRetryTimeout:64e3},Nh()),{},{subtitleStreamController:uf,subtitleTrackController:df,timelineController:Uf,audioStreamController:of,audioTrackController:lf,emeController:jt,cmcdController:Ah,contentSteeringController:Lh});function Nh(){return{cueHandler:Oh,enableWebVTT:!0,enableIMSC1:!0,enableCEA708Captions:!0,captionsTextTrack1Label:"English",captionsTextTrack1LanguageCode:"en",captionsTextTrack2Label:"Spanish",captionsTextTrack2LanguageCode:"es",captionsTextTrack3Label:"Unknown CC",captionsTextTrack3LanguageCode:"",captionsTextTrack4Label:"Unknown CC",captionsTextTrack4LanguageCode:"",renderTextTracksNatively:!0}}function Bh(s,e){if((e.liveSyncDurationCount||e.liveMaxLatencyDurationCount)&&(e.liveSyncDuration||e.liveMaxLatencyDuration))throw new Error("Illegal hls.js config: don't mix up liveSyncDurationCount/liveMaxLatencyDurationCount and liveSyncDuration/liveMaxLatencyDuration");if(e.liveMaxLatencyDurationCount!==void 0&&(e.liveSyncDurationCount===void 0||e.liveMaxLatencyDurationCount<=e.liveSyncDurationCount))throw new Error('Illegal hls.js config: "liveMaxLatencyDurationCount" must be greater than "liveSyncDurationCount"');if(e.liveMaxLatencyDuration!==void 0&&(e.liveSyncDuration===void 0||e.liveMaxLatencyDuration<=e.liveSyncDuration))throw new Error('Illegal hls.js config: "liveMaxLatencyDuration" must be greater than "liveSyncDuration"');const t=En(s),i=["manifest","level","frag"],n=["TimeOut","MaxRetry","RetryDelay","MaxRetryTimeout"];return i.forEach(r=>{const a=`${r==="level"?"playlist":r}LoadPolicy`,o=e[a]===void 0,l=[];n.forEach(u=>{const c=`${r}Loading${u}`,d=e[c];if(d!==void 0&&o){l.push(c);const f=t[a].default;switch(e[a]={default:f},u){case"TimeOut":f.maxLoadTimeMs=d,f.maxTimeToFirstByteMs=d;break;case"MaxRetry":f.errorRetry.maxNumRetry=d,f.timeoutRetry.maxNumRetry=d;break;case"RetryDelay":f.errorRetry.retryDelayMs=d,f.timeoutRetry.retryDelayMs=d;break;case"MaxRetryTimeout":f.errorRetry.maxRetryDelayMs=d,f.timeoutRetry.maxRetryDelayMs=d;break}}}),l.length&&_.warn(`hls.js config: "${l.join('", "')}" setting(s) are deprecated, use "${a}": ${JSON.stringify(e[a])}`)}),Re(Re({},t),e)}function En(s){return s&&typeof s=="object"?Array.isArray(s)?s.map(En):Object.keys(s).reduce((e,t)=>(e[t]=En(s[t]),e),{}):s}function Uh(s){const e=s.loader;e!==fa&&e!==al?(_.log("[config]: Custom loader detected, cannot enable progressive streaming"),s.progressive=!1):Ih()&&(s.loader=fa,s.progressive=!0,s.enableSoftwareAES=!0,_.log("[config]: Progressive streaming enabled, using FetchLoader"))}let js;class $h extends _n{constructor(e,t){super(e,"[level-controller]"),this._levels=[],this._firstLevel=-1,this._maxAutoLevel=-1,this._startLevel=void 0,this.currentLevel=null,this.currentLevelIndex=-1,this.manualLevelIndex=-1,this.steering=void 0,this.onParsedComplete=void 0,this.steering=t,this._registerListeners()}_registerListeners(){const{hls:e}=this;e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.MANIFEST_LOADED,this.onManifestLoaded,this),e.on(v.LEVEL_LOADED,this.onLevelLoaded,this),e.on(v.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(v.FRAG_BUFFERED,this.onFragBuffered,this),e.on(v.ERROR,this.onError,this)}_unregisterListeners(){const{hls:e}=this;e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.MANIFEST_LOADED,this.onManifestLoaded,this),e.off(v.LEVEL_LOADED,this.onLevelLoaded,this),e.off(v.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(v.FRAG_BUFFERED,this.onFragBuffered,this),e.off(v.ERROR,this.onError,this)}destroy(){this._unregisterListeners(),this.steering=null,this.resetLevels(),super.destroy()}stopLoad(){this._levels.forEach(t=>{t.loadError=0,t.fragmentError=0}),super.stopLoad()}resetLevels(){this._startLevel=void 0,this.manualLevelIndex=-1,this.currentLevelIndex=-1,this.currentLevel=null,this._levels=[],this._maxAutoLevel=-1}onManifestLoading(e,t){this.resetLevels()}onManifestLoaded(e,t){const i=this.hls.config.preferManagedMediaSource,n=[],r={},a={};let o=!1,l=!1,u=!1;t.levels.forEach(c=>{var d,f;const h=c.attrs;let{audioCodec:g,videoCodec:p}=c;((d=g)==null?void 0:d.indexOf("mp4a.40.34"))!==-1&&(js||(js=/chrome|firefox/i.test(navigator.userAgent)),js&&(c.audioCodec=g=void 0)),g&&(c.audioCodec=g=ss(g,i)),((f=p)==null?void 0:f.indexOf("avc1"))===0&&(p=c.videoCodec=Lc(p));const{width:m,height:y,unknownCodecs:S}=c;if(o||(o=!!(m&&y)),l||(l=!!p),u||(u=!!g),S!=null&&S.length||g&&!Fs(g,"audio",i)||p&&!Fs(p,"video",i))return;const{CODECS:T,"FRAME-RATE":I,"HDCP-LEVEL":E,"PATHWAY-ID":P,RESOLUTION:R,"VIDEO-RANGE":w}=h,x=`${`${P||"."}-`}${c.bitrate}-${R}-${I}-${T}-${w}-${E}`;if(r[x])if(r[x].uri!==c.url&&!c.attrs["PATHWAY-ID"]){const D=a[x]+=1;c.attrs["PATHWAY-ID"]=new Array(D+1).join(".");const C=new Jt(c);r[x]=C,n.push(C)}else r[x].addGroupId("audio",h.AUDIO),r[x].addGroupId("text",h.SUBTITLES);else{const D=new Jt(c);r[x]=D,a[x]=1,n.push(D)}}),this.filterAndSortMediaOptions(n,t,o,l,u)}filterAndSortMediaOptions(e,t,i,n,r){let a=[],o=[],l=e;if((i||n)&&r&&(l=l.filter(({videoCodec:g,videoRange:p,width:m,height:y})=>(!!g||!!(m&&y))&&Bc(p))),l.length===0){Promise.resolve().then(()=>{if(this.hls){t.levels.length&&this.warn(`One or more CODECS in variant not supported: ${JSON.stringify(t.levels[0].attrs)}`);const g=new Error("no level with compatible codecs found in manifest");this.hls.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.MANIFEST_INCOMPATIBLE_CODECS_ERROR,fatal:!0,url:t.url,error:g,reason:g.message})}});return}if(t.audioTracks){const{preferManagedMediaSource:g}=this.hls.config;a=t.audioTracks.filter(p=>!p.audioCodec||Fs(p.audioCodec,"audio",g)),ha(a)}t.subtitles&&(o=t.subtitles,ha(o));const u=l.slice(0);l.sort((g,p)=>{if(g.attrs["HDCP-LEVEL"]!==p.attrs["HDCP-LEVEL"])return(g.attrs["HDCP-LEVEL"]||"")>(p.attrs["HDCP-LEVEL"]||"")?1:-1;if(i&&g.height!==p.height)return g.height-p.height;if(g.frameRate!==p.frameRate)return g.frameRate-p.frameRate;if(g.videoRange!==p.videoRange)return ns.indexOf(g.videoRange)-ns.indexOf(p.videoRange);if(g.videoCodec!==p.videoCodec){const m=Tr(g.videoCodec),y=Tr(p.videoCodec);if(m!==y)return y-m}if(g.uri===p.uri&&g.codecSet!==p.codecSet){const m=is(g.codecSet),y=is(p.codecSet);if(m!==y)return y-m}return g.averageBitrate!==p.averageBitrate?g.averageBitrate-p.averageBitrate:0});let c=u[0];if(this.steering&&(l=this.steering.filterParsedLevels(l),l.length!==u.length)){for(let g=0;g<u.length;g++)if(u[g].pathwayId===l[0].pathwayId){c=u[g];break}}this._levels=l;for(let g=0;g<l.length;g++)if(l[g]===c){var d;this._firstLevel=g;const p=c.bitrate,m=this.hls.bandwidthEstimate;if(this.log(`manifest loaded, ${l.length} level(s) found, first bitrate: ${p}`),((d=this.hls.userConfig)==null?void 0:d.abrEwmaDefaultEstimate)===void 0){const y=Math.min(p,this.hls.config.abrEwmaDefaultEstimateMax);y>m&&m===ol.abrEwmaDefaultEstimate&&(this.hls.bandwidthEstimate=y)}break}const f=r&&!n,h={levels:l,audioTracks:a,subtitleTracks:o,sessionData:t.sessionData,sessionKeys:t.sessionKeys,firstLevel:this._firstLevel,stats:t.stats,audio:r,video:n,altAudio:!f&&a.some(g=>!!g.url)};this.hls.trigger(v.MANIFEST_PARSED,h),(this.hls.config.autoStartLoad||this.hls.forceStartLoad)&&this.hls.startLoad(this.hls.config.startPosition)}get levels(){return this._levels.length===0?null:this._levels}get level(){return this.currentLevelIndex}set level(e){const t=this._levels;if(t.length===0)return;if(e<0||e>=t.length){const c=new Error("invalid level idx"),d=e<0;if(this.hls.trigger(v.ERROR,{type:re.OTHER_ERROR,details:O.LEVEL_SWITCH_ERROR,level:e,fatal:d,error:c,reason:c.message}),d)return;e=Math.min(e,t.length-1)}const i=this.currentLevelIndex,n=this.currentLevel,r=n?n.attrs["PATHWAY-ID"]:void 0,a=t[e],o=a.attrs["PATHWAY-ID"];if(this.currentLevelIndex=e,this.currentLevel=a,i===e&&a.details&&n&&r===o)return;this.log(`Switching to level ${e} (${a.height?a.height+"p ":""}${a.videoRange?a.videoRange+" ":""}${a.codecSet?a.codecSet+" ":""}@${a.bitrate})${o?" with Pathway "+o:""} from level ${i}${r?" with Pathway "+r:""}`);const l={level:e,attrs:a.attrs,details:a.details,bitrate:a.bitrate,averageBitrate:a.averageBitrate,maxBitrate:a.maxBitrate,realBitrate:a.realBitrate,width:a.width,height:a.height,codecSet:a.codecSet,audioCodec:a.audioCodec,videoCodec:a.videoCodec,audioGroups:a.audioGroups,subtitleGroups:a.subtitleGroups,loaded:a.loaded,loadError:a.loadError,fragmentError:a.fragmentError,name:a.name,id:a.id,uri:a.uri,url:a.url,urlId:0,audioGroupIds:a.audioGroupIds,textGroupIds:a.textGroupIds};this.hls.trigger(v.LEVEL_SWITCHING,l);const u=a.details;if(!u||u.live){const c=this.switchParams(a.uri,n?.details,u);this.loadPlaylist(c)}}get manualLevel(){return this.manualLevelIndex}set manualLevel(e){this.manualLevelIndex=e,this._startLevel===void 0&&(this._startLevel=e),e!==-1&&(this.level=e)}get firstLevel(){return this._firstLevel}set firstLevel(e){this._firstLevel=e}get startLevel(){if(this._startLevel===void 0){const e=this.hls.config.startLevel;return e!==void 0?e:this.hls.firstAutoLevel}return this._startLevel}set startLevel(e){this._startLevel=e}onError(e,t){t.fatal||!t.context||t.context.type===ce.LEVEL&&t.context.level===this.level&&this.checkRetry(t)}onFragBuffered(e,{frag:t}){if(t!==void 0&&t.type===ne.MAIN){const i=t.elementaryStreams;if(!Object.keys(i).some(r=>!!i[r]))return;const n=this._levels[t.level];n!=null&&n.loadError&&(this.log(`Resetting level error count of ${n.loadError} on frag buffered`),n.loadError=0)}}onLevelLoaded(e,t){var i;const{level:n,details:r}=t,a=this._levels[n];if(!a){var o;this.warn(`Invalid level index ${n}`),(o=t.deliveryDirectives)!=null&&o.skip&&(r.deltaUpdateFailed=!0);return}n===this.currentLevelIndex?(a.fragmentError===0&&(a.loadError=0),this.playlistLoaded(n,t,a.details)):(i=t.deliveryDirectives)!=null&&i.skip&&(r.deltaUpdateFailed=!0)}loadPlaylist(e){super.loadPlaylist();const t=this.currentLevelIndex,i=this.currentLevel;if(i&&this.shouldLoadPlaylist(i)){let n=i.uri;if(e)try{n=e.addDirectives(n)}catch(a){this.warn(`Could not construct new URL with HLS Delivery Directives: ${a}`)}const r=i.attrs["PATHWAY-ID"];this.log(`Loading level index ${t}${e?.msn!==void 0?" at sn "+e.msn+" part "+e.part:""} with${r?" Pathway "+r:""} ${n}`),this.clearTimer(),this.hls.trigger(v.LEVEL_LOADING,{url:n,level:t,pathwayId:i.attrs["PATHWAY-ID"],id:0,deliveryDirectives:e||null})}}get nextLoadLevel(){return this.manualLevelIndex!==-1?this.manualLevelIndex:this.hls.nextAutoLevel}set nextLoadLevel(e){this.level=e,this.manualLevelIndex===-1&&(this.hls.nextAutoLevel=e)}removeLevel(e){var t;const i=this._levels.filter((n,r)=>r!==e?!0:(this.steering&&this.steering.removeLevel(n),n===this.currentLevel&&(this.currentLevel=null,this.currentLevelIndex=-1,n.details&&n.details.fragments.forEach(a=>a.level=-1)),!1));Ao(i),this._levels=i,this.currentLevelIndex>-1&&(t=this.currentLevel)!=null&&t.details&&(this.currentLevelIndex=this.currentLevel.details.fragments[0].level),this.hls.trigger(v.LEVELS_UPDATED,{levels:i})}onLevelsUpdated(e,{levels:t}){this._levels=t}checkMaxAutoUpdated(){const{autoLevelCapping:e,maxAutoLevel:t,maxHdcpLevel:i}=this.hls;this._maxAutoLevel!==t&&(this._maxAutoLevel=t,this.hls.trigger(v.MAX_AUTO_LEVEL_UPDATED,{autoLevelCapping:e,levels:this.levels,maxAutoLevel:t,minAutoLevel:this.hls.minAutoLevel,maxHdcpLevel:i}))}}function ha(s){const e={};s.forEach(t=>{const i=t.groupId||"";t.id=e[i]=e[i]||0,e[i]++})}class Gh{constructor(e){this.config=void 0,this.keyUriToKeyInfo={},this.emeController=null,this.config=e}abort(e){for(const i in this.keyUriToKeyInfo){const n=this.keyUriToKeyInfo[i].loader;if(n){var t;if(e&&e!==((t=n.context)==null?void 0:t.frag.type))return;n.abort()}}}detach(){for(const e in this.keyUriToKeyInfo){const t=this.keyUriToKeyInfo[e];(t.mediaKeySessionContext||t.decryptdata.isCommonEncryption)&&delete this.keyUriToKeyInfo[e]}}destroy(){this.detach();for(const e in this.keyUriToKeyInfo){const t=this.keyUriToKeyInfo[e].loader;t&&t.destroy()}this.keyUriToKeyInfo={}}createKeyLoadError(e,t=O.KEY_LOAD_ERROR,i,n,r){return new ut({type:re.NETWORK_ERROR,details:t,fatal:!1,frag:e,response:r,error:i,networkDetails:n})}loadClear(e,t){if(this.emeController&&this.config.emeEnabled){const{sn:i,cc:n}=e;for(let r=0;r<t.length;r++){const a=t[r];if(n<=a.cc&&(i==="initSegment"||a.sn==="initSegment"||i<a.sn)){this.emeController.selectKeySystemFormat(a).then(o=>{a.setKeyFormat(o)});break}}}}load(e){return!e.decryptdata&&e.encrypted&&this.emeController&&this.config.emeEnabled?this.emeController.selectKeySystemFormat(e).then(t=>this.loadInternal(e,t)):this.loadInternal(e)}loadInternal(e,t){var i,n;t&&e.setKeyFormat(t);const r=e.decryptdata;if(!r){const u=new Error(t?`Expected frag.decryptdata to be defined after setting format ${t}`:"Missing decryption data on fragment in onKeyLoading");return Promise.reject(this.createKeyLoadError(e,O.KEY_LOAD_ERROR,u))}const a=r.uri;if(!a)return Promise.reject(this.createKeyLoadError(e,O.KEY_LOAD_ERROR,new Error(`Invalid key URI: "${a}"`)));let o=this.keyUriToKeyInfo[a];if((i=o)!=null&&i.decryptdata.key)return r.key=o.decryptdata.key,Promise.resolve({frag:e,keyInfo:o});if((n=o)!=null&&n.keyLoadPromise){var l;switch((l=o.mediaKeySessionContext)==null?void 0:l.keyStatus){case void 0:case"status-pending":case"usable":case"usable-in-future":return o.keyLoadPromise.then(u=>(r.key=u.keyInfo.decryptdata.key,{frag:e,keyInfo:o}))}}switch(o=this.keyUriToKeyInfo[a]={decryptdata:r,keyLoadPromise:null,loader:null,mediaKeySessionContext:null},r.method){case"ISO-23001-7":case"SAMPLE-AES":case"SAMPLE-AES-CENC":case"SAMPLE-AES-CTR":return r.keyFormat==="identity"?this.loadKeyHTTP(o,e):this.loadKeyEME(o,e);case"AES-128":return this.loadKeyHTTP(o,e);default:return Promise.reject(this.createKeyLoadError(e,O.KEY_LOAD_ERROR,new Error(`Key supplied with unsupported METHOD: "${r.method}"`)))}}loadKeyEME(e,t){const i={frag:t,keyInfo:e};if(this.emeController&&this.config.emeEnabled){const n=this.emeController.loadKey(i);if(n)return(e.keyLoadPromise=n.then(r=>(e.mediaKeySessionContext=r,i))).catch(r=>{throw e.keyLoadPromise=null,r})}return Promise.resolve(i)}loadKeyHTTP(e,t){const i=this.config,n=i.loader,r=new n(i);return t.keyLoader=e.loader=r,e.keyLoadPromise=new Promise((a,o)=>{const l={keyInfo:e,frag:t,responseType:"arraybuffer",url:e.decryptdata.uri},u=i.keyLoadPolicy.default,c={loadPolicy:u,timeout:u.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0},d={onSuccess:(f,h,g,p)=>{const{frag:m,keyInfo:y,url:S}=g;if(!m.decryptdata||y!==this.keyUriToKeyInfo[S])return o(this.createKeyLoadError(m,O.KEY_LOAD_ERROR,new Error("after key load, decryptdata unset or changed"),p));y.decryptdata.key=m.decryptdata.key=new Uint8Array(f.data),m.keyLoader=null,y.loader=null,a({frag:m,keyInfo:y})},onError:(f,h,g,p)=>{this.resetLoader(h),o(this.createKeyLoadError(t,O.KEY_LOAD_ERROR,new Error(`HTTP Error ${f.code} loading key ${f.text}`),g,Re({url:l.url,data:void 0},f)))},onTimeout:(f,h,g)=>{this.resetLoader(h),o(this.createKeyLoadError(t,O.KEY_LOAD_TIMEOUT,new Error("key loading timed out"),g))},onAbort:(f,h,g)=>{this.resetLoader(h),o(this.createKeyLoadError(t,O.INTERNAL_ABORTED,new Error("key loading aborted"),g))}};r.load(l,c,d)})}resetLoader(e){const{frag:t,keyInfo:i,url:n}=e,r=i.loader;t.keyLoader===r&&(t.keyLoader=null,i.loader=null),delete this.keyUriToKeyInfo[n],r&&r.destroy()}}function ll(){return self.SourceBuffer||self.WebKitSourceBuffer}function ul(){if(!_t())return!1;const e=ll();return!e||e.prototype&&typeof e.prototype.appendBuffer=="function"&&typeof e.prototype.remove=="function"}function Vh(){if(!ul())return!1;const s=_t();return typeof s?.isTypeSupported=="function"&&(["avc1.42E01E,mp4a.40.2","av01.0.01M.08","vp09.00.50.08"].some(e=>s.isTypeSupported(gi(e,"video")))||["mp4a.40.2","fLaC"].some(e=>s.isTypeSupported(gi(e,"audio"))))}function Kh(){var s;const e=ll();return typeof(e==null||(s=e.prototype)==null?void 0:s.changeType)=="function"}const Hh=250,Zi=2,qh=.1,Wh=.05;class Yh{constructor(e,t,i,n){this.config=void 0,this.media=null,this.fragmentTracker=void 0,this.hls=void 0,this.nudgeRetry=0,this.stallReported=!1,this.stalled=null,this.moved=!1,this.seeking=!1,this.config=e,this.media=t,this.fragmentTracker=i,this.hls=n}destroy(){this.media=null,this.hls=this.fragmentTracker=null}poll(e,t){const{config:i,media:n,stalled:r}=this;if(n===null)return;const{currentTime:a,seeking:o}=n,l=this.seeking&&!o,u=!this.seeking&&o;if(this.seeking=o,a!==e){if(this.moved=!0,o||(this.nudgeRetry=0),r!==null){if(this.stallReported){const m=self.performance.now()-r;_.warn(`playback not stuck anymore @${a}, after ${Math.round(m)}ms`),this.stallReported=!1}this.stalled=null}return}if(u||l){this.stalled=null;return}if(n.paused&&!o||n.ended||n.playbackRate===0||!me.getBuffered(n).length){this.nudgeRetry=0;return}const c=me.bufferInfo(n,a,0),d=c.nextStart||0;if(o){const m=c.len>Zi,y=!d||t&&t.start<=a||d-a>Zi&&!this.fragmentTracker.getPartialFragment(a);if(m||y)return;this.moved=!1}if(!this.moved&&this.stalled!==null){var f;if(!(c.len>0)&&!d)return;const y=Math.max(d,c.start||0)-a,S=this.hls.levels?this.hls.levels[this.hls.currentLevel]:null,I=(S==null||(f=S.details)==null?void 0:f.live)?S.details.targetduration*2:Zi,E=this.fragmentTracker.getPartialFragment(a);if(y>0&&(y<=I||E)){n.paused||this._trySkipBufferHole(E);return}}const h=self.performance.now();if(r===null){this.stalled=h;return}const g=h-r;if(!o&&g>=Hh&&(this._reportStall(c),!this.media))return;const p=me.bufferInfo(n,a,i.maxBufferHole);this._tryFixBufferStall(p,g)}_tryFixBufferStall(e,t){const{config:i,fragmentTracker:n,media:r}=this;if(r===null)return;const a=r.currentTime,o=n.getPartialFragment(a);o&&(this._trySkipBufferHole(o)||!this.media)||(e.len>i.maxBufferHole||e.nextStart&&e.nextStart-a<i.maxBufferHole)&&t>i.highBufferWatchdogPeriod*1e3&&(_.warn("Trying to nudge playhead over buffer-hole"),this.stalled=null,this._tryNudgeBuffer())}_reportStall(e){const{hls:t,media:i,stallReported:n}=this;if(!n&&i){this.stallReported=!0;const r=new Error(`Playback stalling at @${i.currentTime} due to low buffer (${JSON.stringify(e)})`);_.warn(r.message),t.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.BUFFER_STALLED_ERROR,fatal:!1,error:r,buffer:e.len})}}_trySkipBufferHole(e){const{config:t,hls:i,media:n}=this;if(n===null)return 0;const r=n.currentTime,a=me.bufferInfo(n,r,0),o=r<a.start?a.start:a.nextStart;if(o){const l=a.len<=t.maxBufferHole,u=a.len>0&&a.len<1&&n.readyState<3,c=o-r;if(c>0&&(l||u)){if(c>t.maxBufferHole){const{fragmentTracker:f}=this;let h=!1;if(r===0){const g=f.getAppendedFrag(0,ne.MAIN);g&&o<g.end&&(h=!0)}if(!h){const g=e||f.getAppendedFrag(r,ne.MAIN);if(g){let p=!1,m=g.end;for(;m<o;){const y=f.getPartialFragment(m);if(y)m+=y.duration;else{p=!0;break}}if(p)return 0}}}const d=Math.max(o+Wh,r+qh);if(_.warn(`skipping hole, adjusting currentTime from ${r} to ${d}`),this.moved=!0,this.stalled=null,n.currentTime=d,e&&!e.gap){const f=new Error(`fragment loaded with buffer holes, seeking from ${r} to ${d}`);i.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.BUFFER_SEEK_OVER_HOLE,fatal:!1,error:f,reason:f.message,frag:e})}return d}}return 0}_tryNudgeBuffer(){const{config:e,hls:t,media:i,nudgeRetry:n}=this;if(i===null)return;const r=i.currentTime;if(this.nudgeRetry++,n<e.nudgeMaxRetry){const a=r+(n+1)*e.nudgeOffset,o=new Error(`Nudging 'currentTime' from ${r} to ${a}`);_.warn(o.message),i.currentTime=a,t.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.BUFFER_NUDGE_ON_STALL,error:o,fatal:!1})}else{const a=new Error(`Playhead still not moving while enough data buffered @${r} after ${e.nudgeMaxRetry} nudges`);_.error(a.message),t.trigger(v.ERROR,{type:re.MEDIA_ERROR,details:O.BUFFER_STALLED_ERROR,error:a,fatal:!0})}}}const zh=100;class jh extends On{constructor(e,t,i){super(e,t,i,"[stream-controller]",ne.MAIN),this.audioCodecSwap=!1,this.gapController=null,this.level=-1,this._forceStartLoad=!1,this.altAudio=!1,this.audioOnly=!1,this.fragPlaying=null,this.onvplaying=null,this.onvseeked=null,this.fragLastKbps=0,this.couldBacktrack=!1,this.backtrackFragment=null,this.audioCodecSwitch=!1,this.videoBuffer=null,this._registerListeners()}_registerListeners(){const{hls:e}=this;e.on(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(v.MANIFEST_LOADING,this.onManifestLoading,this),e.on(v.MANIFEST_PARSED,this.onManifestParsed,this),e.on(v.LEVEL_LOADING,this.onLevelLoading,this),e.on(v.LEVEL_LOADED,this.onLevelLoaded,this),e.on(v.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),e.on(v.ERROR,this.onError,this),e.on(v.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),e.on(v.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),e.on(v.BUFFER_CREATED,this.onBufferCreated,this),e.on(v.BUFFER_FLUSHED,this.onBufferFlushed,this),e.on(v.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(v.FRAG_BUFFERED,this.onFragBuffered,this)}_unregisterListeners(){const{hls:e}=this;e.off(v.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(v.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(v.MANIFEST_LOADING,this.onManifestLoading,this),e.off(v.MANIFEST_PARSED,this.onManifestParsed,this),e.off(v.LEVEL_LOADED,this.onLevelLoaded,this),e.off(v.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),e.off(v.ERROR,this.onError,this),e.off(v.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),e.off(v.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),e.off(v.BUFFER_CREATED,this.onBufferCreated,this),e.off(v.BUFFER_FLUSHED,this.onBufferFlushed,this),e.off(v.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(v.FRAG_BUFFERED,this.onFragBuffered,this)}onHandlerDestroying(){this._unregisterListeners(),super.onHandlerDestroying()}startLoad(e){if(this.levels){const{lastCurrentTime:t,hls:i}=this;if(this.stopLoad(),this.setInterval(zh),this.level=-1,!this.startFragRequested){let n=i.startLevel;n===-1&&(i.config.testBandwidth&&this.levels.length>1?(n=0,this.bitrateTest=!0):n=i.firstAutoLevel),i.nextLoadLevel=n,this.level=i.loadLevel,this.loadedmetadata=!1}t>0&&e===-1&&(this.log(`Override startPosition with lastCurrentTime @${t.toFixed(3)}`),e=t),this.state=G.IDLE,this.nextLoadPosition=this.startPosition=this.lastCurrentTime=e,this.tick()}else this._forceStartLoad=!0,this.state=G.STOPPED}stopLoad(){this._forceStartLoad=!1,super.stopLoad()}doTick(){switch(this.state){case G.WAITING_LEVEL:{const{levels:t,level:i}=this,n=t?.[i],r=n?.details;if(r&&(!r.live||this.levelLastLoaded===n)){if(this.waitForCdnTuneIn(r))break;this.state=G.IDLE;break}else if(this.hls.nextLoadLevel!==this.level){this.state=G.IDLE;break}break}case G.FRAG_LOADING_WAITING_RETRY:{var e;const t=self.performance.now(),i=this.retryDate;if(!i||t>=i||(e=this.media)!=null&&e.seeking){const{levels:n,level:r}=this,a=n?.[r];this.resetStartWhenNotLoaded(a||null),this.state=G.IDLE}}break}this.state===G.IDLE&&this.doTickIdle(),this.onTickEnd()}onTickEnd(){super.onTickEnd(),this.checkBuffer(),this.checkFragmentChanged()}doTickIdle(){const{hls:e,levelLastLoaded:t,levels:i,media:n}=this;if(t===null||!n&&(this.startFragRequested||!e.config.startFragPrefetch)||this.altAudio&&this.audioOnly)return;const r=this.buffering?e.nextLoadLevel:e.loadLevel;if(!(i!=null&&i[r]))return;const a=i[r],o=this.getMainFwdBufferInfo();if(o===null)return;const l=this.getLevelDetails();if(l&&this._streamEnded(o,l)){const p={};this.altAudio&&(p.type="video"),this.hls.trigger(v.BUFFER_EOS,p),this.state=G.ENDED;return}if(!this.buffering)return;e.loadLevel!==r&&e.manualLevel===-1&&this.log(`Adapting to level ${r} from level ${this.level}`),this.level=e.nextLoadLevel=r;const u=a.details;if(!u||this.state===G.WAITING_LEVEL||u.live&&this.levelLastLoaded!==a){this.level=r,this.state=G.WAITING_LEVEL;return}const c=o.len,d=this.getMaxBufferLength(a.maxBitrate);if(c>=d)return;this.backtrackFragment&&this.backtrackFragment.start>o.end&&(this.backtrackFragment=null);const f=this.backtrackFragment?this.backtrackFragment.start:o.end;let h=this.getNextFragment(f,u);if(this.couldBacktrack&&!this.fragPrevious&&h&&h.sn!=="initSegment"&&this.fragmentTracker.getState(h)!==Le.OK){var g;const m=((g=this.backtrackFragment)!=null?g:h).sn-u.startSN,y=u.fragments[m-1];y&&h.cc===y.cc&&(h=y,this.fragmentTracker.removeFragment(y))}else this.backtrackFragment&&o.len&&(this.backtrackFragment=null);if(h&&this.isLoopLoading(h,f)){if(!h.gap){const m=this.audioOnly&&!this.altAudio?fe.AUDIO:fe.VIDEO,y=(m===fe.VIDEO?this.videoBuffer:this.mediaBuffer)||this.media;y&&this.afterBufferFlushed(y,m,ne.MAIN)}h=this.getNextFragmentLoopLoading(h,u,o,ne.MAIN,d)}h&&(h.initSegment&&!h.initSegment.data&&!this.bitrateTest&&(h=h.initSegment),this.loadFragment(h,a,f))}loadFragment(e,t,i){const n=this.fragmentTracker.getState(e);this.fragCurrent=e,n===Le.NOT_LOADED||n===Le.PARTIAL?e.sn==="initSegment"?this._loadInitSegment(e,t):this.bitrateTest?(this.log(`Fragment ${e.sn} of level ${e.level} is being downloaded to test bitrate and will not be buffered`),this._loadBitrateTestFrag(e,t)):(this.startFragRequested=!0,super.loadFragment(e,t,i)):this.clearTrackerIfNeeded(e)}getBufferedFrag(e){return this.fragmentTracker.getBufferedFrag(e,ne.MAIN)}followingBufferedFrag(e){return e?this.getBufferedFrag(e.end+.5):null}immediateLevelSwitch(){this.abortCurrentFrag(),this.flushMainBuffer(0,Number.POSITIVE_INFINITY)}nextLevelSwitch(){const{levels:e,media:t}=this;if(t!=null&&t.readyState){let i;const n=this.getAppendedFrag(t.currentTime);n&&n.start>1&&this.flushMainBuffer(0,n.start-1);const r=this.getLevelDetails();if(r!=null&&r.live){const o=this.getMainFwdBufferInfo();if(!o||o.len<r.targetduration*2)return}if(!t.paused&&e){const o=this.hls.nextLoadLevel,l=e[o],u=this.fragLastKbps;u&&this.fragCurrent?i=this.fragCurrent.duration*l.maxBitrate/(1e3*u)+1:i=0}else i=0;const a=this.getBufferedFrag(t.currentTime+i);if(a){const o=this.followingBufferedFrag(a);if(o){this.abortCurrentFrag();const l=o.maxStartPTS?o.maxStartPTS:o.start,u=o.duration,c=Math.max(a.end,l+Math.min(Math.max(u-this.config.maxFragLookUpTolerance,u*(this.couldBacktrack?.5:.125)),u*(this.couldBacktrack?.75:.25)));this.flushMainBuffer(c,Number.POSITIVE_INFINITY)}}}}abortCurrentFrag(){const e=this.fragCurrent;switch(this.fragCurrent=null,this.backtrackFragment=null,e&&(e.abortRequests(),this.fragmentTracker.removeFragment(e)),this.state){case G.KEY_LOADING:case G.FRAG_LOADING:case G.FRAG_LOADING_WAITING_RETRY:case G.PARSING:case G.PARSED:this.state=G.IDLE;break}this.nextLoadPosition=this.getLoadPosition()}flushMainBuffer(e,t){super.flushMainBuffer(e,t,this.altAudio?"video":null)}onMediaAttached(e,t){super.onMediaAttached(e,t);const i=t.media;this.onvplaying=this.onMediaPlaying.bind(this),this.onvseeked=this.onMediaSeeked.bind(this),i.addEventListener("playing",this.onvplaying),i.addEventListener("seeked",this.onvseeked),this.gapController=new Yh(this.config,i,this.fragmentTracker,this.hls)}onMediaDetaching(){const{media:e}=this;e&&this.onvplaying&&this.onvseeked&&(e.removeEventListener("playing",this.onvplaying),e.removeEventListener("seeked",this.onvseeked),this.onvplaying=this.onvseeked=null,this.videoBuffer=null),this.fragPlaying=null,this.gapController&&(this.gapController.destroy(),this.gapController=null),super.onMediaDetaching()}onMediaPlaying(){this.tick()}onMediaSeeked(){const e=this.media,t=e?e.currentTime:null;J(t)&&this.log(`Media seeked to ${t.toFixed(3)}`);const i=this.getMainFwdBufferInfo();if(i===null||i.len===0){this.warn(`Main forward buffer length on "seeked" event ${i?i.len:"empty"})`);return}this.tick()}onManifestLoading(){this.log("Trigger BUFFER_RESET"),this.hls.trigger(v.BUFFER_RESET,void 0),this.fragmentTracker.removeAllFragments(),this.couldBacktrack=!1,this.startPosition=this.lastCurrentTime=this.fragLastKbps=0,this.levels=this.fragPlaying=this.backtrackFragment=this.levelLastLoaded=null,this.altAudio=this.audioOnly=this.startFragRequested=!1}onManifestParsed(e,t){let i=!1,n=!1;t.levels.forEach(r=>{const a=r.audioCodec;a&&(i=i||a.indexOf("mp4a.40.2")!==-1,n=n||a.indexOf("mp4a.40.5")!==-1)}),this.audioCodecSwitch=i&&n&&!Kh(),this.audioCodecSwitch&&this.log("Both AAC/HE-AAC audio found in levels; declaring level codec as HE-AAC"),this.levels=t.levels,this.startFragRequested=!1}onLevelLoading(e,t){const{levels:i}=this;if(!i||this.state!==G.IDLE)return;const n=i[t.level];(!n.details||n.details.live&&this.levelLastLoaded!==n||this.waitForCdnTuneIn(n.details))&&(this.state=G.WAITING_LEVEL)}onLevelLoaded(e,t){var i;const{levels:n}=this,r=t.level,a=t.details,o=a.totalduration;if(!n){this.warn(`Levels were reset while loading level ${r}`);return}this.log(`Level ${r} loaded [${a.startSN},${a.endSN}]${a.lastPartSn?`[part-${a.lastPartSn}-${a.lastPartIndex}]`:""}, cc [${a.startCC}, ${a.endCC}] duration:${o}`);const l=n[r],u=this.fragCurrent;u&&(this.state===G.FRAG_LOADING||this.state===G.FRAG_LOADING_WAITING_RETRY)&&u.level!==t.level&&u.loader&&this.abortCurrentFrag();let c=0;if(a.live||(i=l.details)!=null&&i.live){var d;if(this.checkLiveUpdate(a),a.deltaUpdateFailed)return;c=this.alignPlaylists(a,l.details,(d=this.levelLastLoaded)==null?void 0:d.details)}if(l.details=a,this.levelLastLoaded=l,this.hls.trigger(v.LEVEL_UPDATED,{details:a,level:r}),this.state===G.WAITING_LEVEL){if(this.waitForCdnTuneIn(a))return;this.state=G.IDLE}this.startFragRequested?a.live&&this.synchronizeToLiveEdge(a):this.setStartPosition(a,c),this.tick()}_handleFragmentLoadProgress(e){var t;const{frag:i,part:n,payload:r}=e,{levels:a}=this;if(!a){this.warn(`Levels were reset while fragment load was in progress. Fragment ${i.sn} of level ${i.level} will not be buffered`);return}const o=a[i.level],l=o.details;if(!l){this.warn(`Dropping fragment ${i.sn} of level ${i.level} after level details were reset`),this.fragmentTracker.removeFragment(i);return}const u=o.videoCodec,c=l.PTSKnown||!l.live,d=(t=i.initSegment)==null?void 0:t.data,f=this._getAudioCodec(o),h=this.transmuxer=this.transmuxer||new Ko(this.hls,ne.MAIN,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)),g=n?n.index:-1,p=g!==-1,m=new Pn(i.level,i.sn,i.stats.chunkCount,r.byteLength,g,p),y=this.initPTS[i.cc];h.push(r,d,f,u,i,n,l.totalduration,c,m,y)}onAudioTrackSwitching(e,t){const i=this.altAudio;if(!!!t.url){if(this.mediaBuffer!==this.media){this.log("Switching on main audio, use media.buffered to schedule main fragment loading"),this.mediaBuffer=this.media;const a=this.fragCurrent;a&&(this.log("Switching to main audio track, cancel main fragment load"),a.abortRequests(),this.fragmentTracker.removeFragment(a)),this.resetTransmuxer(),this.resetLoadingState()}else this.audioOnly&&this.resetTransmuxer();const r=this.hls;i&&(r.trigger(v.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:null}),this.fragmentTracker.removeAllFragments()),r.trigger(v.AUDIO_TRACK_SWITCHED,t)}}onAudioTrackSwitched(e,t){const i=t.id,n=!!this.hls.audioTracks[i].url;if(n){const r=this.videoBuffer;r&&this.mediaBuffer!==r&&(this.log("Switching on alternate audio, use video.buffered to schedule main fragment loading"),this.mediaBuffer=r)}this.altAudio=n,this.tick()}onBufferCreated(e,t){const i=t.tracks;let n,r,a=!1;for(const o in i){const l=i[o];if(l.id==="main"){if(r=o,n=l,o==="video"){const u=i[o];u&&(this.videoBuffer=u.buffer)}}else a=!0}a&&n?(this.log(`Alternate track found, use ${r}.buffered to schedule main fragment loading`),this.mediaBuffer=n.buffer):this.mediaBuffer=this.media}onFragBuffered(e,t){const{frag:i,part:n}=t;if(i&&i.type!==ne.MAIN)return;if(this.fragContextChanged(i)){this.warn(`Fragment ${i.sn}${n?" p: "+n.index:""} of level ${i.level} finished buffering, but was aborted. state: ${this.state}`),this.state===G.PARSED&&(this.state=G.IDLE);return}const r=n?n.stats:i.stats;this.fragLastKbps=Math.round(8*r.total/(r.buffering.end-r.loading.first)),i.sn!=="initSegment"&&(this.fragPrevious=i),this.fragBufferedComplete(i,n)}onError(e,t){var i;if(t.fatal){this.state=G.ERROR;return}switch(t.details){case O.FRAG_GAP:case O.FRAG_PARSING_ERROR:case O.FRAG_DECRYPT_ERROR:case O.FRAG_LOAD_ERROR:case O.FRAG_LOAD_TIMEOUT:case O.KEY_LOAD_ERROR:case O.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(ne.MAIN,t);break;case O.LEVEL_LOAD_ERROR:case O.LEVEL_LOAD_TIMEOUT:case O.LEVEL_PARSING_ERROR:!t.levelRetry&&this.state===G.WAITING_LEVEL&&((i=t.context)==null?void 0:i.type)===ce.LEVEL&&(this.state=G.IDLE);break;case O.BUFFER_APPEND_ERROR:case O.BUFFER_FULL_ERROR:if(!t.parent||t.parent!=="main")return;if(t.details===O.BUFFER_APPEND_ERROR){this.resetLoadingState();return}this.reduceLengthAndFlushBuffer(t)&&this.flushMainBuffer(0,Number.POSITIVE_INFINITY);break;case O.INTERNAL_EXCEPTION:this.recoverWorkerError(t);break}}checkBuffer(){const{media:e,gapController:t}=this;if(!(!e||!t||!e.readyState)){if(this.loadedmetadata||!me.getBuffered(e).length){const i=this.state!==G.IDLE?this.fragCurrent:null;t.poll(this.lastCurrentTime,i)}this.lastCurrentTime=e.currentTime}}onFragLoadEmergencyAborted(){this.state=G.IDLE,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.tickImmediate()}onBufferFlushed(e,{type:t}){if(t!==fe.AUDIO||this.audioOnly&&!this.altAudio){const i=(t===fe.VIDEO?this.videoBuffer:this.mediaBuffer)||this.media;this.afterBufferFlushed(i,t,ne.MAIN),this.tick()}}onLevelsUpdated(e,t){this.level>-1&&this.fragCurrent&&(this.level=this.fragCurrent.level),this.levels=t.levels}swapAudioCodec(){this.audioCodecSwap=!this.audioCodecSwap}seekToStartPos(){const{media:e}=this;if(!e)return;const t=e.currentTime;let i=this.startPosition;if(i>=0&&t<i){if(e.seeking){this.log(`could not seek to ${i}, already seeking at ${t}`);return}const n=me.getBuffered(e),a=(n.length?n.start(0):0)-i;a>0&&(a<this.config.maxBufferHole||a<this.config.maxFragLookUpTolerance)&&(this.log(`adjusting start position by ${a} to match buffer start`),i+=a,this.startPosition=i),this.log(`seek to target start position ${i} from current time ${t}`),e.currentTime=i}}_getAudioCodec(e){let t=this.config.defaultAudioCodec||e.audioCodec;return this.audioCodecSwap&&t&&(this.log("Swapping audio codec"),t.indexOf("mp4a.40.5")!==-1?t="mp4a.40.2":t="mp4a.40.5"),t}_loadBitrateTestFrag(e,t){e.bitrateTest=!0,this._doFragLoad(e,t).then(i=>{const{hls:n}=this;if(!i||this.fragContextChanged(e))return;t.fragmentError=0,this.state=G.IDLE,this.startFragRequested=!1,this.bitrateTest=!1;const r=e.stats;r.parsing.start=r.parsing.end=r.buffering.start=r.buffering.end=self.performance.now(),n.trigger(v.FRAG_LOADED,i),e.bitrateTest=!1})}_handleTransmuxComplete(e){var t;const i="main",{hls:n}=this,{remuxResult:r,chunkMeta:a}=e,o=this.getCurrentContext(a);if(!o){this.resetWhenMissingContext(a);return}const{frag:l,part:u,level:c}=o,{video:d,text:f,id3:h,initSegment:g}=r,{details:p}=c,m=this.altAudio?void 0:r.audio;if(this.fragContextChanged(l)){this.fragmentTracker.removeFragment(l);return}if(this.state=G.PARSING,g){if(g!=null&&g.tracks){const T=l.initSegment||l;this._bufferInitSegment(c,g.tracks,T,a),n.trigger(v.FRAG_PARSING_INIT_SEGMENT,{frag:T,id:i,tracks:g.tracks})}const y=g.initPTS,S=g.timescale;J(y)&&(this.initPTS[l.cc]={baseTime:y,timescale:S},n.trigger(v.INIT_PTS_FOUND,{frag:l,id:i,initPTS:y,timescale:S}))}if(d&&p&&l.sn!=="initSegment"){const y=p.fragments[l.sn-1-p.startSN],S=l.sn===p.startSN,T=!y||l.cc>y.cc;if(r.independent!==!1){const{startPTS:I,endPTS:E,startDTS:P,endDTS:R}=d;if(u)u.elementaryStreams[d.type]={startPTS:I,endPTS:E,startDTS:P,endDTS:R};else if(d.firstKeyFrame&&d.independent&&a.id===1&&!T&&(this.couldBacktrack=!0),d.dropped&&d.independent){const w=this.getMainFwdBufferInfo(),b=(w?w.end:this.getLoadPosition())+this.config.maxBufferHole,x=d.firstKeyFramePTS?d.firstKeyFramePTS:I;if(!S&&b<x-this.config.maxBufferHole&&!T){this.backtrack(l);return}else T&&(l.gap=!0);l.setElementaryStreamInfo(d.type,l.start,E,l.start,R,!0)}else S&&I>Zi&&(l.gap=!0);l.setElementaryStreamInfo(d.type,I,E,P,R),this.backtrackFragment&&(this.backtrackFragment=l),this.bufferFragmentData(d,l,u,a,S||T)}else if(S||T)l.gap=!0;else{this.backtrack(l);return}}if(m){const{startPTS:y,endPTS:S,startDTS:T,endDTS:I}=m;u&&(u.elementaryStreams[fe.AUDIO]={startPTS:y,endPTS:S,startDTS:T,endDTS:I}),l.setElementaryStreamInfo(fe.AUDIO,y,S,T,I),this.bufferFragmentData(m,l,u,a)}if(p&&h!=null&&(t=h.samples)!=null&&t.length){const y={id:i,frag:l,details:p,samples:h.samples};n.trigger(v.FRAG_PARSING_METADATA,y)}if(p&&f){const y={id:i,frag:l,details:p,samples:f.samples};n.trigger(v.FRAG_PARSING_USERDATA,y)}}_bufferInitSegment(e,t,i,n){if(this.state!==G.PARSING)return;this.audioOnly=!!t.audio&&!t.video,this.altAudio&&!this.audioOnly&&delete t.audio;const{audio:r,video:a,audiovideo:o}=t;if(r){let l=e.audioCodec;const u=navigator.userAgent.toLowerCase();if(this.audioCodecSwitch){l&&(l.indexOf("mp4a.40.5")!==-1?l="mp4a.40.2":l="mp4a.40.5");const c=r.metadata;c&&"channelCount"in c&&(c.channelCount||1)!==1&&u.indexOf("firefox")===-1&&(l="mp4a.40.5")}l&&l.indexOf("mp4a.40.5")!==-1&&u.indexOf("android")!==-1&&r.container!=="audio/mpeg"&&(l="mp4a.40.2",this.log(`Android: force audio codec to ${l}`)),e.audioCodec&&e.audioCodec!==l&&this.log(`Swapping manifest audio codec "${e.audioCodec}" for "${l}"`),r.levelCodec=l,r.id="main",this.log(`Init audio buffer, container:${r.container}, codecs[selected/level/parsed]=[${l||""}/${e.audioCodec||""}/${r.codec}]`)}a&&(a.levelCodec=e.videoCodec,a.id="main",this.log(`Init video buffer, container:${a.container}, codecs[level/parsed]=[${e.videoCodec||""}/${a.codec}]`)),o&&this.log(`Init audiovideo buffer, container:${o.container}, codecs[level/parsed]=[${e.codecs}/${o.codec}]`),this.hls.trigger(v.BUFFER_CODECS,t),Object.keys(t).forEach(l=>{const c=t[l].initSegment;c!=null&&c.byteLength&&this.hls.trigger(v.BUFFER_APPENDING,{type:l,data:c,frag:i,part:null,chunkMeta:n,parent:i.type})}),this.tickImmediate()}getMainFwdBufferInfo(){return this.getFwdBufferInfo(this.mediaBuffer?this.mediaBuffer:this.media,ne.MAIN)}backtrack(e){this.couldBacktrack=!0,this.backtrackFragment=e,this.resetTransmuxer(),this.flushBufferGap(e),this.fragmentTracker.removeFragment(e),this.fragPrevious=null,this.nextLoadPosition=e.start,this.state=G.IDLE}checkFragmentChanged(){const e=this.media;let t=null;if(e&&e.readyState>1&&e.seeking===!1){const i=e.currentTime;if(me.isBuffered(e,i)?t=this.getAppendedFrag(i):me.isBuffered(e,i+.1)&&(t=this.getAppendedFrag(i+.1)),t){this.backtrackFragment=null;const n=this.fragPlaying,r=t.level;(!n||t.sn!==n.sn||n.level!==r)&&(this.fragPlaying=t,this.hls.trigger(v.FRAG_CHANGED,{frag:t}),(!n||n.level!==r)&&this.hls.trigger(v.LEVEL_SWITCHED,{level:r}))}}}get nextLevel(){const e=this.nextBufferedFrag;return e?e.level:-1}get currentFrag(){const e=this.media;return e?this.fragPlaying||this.getAppendedFrag(e.currentTime):null}get currentProgramDateTime(){const e=this.media;if(e){const t=e.currentTime,i=this.currentFrag;if(i&&J(t)&&J(i.programDateTime)){const n=i.programDateTime+(t-i.start)*1e3;return new Date(n)}}return null}get currentLevel(){const e=this.currentFrag;return e?e.level:-1}get nextBufferedFrag(){const e=this.currentFrag;return e?this.followingBufferedFrag(e):null}get forceStartLoad(){return this._forceStartLoad}}class We{static get version(){return"1.5.20"}static isMSESupported(){return ul()}static isSupported(){return Vh()}static getMediaSource(){return _t()}static get Events(){return v}static get ErrorTypes(){return re}static get ErrorDetails(){return O}static get DefaultConfig(){return We.defaultConfig?We.defaultConfig:ol}static set DefaultConfig(e){We.defaultConfig=e}constructor(e={}){this.config=void 0,this.userConfig=void 0,this.coreComponents=void 0,this.networkControllers=void 0,this.started=!1,this._emitter=new $n,this._autoLevelCapping=-1,this._maxHdcpLevel=null,this.abrController=void 0,this.bufferController=void 0,this.capLevelController=void 0,this.latencyController=void 0,this.levelController=void 0,this.streamController=void 0,this.audioTrackController=void 0,this.subtitleTrackController=void 0,this.emeController=void 0,this.cmcdController=void 0,this._media=null,this.url=null,this.triggeringException=void 0,Mu(e.debug||!1,"Hls instance");const t=this.config=Bh(We.DefaultConfig,e);this.userConfig=e,t.progressive&&Uh(t);const{abrController:i,bufferController:n,capLevelController:r,errorController:a,fpsController:o}=t,l=new a(this),u=this.abrController=new i(this),c=this.bufferController=new n(this),d=this.capLevelController=new r(this),f=new o(this),h=new wc(this),g=new Oc(this),p=t.contentSteeringController,m=p?new p(this):null,y=this.levelController=new $h(this,m),S=new cd(this),T=new Gh(this.config),I=this.streamController=new jh(this,S,T);d.setStreamController(I),f.setStreamController(I);const E=[h,y,I];m&&E.splice(1,0,m),this.networkControllers=E;const P=[u,c,d,f,g,S];this.audioTrackController=this.createController(t.audioTrackController,E);const R=t.audioStreamController;R&&E.push(new R(this,S,T)),this.subtitleTrackController=this.createController(t.subtitleTrackController,E);const w=t.subtitleStreamController;w&&E.push(new w(this,S,T)),this.createController(t.timelineController,P),T.emeController=this.emeController=this.createController(t.emeController,P),this.cmcdController=this.createController(t.cmcdController,P),this.latencyController=this.createController(Mc,P),this.coreComponents=P,E.push(l);const b=l.onErrorOut;typeof b=="function"&&this.on(v.ERROR,b,l)}createController(e,t){if(e){const i=new e(this);return t&&t.push(i),i}return null}on(e,t,i=this){this._emitter.on(e,t,i)}once(e,t,i=this){this._emitter.once(e,t,i)}removeAllListeners(e){this._emitter.removeAllListeners(e)}off(e,t,i=this,n){this._emitter.off(e,t,i,n)}listeners(e){return this._emitter.listeners(e)}emit(e,t,i){return this._emitter.emit(e,t,i)}trigger(e,t){if(this.config.debug)return this.emit(e,e,t);try{return this.emit(e,e,t)}catch(i){if(_.error("An internal error happened while handling event "+e+'. Error message: "'+i.message+'". Here is a stacktrace:',i),!this.triggeringException){this.triggeringException=!0;const n=e===v.ERROR;this.trigger(v.ERROR,{type:re.OTHER_ERROR,details:O.INTERNAL_EXCEPTION,fatal:n,event:e,error:i}),this.triggeringException=!1}}return!1}listenerCount(e){return this._emitter.listenerCount(e)}destroy(){_.log("destroy"),this.trigger(v.DESTROYING,void 0),this.detachMedia(),this.removeAllListeners(),this._autoLevelCapping=-1,this.url=null,this.networkControllers.forEach(t=>t.destroy()),this.networkControllers.length=0,this.coreComponents.forEach(t=>t.destroy()),this.coreComponents.length=0;const e=this.config;e.xhrSetup=e.fetchSetup=void 0,this.userConfig=null}attachMedia(e){_.log("attachMedia"),this._media=e,this.trigger(v.MEDIA_ATTACHING,{media:e})}detachMedia(){_.log("detachMedia"),this.trigger(v.MEDIA_DETACHING,void 0),this._media=null}loadSource(e){this.stopLoad();const t=this.media,i=this.url,n=this.url=In.buildAbsoluteURL(self.location.href,e,{alwaysNormalize:!0});this._autoLevelCapping=-1,this._maxHdcpLevel=null,_.log(`loadSource:${n}`),t&&i&&(i!==n||this.bufferController.hasSourceTypes())&&(this.detachMedia(),this.attachMedia(t)),this.trigger(v.MANIFEST_LOADING,{url:e})}startLoad(e=-1){_.log(`startLoad(${e})`),this.started=!0,this.resumeBuffering();for(let t=0;t<this.networkControllers.length&&(this.networkControllers[t].startLoad(e),!(!this.started||!this.networkControllers));t++);}stopLoad(){_.log("stopLoad"),this.started=!1;for(let e=0;e<this.networkControllers.length&&(this.networkControllers[e].stopLoad(),!(this.started||!this.networkControllers));e++);}resumeBuffering(){_.log("resume buffering"),this.networkControllers.forEach(e=>{e.resumeBuffering&&e.resumeBuffering()})}pauseBuffering(){_.log("pause buffering"),this.networkControllers.forEach(e=>{e.pauseBuffering&&e.pauseBuffering()})}swapAudioCodec(){_.log("swapAudioCodec"),this.streamController.swapAudioCodec()}recoverMediaError(){_.log("recoverMediaError");const e=this._media;this.detachMedia(),e&&this.attachMedia(e)}removeLevel(e){this.levelController.removeLevel(e)}get levels(){const e=this.levelController.levels;return e||[]}get currentLevel(){return this.streamController.currentLevel}set currentLevel(e){_.log(`set currentLevel:${e}`),this.levelController.manualLevel=e,this.streamController.immediateLevelSwitch()}get nextLevel(){return this.streamController.nextLevel}set nextLevel(e){_.log(`set nextLevel:${e}`),this.levelController.manualLevel=e,this.streamController.nextLevelSwitch()}get loadLevel(){return this.levelController.level}set loadLevel(e){_.log(`set loadLevel:${e}`),this.levelController.manualLevel=e}get nextLoadLevel(){return this.levelController.nextLoadLevel}set nextLoadLevel(e){this.levelController.nextLoadLevel=e}get firstLevel(){return Math.max(this.levelController.firstLevel,this.minAutoLevel)}set firstLevel(e){_.log(`set firstLevel:${e}`),this.levelController.firstLevel=e}get startLevel(){const e=this.levelController.startLevel;return e===-1&&this.abrController.forcedAutoLevel>-1?this.abrController.forcedAutoLevel:e}set startLevel(e){_.log(`set startLevel:${e}`),e!==-1&&(e=Math.max(e,this.minAutoLevel)),this.levelController.startLevel=e}get capLevelToPlayerSize(){return this.config.capLevelToPlayerSize}set capLevelToPlayerSize(e){const t=!!e;t!==this.config.capLevelToPlayerSize&&(t?this.capLevelController.startCapping():(this.capLevelController.stopCapping(),this.autoLevelCapping=-1,this.streamController.nextLevelSwitch()),this.config.capLevelToPlayerSize=t)}get autoLevelCapping(){return this._autoLevelCapping}get bandwidthEstimate(){const{bwEstimator:e}=this.abrController;return e?e.getEstimate():NaN}set bandwidthEstimate(e){this.abrController.resetEstimator(e)}get ttfbEstimate(){const{bwEstimator:e}=this.abrController;return e?e.getEstimateTTFB():NaN}set autoLevelCapping(e){this._autoLevelCapping!==e&&(_.log(`set autoLevelCapping:${e}`),this._autoLevelCapping=e,this.levelController.checkMaxAutoUpdated())}get maxHdcpLevel(){return this._maxHdcpLevel}set maxHdcpLevel(e){Nc(e)&&this._maxHdcpLevel!==e&&(this._maxHdcpLevel=e,this.levelController.checkMaxAutoUpdated())}get autoLevelEnabled(){return this.levelController.manualLevel===-1}get manualLevel(){return this.levelController.manualLevel}get minAutoLevel(){const{levels:e,config:{minAutoBitrate:t}}=this;if(!e)return 0;const i=e.length;for(let n=0;n<i;n++)if(e[n].maxBitrate>=t)return n;return 0}get maxAutoLevel(){const{levels:e,autoLevelCapping:t,maxHdcpLevel:i}=this;let n;if(t===-1&&e!=null&&e.length?n=e.length-1:n=t,i)for(let r=n;r--;){const a=e[r].attrs["HDCP-LEVEL"];if(a&&a<=i)return r}return n}get firstAutoLevel(){return this.abrController.firstAutoLevel}get nextAutoLevel(){return this.abrController.nextAutoLevel}set nextAutoLevel(e){this.abrController.nextAutoLevel=e}get playingDate(){return this.streamController.currentProgramDateTime}get mainForwardBufferInfo(){return this.streamController.getMainFwdBufferInfo()}setAudioOption(e){var t;return(t=this.audioTrackController)==null?void 0:t.setAudioOption(e)}setSubtitleOption(e){var t;return(t=this.subtitleTrackController)==null||t.setSubtitleOption(e),null}get allAudioTracks(){const e=this.audioTrackController;return e?e.allAudioTracks:[]}get audioTracks(){const e=this.audioTrackController;return e?e.audioTracks:[]}get audioTrack(){const e=this.audioTrackController;return e?e.audioTrack:-1}set audioTrack(e){const t=this.audioTrackController;t&&(t.audioTrack=e)}get allSubtitleTracks(){const e=this.subtitleTrackController;return e?e.allSubtitleTracks:[]}get subtitleTracks(){const e=this.subtitleTrackController;return e?e.subtitleTracks:[]}get subtitleTrack(){const e=this.subtitleTrackController;return e?e.subtitleTrack:-1}get media(){return this._media}set subtitleTrack(e){const t=this.subtitleTrackController;t&&(t.subtitleTrack=e)}get subtitleDisplay(){const e=this.subtitleTrackController;return e?e.subtitleDisplay:!1}set subtitleDisplay(e){const t=this.subtitleTrackController;t&&(t.subtitleDisplay=e)}get lowLatencyMode(){return this.config.lowLatencyMode}set lowLatencyMode(e){this.config.lowLatencyMode=e}get liveSyncPosition(){return this.latencyController.liveSyncPosition}get latency(){return this.latencyController.latency}get maxLatency(){return this.latencyController.maxLatency}get targetLatency(){return this.latencyController.targetLatency}get drift(){return this.latencyController.drift}get forceStartLoad(){return this.streamController.forceStartLoad}}We.defaultConfig=void 0;const Xs=ke({name:"QCardSection",props:{tag:{type:String,default:"div"},horizontal:Boolean},setup(s,{slots:e}){const t=U(()=>`q-card__section q-card__section--${s.horizontal===!0?"horiz row no-wrap":"vert"}`);return()=>W(s.tag,{class:t.value},Fe(e.default))}});let Qs,Bi=0;const Ce=new Array(256);for(let s=0;s<256;s++)Ce[s]=(s+256).toString(16).substring(1);const Xh=(()=>{const s=typeof crypto<"u"?crypto:typeof window<"u"?window.crypto||window.msCrypto:void 0;if(s!==void 0){if(s.randomBytes!==void 0)return s.randomBytes;if(s.getRandomValues!==void 0)return e=>{const t=new Uint8Array(e);return s.getRandomValues(t),t}}return e=>{const t=[];for(let i=e;i>0;i--)t.push(Math.floor(Math.random()*256));return t}})(),ga=4096;function An(){(Qs===void 0||Bi+16>ga)&&(Bi=0,Qs=Xh(ga));const s=Array.prototype.slice.call(Qs,Bi,Bi+=16);return s[6]=s[6]&15|64,s[8]=s[8]&63|128,Ce[s[0]]+Ce[s[1]]+Ce[s[2]]+Ce[s[3]]+"-"+Ce[s[4]]+Ce[s[5]]+"-"+Ce[s[6]]+Ce[s[7]]+"-"+Ce[s[8]]+Ce[s[9]]+"-"+Ce[s[10]]+Ce[s[11]]+Ce[s[12]]+Ce[s[13]]+Ce[s[14]]+Ce[s[15]]}function Qh(s){return s??null}function ma(s,e){return s??(e===!0?`f_${An()}`:null)}function Zh({getValue:s,required:e=!0}={}){if(Xl.value===!0){const t=s!==void 0?te(Qh(s())):te(null);return e===!0&&t.value===null&&gt(()=>{t.value=`f_${An()}`}),s!==void 0&&le(s,i=>{t.value=ma(i,e)}),t}return s!==void 0?U(()=>ma(s(),e)):te(`f_${An()}`)}const pa=/^on[A-Z]/;function Jh(){const{attrs:s,vnode:e}=Ee(),t={listeners:te({}),attributes:te({})};function i(){const n={},r={};for(const a in s)a!=="class"&&a!=="style"&&pa.test(a)===!1&&(n[a]=s[a]);for(const a in e.props)pa.test(a)===!0&&(r[a]=e.props[a]);t.attributes.value=n,t.listeners.value=r}return Ga(i),i(),t}const Pt={dark:{type:Boolean,default:null}};function Ft(s,e){return U(()=>s.dark===null?e.dark.isActive:s.dark)}function eg({validate:s,resetValidation:e,requiresQForm:t}){const i=an(Ql,!1);if(i!==!1){const{props:n,proxy:r}=Ee();Object.assign(r,{validate:s,resetValidation:e}),le(()=>n.disable,a=>{a===!0?(typeof e=="function"&&e(),i.unbindComponent(r)):i.bindComponent(r)}),gt(()=>{n.disable!==!0&&i.bindComponent(r)}),Be(()=>{n.disable!==!0&&i.unbindComponent(r)})}else t===!0&&console.error("Parent QForm not found on useFormChild()!")}const va=/^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,ya=/^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,Sa=/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,Ui=/^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,$i=/^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/,Zs={date:s=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(s),time:s=>/^([0-1]?\d|2[0-3]):[0-5]\d$/.test(s),fulltime:s=>/^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(s),timeOrFulltime:s=>/^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(s),email:s=>/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(s),hexColor:s=>va.test(s),hexaColor:s=>ya.test(s),hexOrHexaColor:s=>Sa.test(s),rgbColor:s=>Ui.test(s),rgbaColor:s=>$i.test(s),rgbOrRgbaColor:s=>Ui.test(s)||$i.test(s),hexOrRgbColor:s=>va.test(s)||Ui.test(s),hexaOrRgbaColor:s=>ya.test(s)||$i.test(s),anyColor:s=>Sa.test(s)||Ui.test(s)||$i.test(s)},tg=[!0,!1,"ondemand"],ig={modelValue:{},error:{type:Boolean,default:null},errorMessage:String,noErrorIcon:Boolean,rules:Array,reactiveRules:Boolean,lazyRules:{type:[Boolean,String],default:!1,validator:s=>tg.includes(s)}};function sg(s,e){const{props:t,proxy:i}=Ee(),n=te(!1),r=te(null),a=te(!1);eg({validate:p,resetValidation:g});let o=0,l;const u=U(()=>t.rules!==void 0&&t.rules!==null&&t.rules.length!==0),c=U(()=>t.disable!==!0&&u.value===!0&&e.value===!1),d=U(()=>t.error===!0||n.value===!0),f=U(()=>typeof t.errorMessage=="string"&&t.errorMessage.length!==0?t.errorMessage:r.value);le(()=>t.modelValue,()=>{a.value=!0,c.value===!0&&t.lazyRules===!1&&m()});function h(){t.lazyRules!=="ondemand"&&c.value===!0&&a.value===!0&&m()}le(()=>t.reactiveRules,y=>{y===!0?l===void 0&&(l=le(()=>t.rules,h,{immediate:!0,deep:!0})):l!==void 0&&(l(),l=void 0)},{immediate:!0}),le(()=>t.lazyRules,h),le(s,y=>{y===!0?a.value=!0:c.value===!0&&t.lazyRules!=="ondemand"&&m()});function g(){o++,e.value=!1,a.value=!1,n.value=!1,r.value=null,m.cancel()}function p(y=t.modelValue){if(t.disable===!0||u.value===!1)return!0;const S=++o,T=e.value!==!0?()=>{a.value=!0}:()=>{},I=(P,R)=>{P===!0&&T(),n.value=P,r.value=R||null,e.value=!1},E=[];for(let P=0;P<t.rules.length;P++){const R=t.rules[P];let w;if(typeof R=="function"?w=R(y,Zs):typeof R=="string"&&Zs[R]!==void 0&&(w=Zs[R](y)),w===!1||typeof w=="string")return I(!0,w),!1;w!==!0&&w!==void 0&&E.push(w)}return E.length===0?(I(!1),!0):(e.value=!0,Promise.all(E).then(P=>{if(P===void 0||Array.isArray(P)===!1||P.length===0)return S===o&&I(!1),!0;const R=P.find(w=>w===!1||typeof w=="string");return S===o&&I(R!==void 0,R),R===void 0},P=>(S===o&&(console.error(P),I(!0)),!1)))}const m=Va(p,0);return Be(()=>{l?.(),m.cancel()}),Object.assign(i,{resetValidation:g,validate:p}),Rn(i,"hasError",()=>d.value),{isDirtyModel:a,hasRules:u,hasError:d,errorMessage:f,validate:p,resetValidation:g}}let Lt=[],pi=[];function cl(s){pi=pi.filter(e=>e!==s)}function ng(s){cl(s),pi.push(s)}function Ta(s){cl(s),pi.length===0&&Lt.length!==0&&(Lt[Lt.length-1](),Lt=[])}function Ss(s){pi.length===0?s():Lt.push(s)}function rg(s){Lt=Lt.filter(e=>e!==s)}function vi(s){return s!=null&&(""+s).length!==0}const ag={...Pt,...ig,label:String,stackLabel:Boolean,hint:String,hideHint:Boolean,prefix:String,suffix:String,labelColor:String,color:String,bgColor:String,filled:Boolean,outlined:Boolean,borderless:Boolean,standout:[Boolean,String],square:Boolean,loading:Boolean,labelSlot:Boolean,bottomSlots:Boolean,hideBottomSpace:Boolean,rounded:Boolean,dense:Boolean,itemAligned:Boolean,counter:Boolean,clearable:Boolean,clearIcon:String,disable:Boolean,readonly:Boolean,autofocus:Boolean,for:String},Ts={...ag,maxlength:[Number,String]},qn=["update:modelValue","clear","focus","blur"];function Wn({requiredForAttr:s=!0,tagProp:e,changeEvent:t=!1}={}){const{props:i,proxy:n}=Ee(),r=Ft(i,n.$q),a=Zh({required:s,getValue:()=>i.for});return{requiredForAttr:s,changeEvent:t,tag:e===!0?U(()=>i.tag):{value:"label"},isDark:r,editable:U(()=>i.disable!==!0&&i.readonly!==!0),innerLoading:te(!1),focused:te(!1),hasPopupOpen:!1,splitAttrs:Jh(),targetUid:a,rootRef:te(null),targetRef:te(null),controlRef:te(null)}}function Yn(s){const{props:e,emit:t,slots:i,attrs:n,proxy:r}=Ee(),{$q:a}=r;let o=null;s.hasValue===void 0&&(s.hasValue=U(()=>vi(e.modelValue))),s.emitValue===void 0&&(s.emitValue=V=>{t("update:modelValue",V)}),s.controlEvents===void 0&&(s.controlEvents={onFocusin:b,onFocusout:x}),Object.assign(s,{clearValue:D,onControlFocusin:b,onControlFocusout:x,focus:R}),s.computedCounter===void 0&&(s.computedCounter=U(()=>{if(e.counter!==!1){const V=typeof e.modelValue=="string"||typeof e.modelValue=="number"?(""+e.modelValue).length:Array.isArray(e.modelValue)===!0?e.modelValue.length:0,B=e.maxlength!==void 0?e.maxlength:e.maxValues;return V+(B!==void 0?" / "+B:"")}}));const{isDirtyModel:l,hasRules:u,hasError:c,errorMessage:d,resetValidation:f}=sg(s.focused,s.innerLoading),h=s.floatingLabel!==void 0?U(()=>e.stackLabel===!0||s.focused.value===!0||s.floatingLabel.value===!0):U(()=>e.stackLabel===!0||s.focused.value===!0||s.hasValue.value===!0),g=U(()=>e.bottomSlots===!0||e.hint!==void 0||u.value===!0||e.counter===!0||e.error!==null),p=U(()=>e.filled===!0?"filled":e.outlined===!0?"outlined":e.borderless===!0?"borderless":e.standout?"standout":"standard"),m=U(()=>`q-field row no-wrap items-start q-field--${p.value}`+(s.fieldClass!==void 0?` ${s.fieldClass.value}`:"")+(e.rounded===!0?" q-field--rounded":"")+(e.square===!0?" q-field--square":"")+(h.value===!0?" q-field--float":"")+(S.value===!0?" q-field--labeled":"")+(e.dense===!0?" q-field--dense":"")+(e.itemAligned===!0?" q-field--item-aligned q-item-type":"")+(s.isDark.value===!0?" q-field--dark":"")+(s.getControl===void 0?" q-field--auto-height":"")+(s.focused.value===!0?" q-field--focused":"")+(c.value===!0?" q-field--error":"")+(c.value===!0||s.focused.value===!0?" q-field--highlighted":"")+(e.hideBottomSpace!==!0&&g.value===!0?" q-field--with-bottom":"")+(e.disable===!0?" q-field--disabled":e.readonly===!0?" q-field--readonly":"")),y=U(()=>"q-field__control relative-position row no-wrap"+(e.bgColor!==void 0?` bg-${e.bgColor}`:"")+(c.value===!0?" text-negative":typeof e.standout=="string"&&e.standout.length!==0&&s.focused.value===!0?` ${e.standout}`:e.color!==void 0?` text-${e.color}`:"")),S=U(()=>e.labelSlot===!0||e.label!==void 0),T=U(()=>"q-field__label no-pointer-events absolute ellipsis"+(e.labelColor!==void 0&&c.value!==!0?` text-${e.labelColor}`:"")),I=U(()=>({id:s.targetUid.value,editable:s.editable.value,focused:s.focused.value,floatingLabel:h.value,modelValue:e.modelValue,emitValue:s.emitValue})),E=U(()=>{const V={};return s.targetUid.value&&(V.for=s.targetUid.value),e.disable===!0&&(V["aria-disabled"]="true"),V});function P(){const V=document.activeElement;let B=s.targetRef?.value;B&&(V===null||V.id!==s.targetUid.value)&&(B.hasAttribute("tabindex")===!0||(B=B.querySelector("[tabindex]")),B!==V&&B?.focus({preventScroll:!0}))}function R(){Ss(P)}function w(){rg(P);const V=document.activeElement;V!==null&&s.rootRef.value.contains(V)&&V.blur()}function b(V){o!==null&&(clearTimeout(o),o=null),s.editable.value===!0&&s.focused.value===!1&&(s.focused.value=!0,t("focus",V))}function x(V,B){o!==null&&clearTimeout(o),o=setTimeout(()=>{o=null,!(document.hasFocus()===!0&&(s.hasPopupOpen===!0||s.controlRef===void 0||s.controlRef.value===null||s.controlRef.value.contains(document.activeElement)!==!1))&&(s.focused.value===!0&&(s.focused.value=!1,t("blur",V)),B?.())})}function D(V){Ne(V),a.platform.is.mobile!==!0?(s.targetRef?.value||s.rootRef.value).focus():s.rootRef.value.contains(document.activeElement)===!0&&document.activeElement.blur(),e.type==="file"&&(s.inputRef.value.value=null),t("update:modelValue",null),s.changeEvent===!0&&t("change",null),t("clear",e.modelValue),Se(()=>{const B=l.value;f(),l.value=B})}function C(V){[13,32].includes(V.keyCode)&&D(V)}function M(){const V=[];return i.prepend!==void 0&&V.push(W("div",{class:"q-field__prepend q-field__marginal row no-wrap items-center",key:"prepend",onClick:et},i.prepend())),V.push(W("div",{class:"q-field__control-container col relative-position row no-wrap q-anchor--skip"},N())),c.value===!0&&e.noErrorIcon===!1&&V.push(K("error",[W(Rt,{name:a.iconSet.field.error,color:"negative"})])),e.loading===!0||s.innerLoading.value===!0?V.push(K("inner-loading-append",i.loading!==void 0?i.loading():[W(gu,{color:e.color})])):e.clearable===!0&&s.hasValue.value===!0&&s.editable.value===!0&&V.push(K("inner-clearable-append",[W(Rt,{class:"q-field__focusable-action",name:e.clearIcon||a.iconSet.field.clear,tabindex:0,role:"button","aria-hidden":"false","aria-label":a.lang.label.clear,onKeyup:C,onClick:D})])),i.append!==void 0&&V.push(W("div",{class:"q-field__append q-field__marginal row no-wrap items-center",key:"append",onClick:et},i.append())),s.getInnerAppend!==void 0&&V.push(K("inner-append",s.getInnerAppend())),s.getControlChild!==void 0&&V.push(s.getControlChild()),V}function N(){const V=[];return e.prefix!==void 0&&e.prefix!==null&&V.push(W("div",{class:"q-field__prefix no-pointer-events row items-center"},e.prefix)),s.getShadowControl!==void 0&&s.hasShadow.value===!0&&V.push(s.getShadowControl()),s.getControl!==void 0?V.push(s.getControl()):i.rawControl!==void 0?V.push(i.rawControl()):i.control!==void 0&&V.push(W("div",{ref:s.targetRef,class:"q-field__native row",tabindex:-1,...s.splitAttrs.attributes.value,"data-autofocus":e.autofocus===!0||void 0},i.control(I.value))),S.value===!0&&V.push(W("div",{class:T.value},Fe(i.label,e.label))),e.suffix!==void 0&&e.suffix!==null&&V.push(W("div",{class:"q-field__suffix no-pointer-events row items-center"},e.suffix)),V.concat(Fe(i.default))}function Y(){let V,B;c.value===!0?d.value!==null?(V=[W("div",{role:"alert"},d.value)],B=`q--slot-error-${d.value}`):(V=Fe(i.error),B="q--slot-error"):(e.hideHint!==!0||s.focused.value===!0)&&(e.hint!==void 0?(V=[W("div",e.hint)],B=`q--slot-hint-${e.hint}`):(V=Fe(i.hint),B="q--slot-hint"));const $=e.counter===!0||i.counter!==void 0;if(e.hideBottomSpace===!0&&$===!1&&V===void 0)return;const k=W("div",{key:B,class:"q-field__messages col"},V);return W("div",{class:"q-field__bottom row items-start q-field__bottom--"+(e.hideBottomSpace!==!0?"animated":"stale"),onClick:et},[e.hideBottomSpace===!0?k:W(Ji,{name:"q-transition--field-message"},()=>k),$===!0?W("div",{class:"q-field__counter"},i.counter!==void 0?i.counter():s.computedCounter.value):null])}function K(V,B){return B===null?null:W("div",{key:V,class:"q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"},B)}let z=!1;return ps(()=>{z=!0}),Ka(()=>{z===!0&&e.autofocus===!0&&r.focus()}),e.autofocus===!0&&gt(()=>{r.focus()}),Be(()=>{o!==null&&clearTimeout(o)}),Object.assign(r,{focus:R,blur:w}),function(){const B=s.getControl===void 0&&i.control===void 0?{...s.splitAttrs.attributes.value,"data-autofocus":e.autofocus===!0||void 0,...E.value}:E.value;return W(s.tag.value,{ref:s.rootRef,class:[m.value,n.class],style:n.style,...B},[i.before!==void 0?W("div",{class:"q-field__before q-field__marginal row no-wrap items-center",onClick:et},i.before()):null,W("div",{class:"q-field__inner relative-position col self-stretch"},[W("div",{ref:s.controlRef,class:y.value,tabindex:-1,...s.controlEvents},M()),g.value===!0?Y():null]),i.after!==void 0?W("div",{class:"q-field__after q-field__marginal row no-wrap items-center",onClick:et},i.after()):null])}}const og=ke({name:"QField",inheritAttrs:!1,props:{...Ts,tag:{type:String,default:"label"}},emits:qn,setup(){return Yn(Wn({tagProp:!0}))}}),lg={xs:8,sm:10,md:14,lg:20,xl:24},ug=ke({name:"QChip",props:{...Pt,...Xa,dense:Boolean,icon:String,iconRight:String,iconRemove:String,iconSelected:String,label:[String,Number],color:String,textColor:String,modelValue:{type:Boolean,default:!0},selected:{type:Boolean,default:null},square:Boolean,outline:Boolean,clickable:Boolean,removable:Boolean,removeAriaLabel:String,tabindex:[String,Number],disable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","update:selected","remove","click"],setup(s,{slots:e,emit:t}){const{proxy:{$q:i}}=Ee(),n=Ft(s,i),r=Qa(s,lg),a=U(()=>s.selected===!0||s.icon!==void 0),o=U(()=>s.selected===!0?s.iconSelected||i.iconSet.chip.selected:s.icon),l=U(()=>s.iconRemove||i.iconSet.chip.remove),u=U(()=>s.disable===!1&&(s.clickable===!0||s.selected!==null)),c=U(()=>{const m=s.outline===!0&&s.color||s.textColor;return"q-chip row inline no-wrap items-center"+(s.outline===!1&&s.color!==void 0?` bg-${s.color}`:"")+(m?` text-${m} q-chip--colored`:"")+(s.disable===!0?" disabled":"")+(s.dense===!0?" q-chip--dense":"")+(s.outline===!0?" q-chip--outline":"")+(s.selected===!0?" q-chip--selected":"")+(u.value===!0?" q-chip--clickable cursor-pointer non-selectable q-hoverable":"")+(s.square===!0?" q-chip--square":"")+(n.value===!0?" q-chip--dark q-dark":"")}),d=U(()=>{const m=s.disable===!0?{tabindex:-1,"aria-disabled":"true"}:{tabindex:s.tabindex||0},y={...m,role:"button","aria-hidden":"false","aria-label":s.removeAriaLabel||i.lang.label.remove};return{chip:m,remove:y}});function f(m){m.keyCode===13&&h(m)}function h(m){s.disable||(t("update:selected",!s.selected),t("click",m))}function g(m){(m.keyCode===void 0||m.keyCode===13)&&(Ne(m),s.disable===!1&&(t("update:modelValue",!1),t("remove")))}function p(){const m=[];u.value===!0&&m.push(W("div",{class:"q-focus-helper"})),a.value===!0&&m.push(W(Rt,{class:"q-chip__icon q-chip__icon--left",name:o.value}));const y=s.label!==void 0?[W("div",{class:"ellipsis"},[s.label])]:void 0;return m.push(W("div",{class:"q-chip__content col row no-wrap items-center q-anchor--skip"},fu(e.default,y))),s.iconRight&&m.push(W(Rt,{class:"q-chip__icon q-chip__icon--right",name:s.iconRight})),s.removable===!0&&m.push(W(Rt,{class:"q-chip__icon q-chip__icon--remove cursor-pointer",name:l.value,...d.value.remove,onClick:g,onKeyup:g})),m}return()=>{if(s.modelValue===!1)return;const m={class:c.value,style:r.value};return u.value===!0&&Object.assign(m,d.value.chip,{onClick:h,onKeyup:f}),du("div",m,p(),"ripple",s.ripple!==!1&&s.disable!==!0,()=>[[mu,s.ripple]])}}}),dl=ke({name:"QItem",props:{...Pt,...pu,tag:{type:String,default:"div"},active:{type:Boolean,default:null},clickable:Boolean,dense:Boolean,insetLevel:Number,tabindex:[String,Number],focused:Boolean,manualFocus:Boolean},emits:["click","keyup"],setup(s,{slots:e,emit:t}){const{proxy:{$q:i}}=Ee(),n=Ft(s,i),{hasLink:r,linkAttrs:a,linkClass:o,linkTag:l,navigateOnClick:u}=vu(),c=te(null),d=te(null),f=U(()=>s.clickable===!0||r.value===!0||s.tag==="label"),h=U(()=>s.disable!==!0&&f.value===!0),g=U(()=>"q-item q-item-type row no-wrap"+(s.dense===!0?" q-item--dense":"")+(n.value===!0?" q-item--dark":"")+(r.value===!0&&s.active===null?o.value:s.active===!0?` q-item--active${s.activeClass!==void 0?` ${s.activeClass}`:""}`:"")+(s.disable===!0?" disabled":"")+(h.value===!0?" q-item--clickable q-link cursor-pointer "+(s.manualFocus===!0?"q-manual-focusable":"q-focusable q-hoverable")+(s.focused===!0?" q-manual-focusable--focused":""):"")),p=U(()=>s.insetLevel===void 0?null:{["padding"+(i.lang.rtl===!0?"Right":"Left")]:16+s.insetLevel*56+"px"});function m(T){h.value===!0&&(d.value!==null&&T.qAvoidFocus!==!0&&(T.qKeyEvent!==!0&&document.activeElement===c.value?d.value.focus():document.activeElement===d.value&&c.value.focus()),u(T))}function y(T){if(h.value===!0&&yi(T,[13,32])===!0){Ne(T),T.qKeyEvent=!0;const I=new MouseEvent("click",T);I.qKeyEvent=!0,c.value.dispatchEvent(I)}t("keyup",T)}function S(){const T=hu(e.default,[]);return h.value===!0&&T.unshift(W("div",{class:"q-focus-helper",tabindex:-1,ref:d})),T}return()=>{const T={ref:c,class:g.value,style:p.value,role:"listitem",onClick:m,onKeyup:y};return h.value===!0?(T.tabindex=s.tabindex||"0",Object.assign(T,a.value)):f.value===!0&&(T["aria-disabled"]="true"),W(l.value,T,S())}}}),fl=ke({name:"QItemSection",props:{avatar:Boolean,thumbnail:Boolean,side:Boolean,top:Boolean,noWrap:Boolean},setup(s,{slots:e}){const t=U(()=>`q-item__section column q-item__section--${s.avatar===!0||s.side===!0||s.thumbnail===!0?"side":"main"}`+(s.top===!0?" q-item__section--top justify-start":" justify-center")+(s.avatar===!0?" q-item__section--avatar":"")+(s.thumbnail===!0?" q-item__section--thumbnail":"")+(s.noWrap===!0?" q-item__section--nowrap":""));return()=>W("div",{class:t.value},Fe(e.default))}}),cg=ke({name:"QItemLabel",props:{overline:Boolean,caption:Boolean,header:Boolean,lines:[Number,String]},setup(s,{slots:e}){const t=U(()=>parseInt(s.lines,10)),i=U(()=>"q-item__label"+(s.overline===!0?" q-item__label--overline text-overline":"")+(s.caption===!0?" q-item__label--caption text-caption":"")+(s.header===!0?" q-item__label--header":"")+(t.value===1?" ellipsis":"")),n=U(()=>s.lines!==void 0&&t.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":t.value}:null);return()=>W("div",{style:n.value,class:i.value},Fe(e.default))}});function dg(){if(window.getSelection!==void 0){const s=window.getSelection();s.empty!==void 0?s.empty():s.removeAllRanges!==void 0&&(s.removeAllRanges(),Zl.is.mobile!==!0&&s.addRange(document.createRange()))}else document.selection!==void 0&&document.selection.empty()}const fg={target:{type:[Boolean,String,Element],default:!0},noParentEvent:Boolean},hg={...fg,contextMenu:Boolean};function gg({showing:s,avoidEmit:e,configureAnchorEl:t}){const{props:i,proxy:n,emit:r}=Ee(),a=te(null);let o=null;function l(h){return a.value===null?!1:h===void 0||h.touches===void 0||h.touches.length<=1}const u={};t===void 0&&(Object.assign(u,{hide(h){n.hide(h)},toggle(h){n.toggle(h),h.qAnchorHandled=!0},toggleKey(h){yi(h,13)===!0&&u.toggle(h)},contextClick(h){n.hide(h),et(h),Se(()=>{n.show(h),h.qAnchorHandled=!0})},prevent:et,mobileTouch(h){if(u.mobileCleanup(h),l(h)!==!0)return;n.hide(h),a.value.classList.add("non-selectable");const g=h.target;ur(u,"anchor",[[g,"touchmove","mobileCleanup","passive"],[g,"touchend","mobileCleanup","passive"],[g,"touchcancel","mobileCleanup","passive"],[a.value,"contextmenu","prevent","notPassive"]]),o=setTimeout(()=>{o=null,n.show(h),h.qAnchorHandled=!0},300)},mobileCleanup(h){a.value.classList.remove("non-selectable"),o!==null&&(clearTimeout(o),o=null),s.value===!0&&h!==void 0&&dg()}}),t=function(h=i.contextMenu){if(i.noParentEvent===!0||a.value===null)return;let g;h===!0?n.$q.platform.is.mobile===!0?g=[[a.value,"touchstart","mobileTouch","passive"]]:g=[[a.value,"mousedown","hide","passive"],[a.value,"contextmenu","contextClick","notPassive"]]:g=[[a.value,"click","toggle","passive"],[a.value,"keyup","toggleKey","passive"]],ur(u,"anchor",g)});function c(){Jl(u,"anchor")}function d(h){for(a.value=h;a.value.classList.contains("q-anchor--skip");)a.value=a.value.parentNode;t()}function f(){if(i.target===!1||i.target===""||n.$el.parentNode===null)a.value=null;else if(i.target===!0)d(n.$el.parentNode);else{let h=i.target;if(typeof i.target=="string")try{h=document.querySelector(i.target)}catch{h=void 0}h!=null?(a.value=h.$el||h,t()):(a.value=null,console.error(`Anchor: target "${i.target}" not found`))}}return le(()=>i.contextMenu,h=>{a.value!==null&&(c(),t(h))}),le(()=>i.target,()=>{a.value!==null&&c(),f()}),le(()=>i.noParentEvent,h=>{a.value!==null&&(h===!0?c():t())}),gt(()=>{f(),e!==!0&&i.modelValue===!0&&a.value===null&&r("update:modelValue",!1)}),Be(()=>{o!==null&&clearTimeout(o),c()}),{anchorEl:a,canShow:l,anchorEvents:u}}function mg(s,e){const t=te(null);let i;function n(o,l){const u=`${l!==void 0?"add":"remove"}EventListener`,c=l!==void 0?l:i;o!==window&&o[u]("scroll",c,Qe.passive),window[u]("scroll",c,Qe.passive),i=l}function r(){t.value!==null&&(n(t.value),t.value=null)}const a=le(()=>s.noParentEvent,()=>{t.value!==null&&(r(),e())});return Be(a),{localScrollTarget:t,unconfigureScrollTarget:r,changeScrollEvent:n}}const hl={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},gl=["beforeShow","show","beforeHide","hide"];function ml({showing:s,canShow:e,hideOnRouteChange:t,handleShow:i,handleHide:n,processOnMount:r}){const a=Ee(),{props:o,emit:l,proxy:u}=a;let c;function d(S){s.value===!0?g(S):f(S)}function f(S){if(o.disable===!0||S?.qAnchorHandled===!0||e!==void 0&&e(S)!==!0)return;const T=o["onUpdate:modelValue"]!==void 0;T===!0&&(l("update:modelValue",!0),c=S,Se(()=>{c===S&&(c=void 0)})),(o.modelValue===null||T===!1)&&h(S)}function h(S){s.value!==!0&&(s.value=!0,l("beforeShow",S),i!==void 0?i(S):l("show",S))}function g(S){if(o.disable===!0)return;const T=o["onUpdate:modelValue"]!==void 0;T===!0&&(l("update:modelValue",!1),c=S,Se(()=>{c===S&&(c=void 0)})),(o.modelValue===null||T===!1)&&p(S)}function p(S){s.value!==!1&&(s.value=!1,l("beforeHide",S),n!==void 0?n(S):l("hide",S))}function m(S){o.disable===!0&&S===!0?o["onUpdate:modelValue"]!==void 0&&l("update:modelValue",!1):S===!0!==s.value&&(S===!0?h:p)(c)}le(()=>o.modelValue,m),t!==void 0&&yu(a)===!0&&le(()=>u.$route.fullPath,()=>{t.value===!0&&s.value===!0&&g()}),gt(()=>{m(o.modelValue)});const y={show:f,hide:g,toggle:d};return Object.assign(u,y),y}let pg=1,vg=document.body;function yg(s,e){const t=document.createElement("div");if(t.id=e!==void 0?`q-portal--${e}--${pg++}`:s,cr.globalNodes!==void 0){const i=cr.globalNodes.class;i!==void 0&&(t.className=i)}return vg.appendChild(t),t}function Sg(s){s.remove()}const Xt=[];function Tg(s){return Xt.find(e=>e.contentEl!==null&&e.contentEl.contains(s))}function pl(s,e){do{if(s.$options.name==="QMenu"){if(s.hide(e),s.$props.separateClosePopup===!0)return Hi(s)}else if(s.__qPortal===!0){const t=Hi(s);return t?.$options.name==="QPopupProxy"?(s.hide(e),t):s}s=Hi(s)}while(s!=null)}function xg(s,e,t){for(;t!==0&&s!==void 0&&s!==null;){if(s.__qPortal===!0){if(t--,s.$options.name==="QMenu"){s=pl(s,e);continue}s.hide(e)}s=Hi(s)}}const Eg=ke({name:"QPortal",setup(s,{slots:e}){return()=>e.default()}});function Ag(s){for(s=s.parent;s!=null;){if(s.type.name==="QGlobalDialog")return!0;if(s.type.name==="QDialog"||s.type.name==="QMenu")return!1;s=s.parent}return!1}function vl(s,e,t,i){const n=te(!1),r=te(!1);let a=null;const o={},l=i==="dialog"&&Ag(s);function u(d){if(d===!0){Ta(o),r.value=!0;return}r.value=!1,n.value===!1&&(l===!1&&a===null&&(a=yg(!1,i)),n.value=!0,Xt.push(s.proxy),ng(o))}function c(d){if(r.value=!1,d!==!0)return;Ta(o),n.value=!1;const f=Xt.indexOf(s.proxy);f!==-1&&Xt.splice(f,1),a!==null&&(Sg(a),a=null)}return Ha(()=>{c(!0)}),s.proxy.__qPortal=!0,Rn(s.proxy,"contentEl",()=>e.value),{showPortal:u,hidePortal:c,portalIsActive:n,portalIsAccessible:r,renderPortal:()=>l===!0?t():n.value===!0?[W(eu,{to:a},W(Eg,t))]:void 0}}const yl={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function Sl(s,e=()=>{},t=()=>{}){return{transitionProps:U(()=>{const i=`q-transition--${s.transitionShow||e()}`,n=`q-transition--${s.transitionHide||t()}`;return{appear:!0,enterFromClass:`${i}-enter-from`,enterActiveClass:`${i}-enter-active`,enterToClass:`${i}-enter-to`,leaveFromClass:`${n}-leave-from`,leaveActiveClass:`${n}-leave-active`,leaveToClass:`${n}-leave-to`}}),transitionStyle:U(()=>`--q-transition-duration: ${s.transitionDuration}ms`)}}function Tl(){let s;const e=Ee();function t(){s=void 0}return ps(t),Be(t),{removeTick:t,registerTick(i){s=i,Se(()=>{s===i&&(Za(e)===!1&&s(),s=void 0)})}}}function xl(){let s=null;const e=Ee();function t(){s!==null&&(clearTimeout(s),s=null)}return ps(t),Be(t),{removeTimeout:t,registerTimeout(i,n){t(),Za(e)===!1&&(s=setTimeout(()=>{s=null,i()},n))}}}const Dt=[];let ti;function bg(s){ti=s.keyCode===27}function Lg(){ti===!0&&(ti=!1)}function Rg(s){ti===!0&&(ti=!1,yi(s,27)===!0&&Dt[Dt.length-1](s))}function El(s){window[s]("keydown",bg),window[s]("blur",Lg),window[s]("keyup",Rg),ti=!1}function Al(s){tt.is.desktop===!0&&(Dt.push(s),Dt.length===1&&El("addEventListener"))}function ds(s){const e=Dt.indexOf(s);e!==-1&&(Dt.splice(e,1),Dt.length===0&&El("removeEventListener"))}const wt=[];function bl(s){wt[wt.length-1](s)}function Ll(s){tt.is.desktop===!0&&(wt.push(s),wt.length===1&&document.body.addEventListener("focusin",bl))}function bn(s){const e=wt.indexOf(s);e!==-1&&(wt.splice(e,1),wt.length===0&&document.body.removeEventListener("focusin",bl))}const{notPassiveCapture:fs}=Qe,kt=[];function hs(s){const e=s.target;if(e===void 0||e.nodeType===8||e.classList.contains("no-pointer-events")===!0)return;let t=Xt.length-1;for(;t>=0;){const i=Xt[t].$;if(i.type.name==="QTooltip"){t--;continue}if(i.type.name!=="QDialog")break;if(i.props.seamless!==!0)return;t--}for(let i=kt.length-1;i>=0;i--){const n=kt[i];if((n.anchorEl.value===null||n.anchorEl.value.contains(e)===!1)&&(e===document.body||n.innerRef.value!==null&&n.innerRef.value.contains(e)===!1))s.qClickOutside=!0,n.onClickOutside(s);else return}}function Ig(s){kt.push(s),kt.length===1&&(document.addEventListener("mousedown",hs,fs),document.addEventListener("touchstart",hs,fs))}function xa(s){const e=kt.findIndex(t=>t===s);e!==-1&&(kt.splice(e,1),kt.length===0&&(document.removeEventListener("mousedown",hs,fs),document.removeEventListener("touchstart",hs,fs)))}let Ea,Aa;function ba(s){const e=s.split(" ");return e.length!==2?!1:["top","center","bottom"].includes(e[0])!==!0?(console.error("Anchor/Self position must start with one of top/center/bottom"),!1):["left","middle","right","start","end"].includes(e[1])!==!0?(console.error("Anchor/Self position must end with one of left/middle/right/start/end"),!1):!0}function Cg(s){return s?!(s.length!==2||typeof s[0]!="number"||typeof s[1]!="number"):!0}const Ln={"start#ltr":"left","start#rtl":"right","end#ltr":"right","end#rtl":"left"};["left","middle","right"].forEach(s=>{Ln[`${s}#ltr`]=s,Ln[`${s}#rtl`]=s});function La(s,e){const t=s.split(" ");return{vertical:t[0],horizontal:Ln[`${t[1]}#${e===!0?"rtl":"ltr"}`]}}function Dg(s,e){let{top:t,left:i,right:n,bottom:r,width:a,height:o}=s.getBoundingClientRect();return e!==void 0&&(t-=e[1],i-=e[0],r+=e[1],n+=e[0],a+=e[0],o+=e[1]),{top:t,bottom:r,height:o,left:i,right:n,width:a,middle:i+(n-i)/2,center:t+(r-t)/2}}function wg(s,e,t){let{top:i,left:n}=s.getBoundingClientRect();return i+=e.top,n+=e.left,t!==void 0&&(i+=t[1],n+=t[0]),{top:i,bottom:i+1,height:1,left:n,right:n+1,width:1,middle:n,center:i}}function kg(s,e){return{top:0,center:e/2,bottom:e,left:0,middle:s/2,right:s}}function Ra(s,e,t,i){return{top:s[t.vertical]-e[i.vertical],left:s[t.horizontal]-e[i.horizontal]}}function Rl(s,e=0){if(s.targetEl===null||s.anchorEl===null||e>5)return;if(s.targetEl.offsetHeight===0||s.targetEl.offsetWidth===0){setTimeout(()=>{Rl(s,e+1)},10);return}const{targetEl:t,offset:i,anchorEl:n,anchorOrigin:r,selfOrigin:a,absoluteOffset:o,fit:l,cover:u,maxHeight:c,maxWidth:d}=s;if(tt.is.ios===!0&&window.visualViewport!==void 0){const P=document.body.style,{offsetLeft:R,offsetTop:w}=window.visualViewport;R!==Ea&&(P.setProperty("--q-pe-left",R+"px"),Ea=R),w!==Aa&&(P.setProperty("--q-pe-top",w+"px"),Aa=w)}const{scrollLeft:f,scrollTop:h}=t,g=o===void 0?Dg(n,u===!0?[0,0]:i):wg(n,o,i);Object.assign(t.style,{top:0,left:0,minWidth:null,minHeight:null,maxWidth:d,maxHeight:c,visibility:"visible"});const{offsetWidth:p,offsetHeight:m}=t,{elWidth:y,elHeight:S}=l===!0||u===!0?{elWidth:Math.max(g.width,p),elHeight:u===!0?Math.max(g.height,m):m}:{elWidth:p,elHeight:m};let T={maxWidth:d,maxHeight:c};(l===!0||u===!0)&&(T.minWidth=g.width+"px",u===!0&&(T.minHeight=g.height+"px")),Object.assign(t.style,T);const I=kg(y,S);let E=Ra(g,I,r,a);if(o===void 0||i===void 0)Js(E,g,I,r,a);else{const{top:P,left:R}=E;Js(E,g,I,r,a);let w=!1;if(E.top!==P){w=!0;const b=2*i[1];g.center=g.top-=b,g.bottom-=b+2}if(E.left!==R){w=!0;const b=2*i[0];g.middle=g.left-=b,g.right-=b+2}w===!0&&(E=Ra(g,I,r,a),Js(E,g,I,r,a))}T={top:E.top+"px",left:E.left+"px"},E.maxHeight!==void 0&&(T.maxHeight=E.maxHeight+"px",g.height>E.maxHeight&&(T.minHeight=T.maxHeight)),E.maxWidth!==void 0&&(T.maxWidth=E.maxWidth+"px",g.width>E.maxWidth&&(T.minWidth=T.maxWidth)),Object.assign(t.style,T),t.scrollTop!==h&&(t.scrollTop=h),t.scrollLeft!==f&&(t.scrollLeft=f)}function Js(s,e,t,i,n){const r=t.bottom,a=t.right,o=xu(),l=window.innerHeight-o,u=document.body.clientWidth;if(s.top<0||s.top+r>l)if(n.vertical==="center")s.top=e[i.vertical]>l/2?Math.max(0,l-r):0,s.maxHeight=Math.min(r,l);else if(e[i.vertical]>l/2){const c=Math.min(l,i.vertical==="center"?e.center:i.vertical===n.vertical?e.bottom:e.top);s.maxHeight=Math.min(r,c),s.top=Math.max(0,c-r)}else s.top=Math.max(0,i.vertical==="center"?e.center:i.vertical===n.vertical?e.top:e.bottom),s.maxHeight=Math.min(r,l-s.top);if(s.left<0||s.left+a>u)if(s.maxWidth=Math.min(a,u),n.horizontal==="middle")s.left=e[i.horizontal]>u/2?Math.max(0,u-a):0;else if(e[i.horizontal]>u/2){const c=Math.min(u,i.horizontal==="middle"?e.middle:i.horizontal===n.horizontal?e.right:e.left);s.maxWidth=Math.min(a,c),s.left=Math.max(0,c-s.maxWidth)}else s.left=Math.max(0,i.horizontal==="middle"?e.middle:i.horizontal===n.horizontal?e.left:e.right),s.maxWidth=Math.min(a,u-s.left)}const _g=ke({name:"QMenu",inheritAttrs:!1,props:{...hg,...hl,...Pt,...yl,persistent:Boolean,autoClose:Boolean,separateClosePopup:Boolean,noEscDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,fit:Boolean,cover:Boolean,square:Boolean,anchor:{type:String,validator:ba},self:{type:String,validator:ba},offset:{type:Array,validator:Cg},scrollTarget:Eu,touchPosition:Boolean,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null}},emits:[...gl,"click","escapeKey"],setup(s,{slots:e,emit:t,attrs:i}){let n=null,r,a,o;const l=Ee(),{proxy:u}=l,{$q:c}=u,d=te(null),f=te(!1),h=U(()=>s.persistent!==!0&&s.noRouteDismiss!==!0),g=Ft(s,c),{registerTick:p,removeTick:m}=Tl(),{registerTimeout:y}=xl(),{transitionProps:S,transitionStyle:T}=Sl(s),{localScrollTarget:I,changeScrollEvent:E,unconfigureScrollTarget:P}=mg(s,q),{anchorEl:R,canShow:w}=gg({showing:f}),{hide:b}=ml({showing:f,canShow:w,handleShow:$,handleHide:k,hideOnRouteChange:h,processOnMount:!0}),{showPortal:x,hidePortal:D,renderPortal:C}=vl(l,d,ue,"menu"),M={anchorEl:R,innerRef:d,onClickOutside(X){if(s.persistent!==!0&&f.value===!0)return b(X),(X.type==="touchstart"||X.target.classList.contains("q-dialog__backdrop"))&&Ne(X),!0}},N=U(()=>La(s.anchor||(s.cover===!0?"center middle":"bottom start"),c.lang.rtl)),Y=U(()=>s.cover===!0?N.value:La(s.self||"top start",c.lang.rtl)),K=U(()=>(s.square===!0?" q-menu--square":"")+(g.value===!0?" q-menu--dark q-dark":"")),z=U(()=>s.autoClose===!0?{onClick:ee}:{}),V=U(()=>f.value===!0&&s.persistent!==!0);le(V,X=>{X===!0?(Al(H),Ig(M)):(ds(H),xa(M))});function B(){Ss(()=>{let X=d.value;X&&X.contains(document.activeElement)!==!0&&(X=X.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||X.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||X.querySelector("[autofocus], [data-autofocus]")||X,X.focus({preventScroll:!0}))})}function $(X){if(n=s.noRefocus===!1?document.activeElement:null,Ll(ie),x(),q(),r=void 0,X!==void 0&&(s.touchPosition||s.contextMenu)){const ve=tu(X);if(ve.left!==void 0){const{top:Ye,left:Ot}=R.value.getBoundingClientRect();r={left:ve.left-Ot,top:ve.top-Ye}}}a===void 0&&(a=le(()=>c.screen.width+"|"+c.screen.height+"|"+s.self+"|"+s.anchor+"|"+c.lang.rtl,j)),s.noFocus!==!0&&document.activeElement.blur(),p(()=>{j(),s.noFocus!==!0&&B()}),y(()=>{c.platform.is.ios===!0&&(o=s.autoClose,d.value.click()),j(),x(!0),t("show",X)},s.transitionDuration)}function k(X){m(),D(),L(!0),n!==null&&(X===void 0||X.qClickOutside!==!0)&&(((X?.type.indexOf("key")===0?n.closest('[tabindex]:not([tabindex^="-"])'):void 0)||n).focus(),n=null),y(()=>{D(!0),t("hide",X)},s.transitionDuration)}function L(X){r=void 0,a!==void 0&&(a(),a=void 0),(X===!0||f.value===!0)&&(bn(ie),P(),xa(M),ds(H)),X!==!0&&(n=null)}function q(){(R.value!==null||s.scrollTarget!==void 0)&&(I.value=Au(R.value,s.scrollTarget),E(I.value,j))}function ee(X){o!==!0?(pl(u,X),t("click",X)):o=!1}function ie(X){V.value===!0&&s.noFocus!==!0&&za(d.value,X.target)!==!0&&B()}function H(X){s.noEscDismiss!==!0&&(t("escapeKey"),b(X))}function j(){Rl({targetEl:d.value,offset:s.offset,anchorEl:R.value,anchorOrigin:N.value,selfOrigin:Y.value,absoluteOffset:r,fit:s.fit,cover:s.cover,maxHeight:s.maxHeight,maxWidth:s.maxWidth})}function ue(){return W(Ji,S.value,()=>f.value===!0?W("div",{role:"menu",...i,ref:d,tabindex:-1,class:["q-menu q-position-engine scroll"+K.value,i.class],style:[i.style,T.value],...z.value},Fe(e.default)):null)}return Be(L),Object.assign(u,{focus:B,updatePosition:j}),C}});function Pg(s,e,t){let i;function n(){i!==void 0&&(dr.remove(i),i=void 0)}return Be(()=>{s.value===!0&&n()}),{removeFromHistory:n,addToHistory(){i={condition:()=>t.value===!0,handler:e},dr.add(i)}}}let oi=0,en,tn,ui,sn=!1,Ia,Ca,Da,Tt=null;function Fg(s){Og(s)&&Ne(s)}function Og(s){if(s.target===document.body||s.target.classList.contains("q-layout__backdrop"))return!0;const e=iu(s),t=s.shiftKey&&!s.deltaX,i=!t&&Math.abs(s.deltaX)<=Math.abs(s.deltaY),n=t||i?s.deltaY:s.deltaX;for(let r=0;r<e.length;r++){const a=e[r];if(Ru(a,i))return i?n<0&&a.scrollTop===0?!0:n>0&&a.scrollTop+a.clientHeight===a.scrollHeight:n<0&&a.scrollLeft===0?!0:n>0&&a.scrollLeft+a.clientWidth===a.scrollWidth}return!0}function wa(s){s.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function Gi(s){sn!==!0&&(sn=!0,requestAnimationFrame(()=>{sn=!1;const{height:e}=s.target,{clientHeight:t,scrollTop:i}=document.scrollingElement;(ui===void 0||e!==window.innerHeight)&&(ui=t-e,document.scrollingElement.scrollTop=i),i>ui&&(document.scrollingElement.scrollTop-=Math.ceil((i-ui)/8))}))}function ka(s){const e=document.body,t=window.visualViewport!==void 0;if(s==="add"){const{overflowY:i,overflowX:n}=window.getComputedStyle(e);en=bu(window),tn=Lu(window),Ia=e.style.left,Ca=e.style.top,Da=window.location.href,e.style.left=`-${en}px`,e.style.top=`-${tn}px`,n!=="hidden"&&(n==="scroll"||e.scrollWidth>window.innerWidth)&&e.classList.add("q-body--force-scrollbar-x"),i!=="hidden"&&(i==="scroll"||e.scrollHeight>window.innerHeight)&&e.classList.add("q-body--force-scrollbar-y"),e.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,tt.is.ios===!0&&(t===!0?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",Gi,Qe.passiveCapture),window.visualViewport.addEventListener("scroll",Gi,Qe.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",wa,Qe.passiveCapture))}tt.is.desktop===!0&&tt.is.mac===!0&&window[`${s}EventListener`]("wheel",Fg,Qe.notPassive),s==="remove"&&(tt.is.ios===!0&&(t===!0?(window.visualViewport.removeEventListener("resize",Gi,Qe.passiveCapture),window.visualViewport.removeEventListener("scroll",Gi,Qe.passiveCapture)):window.removeEventListener("scroll",wa,Qe.passiveCapture)),e.classList.remove("q-body--prevent-scroll"),e.classList.remove("q-body--force-scrollbar-x"),e.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,e.style.left=Ia,e.style.top=Ca,window.location.href===Da&&window.scrollTo(en,tn),ui=void 0)}function Mg(s){let e="add";if(s===!0){if(oi++,Tt!==null){clearTimeout(Tt),Tt=null;return}if(oi>1)return}else{if(oi===0||(oi--,oi>0))return;if(e="remove",tt.is.ios===!0&&tt.is.nativeMobile===!0){Tt!==null&&clearTimeout(Tt),Tt=setTimeout(()=>{ka(e),Tt=null},100);return}}ka(e)}function Ng(){let s;return{preventBodyScroll(e){e!==s&&(s!==void 0||e===!0)&&(s=e,Mg(e))}}}let Vi=0;const Bg={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},_a={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]},Il=ke({name:"QDialog",inheritAttrs:!1,props:{...hl,...yl,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,allowFocusOutside:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,backdropFilter:String,position:{type:String,default:"standard",validator:s=>["standard","top","bottom","left","right"].includes(s)}},emits:[...gl,"shake","click","escapeKey"],setup(s,{slots:e,emit:t,attrs:i}){const n=Ee(),r=te(null),a=te(!1),o=te(!1);let l=null,u=null,c,d;const f=U(()=>s.persistent!==!0&&s.noRouteDismiss!==!0&&s.seamless!==!0),{preventBodyScroll:h}=Ng(),{registerTimeout:g}=xl(),{registerTick:p,removeTick:m}=Tl(),{transitionProps:y,transitionStyle:S}=Sl(s,()=>_a[s.position][0],()=>_a[s.position][1]),T=U(()=>S.value+(s.backdropFilter!==void 0?`;backdrop-filter:${s.backdropFilter};-webkit-backdrop-filter:${s.backdropFilter}`:"")),{showPortal:I,hidePortal:E,portalIsAccessible:P,renderPortal:R}=vl(n,r,ie,"dialog"),{hide:w}=ml({showing:a,hideOnRouteChange:f,handleShow:Y,handleHide:K,processOnMount:!0}),{addToHistory:b,removeFromHistory:x}=Pg(a,w,f),D=U(()=>`q-dialog__inner flex no-pointer-events q-dialog__inner--${s.maximized===!0?"maximized":"minimized"} q-dialog__inner--${s.position} ${Bg[s.position]}`+(o.value===!0?" q-dialog__inner--animating":"")+(s.fullWidth===!0?" q-dialog__inner--fullwidth":"")+(s.fullHeight===!0?" q-dialog__inner--fullheight":"")+(s.square===!0?" q-dialog__inner--square":"")),C=U(()=>a.value===!0&&s.seamless!==!0),M=U(()=>s.autoClose===!0?{onClick:L}:{}),N=U(()=>[`q-dialog fullscreen no-pointer-events q-dialog--${C.value===!0?"modal":"seamless"}`,i.class]);le(()=>s.maximized,H=>{a.value===!0&&k(H)}),le(C,H=>{h(H),H===!0?(Ll(ee),Al(B)):(bn(ee),ds(B))});function Y(H){b(),u=s.noRefocus===!1&&document.activeElement!==null?document.activeElement:null,k(s.maximized),I(),o.value=!0,s.noFocus!==!0?(document.activeElement?.blur(),p(z)):m(),g(()=>{if(n.proxy.$q.platform.is.ios===!0){if(s.seamless!==!0&&document.activeElement){const{top:j,bottom:ue}=document.activeElement.getBoundingClientRect(),{innerHeight:X}=window,ve=window.visualViewport!==void 0?window.visualViewport.height:X;j>0&&ue>ve/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-ve,ue>=X?1/0:Math.ceil(document.scrollingElement.scrollTop+ue-ve/2))),document.activeElement.scrollIntoView()}d=!0,r.value.click(),d=!1}I(!0),o.value=!1,t("show",H)},s.transitionDuration)}function K(H){m(),x(),$(!0),o.value=!0,E(),u!==null&&(((H?.type.indexOf("key")===0?u.closest('[tabindex]:not([tabindex^="-"])'):void 0)||u).focus(),u=null),g(()=>{E(!0),o.value=!1,t("hide",H)},s.transitionDuration)}function z(H){Ss(()=>{let j=r.value;if(j!==null){if(H!==void 0){const ue=j.querySelector(H);if(ue!==null){ue.focus({preventScroll:!0});return}}j.contains(document.activeElement)!==!0&&(j=j.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||j.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||j.querySelector("[autofocus], [data-autofocus]")||j,j.focus({preventScroll:!0}))}})}function V(H){H&&typeof H.focus=="function"?H.focus({preventScroll:!0}):z(),t("shake");const j=r.value;j!==null&&(j.classList.remove("q-animate--scale"),j.classList.add("q-animate--scale"),l!==null&&clearTimeout(l),l=setTimeout(()=>{l=null,r.value!==null&&(j.classList.remove("q-animate--scale"),z())},170))}function B(){s.seamless!==!0&&(s.persistent===!0||s.noEscDismiss===!0?s.maximized!==!0&&s.noShake!==!0&&V():(t("escapeKey"),w()))}function $(H){l!==null&&(clearTimeout(l),l=null),(H===!0||a.value===!0)&&(k(!1),s.seamless!==!0&&(h(!1),bn(ee),ds(B))),H!==!0&&(u=null)}function k(H){H===!0?c!==!0&&(Vi<1&&document.body.classList.add("q-body--dialog"),Vi++,c=!0):c===!0&&(Vi<2&&document.body.classList.remove("q-body--dialog"),Vi--,c=!1)}function L(H){d!==!0&&(w(H),t("click",H))}function q(H){s.persistent!==!0&&s.noBackdropDismiss!==!0?w(H):s.noShake!==!0&&V()}function ee(H){s.allowFocusOutside!==!0&&P.value===!0&&za(r.value,H.target)!==!0&&z('[tabindex]:not([tabindex="-1"])')}Object.assign(n.proxy,{focus:z,shake:V,__updateRefocusTarget(H){u=H||null}}),Be($);function ie(){return W("div",{role:"dialog","aria-modal":C.value===!0?"true":"false",...i,class:N.value},[W(Ji,{name:"q-transition--fade",appear:!0},()=>C.value===!0?W("div",{class:"q-dialog__backdrop fixed-full",style:T.value,"aria-hidden":"true",tabindex:-1,onClick:q}):null),W(Ji,y.value,()=>a.value===!0?W("div",{ref:r,class:D.value,style:S.value,tabindex:-1,...M.value},Fe(e.default)):null)])}return R}});let gs=!1;{const s=document.createElement("div");s.setAttribute("dir","rtl"),Object.assign(s.style,{width:"1px",height:"1px",overflow:"auto"});const e=document.createElement("div");Object.assign(e.style,{width:"1000px",height:"1px"}),document.body.appendChild(s),s.appendChild(e),s.scrollLeft=-1e3,gs=s.scrollLeft>=0,s.remove()}const qe=1e3,Ug=["start","center","end","start-force","center-force","end-force"],Cl=Array.prototype.filter,$g=window.getComputedStyle(document.body).overflowAnchor===void 0?su:function(s,e){s!==null&&(s._qOverflowAnimationFrame!==void 0&&cancelAnimationFrame(s._qOverflowAnimationFrame),s._qOverflowAnimationFrame=requestAnimationFrame(()=>{if(s===null)return;s._qOverflowAnimationFrame=void 0;const t=s.children||[];Cl.call(t,n=>n.dataset&&n.dataset.qVsAnchor!==void 0).forEach(n=>{delete n.dataset.qVsAnchor});const i=t[e];i?.dataset&&(i.dataset.qVsAnchor="")}))};function Qt(s,e){return s+e}function nn(s,e,t,i,n,r,a,o){const l=s===window?document.scrollingElement||document.documentElement:s,u=n===!0?"offsetWidth":"offsetHeight",c={scrollStart:0,scrollViewSize:-a-o,scrollMaxSize:0,offsetStart:-a,offsetEnd:-o};if(n===!0?(s===window?(c.scrollStart=window.pageXOffset||window.scrollX||document.body.scrollLeft||0,c.scrollViewSize+=document.documentElement.clientWidth):(c.scrollStart=l.scrollLeft,c.scrollViewSize+=l.clientWidth),c.scrollMaxSize=l.scrollWidth,r===!0&&(c.scrollStart=(gs===!0?c.scrollMaxSize-c.scrollViewSize:0)-c.scrollStart)):(s===window?(c.scrollStart=window.pageYOffset||window.scrollY||document.body.scrollTop||0,c.scrollViewSize+=document.documentElement.clientHeight):(c.scrollStart=l.scrollTop,c.scrollViewSize+=l.clientHeight),c.scrollMaxSize=l.scrollHeight),t!==null)for(let d=t.previousElementSibling;d!==null;d=d.previousElementSibling)d.classList.contains("q-virtual-scroll--skip")===!1&&(c.offsetStart+=d[u]);if(i!==null)for(let d=i.nextElementSibling;d!==null;d=d.nextElementSibling)d.classList.contains("q-virtual-scroll--skip")===!1&&(c.offsetEnd+=d[u]);if(e!==s){const d=l.getBoundingClientRect(),f=e.getBoundingClientRect();n===!0?(c.offsetStart+=f.left-d.left,c.offsetEnd-=f.width):(c.offsetStart+=f.top-d.top,c.offsetEnd-=f.height),s!==window&&(c.offsetStart+=c.scrollStart),c.offsetEnd+=c.scrollMaxSize-c.offsetStart}return c}function Pa(s,e,t,i){e==="end"&&(e=(s===window?document.body:s)[t===!0?"scrollWidth":"scrollHeight"]),s===window?t===!0?(i===!0&&(e=(gs===!0?document.body.scrollWidth-document.documentElement.clientWidth:0)-e),window.scrollTo(e,window.pageYOffset||window.scrollY||document.body.scrollTop||0)):window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,e):t===!0?(i===!0&&(e=(gs===!0?s.scrollWidth-s.offsetWidth:0)-e),s.scrollLeft=e):s.scrollTop=e}function li(s,e,t,i){if(t>=i)return 0;const n=e.length,r=Math.floor(t/qe),a=Math.floor((i-1)/qe)+1;let o=s.slice(r,a).reduce(Qt,0);return t%qe!==0&&(o-=e.slice(r*qe,t).reduce(Qt,0)),i%qe!==0&&i!==n&&(o-=e.slice(i,a*qe).reduce(Qt,0)),o}const Gg={virtualScrollSliceSize:{type:[Number,String],default:10},virtualScrollSliceRatioBefore:{type:[Number,String],default:1},virtualScrollSliceRatioAfter:{type:[Number,String],default:1},virtualScrollItemSize:{type:[Number,String],default:24},virtualScrollStickySizeStart:{type:[Number,String],default:0},virtualScrollStickySizeEnd:{type:[Number,String],default:0},tableColspan:[Number,String]},Fa={virtualScrollHorizontal:Boolean,onVirtualScroll:Function,...Gg};function Vg({virtualScrollLength:s,getVirtualScrollTarget:e,getVirtualScrollEl:t,virtualScrollItemSizeComputed:i}){const n=Ee(),{props:r,emit:a,proxy:o}=n,{$q:l}=o;let u,c,d,f=[],h;const g=te(0),p=te(0),m=te({}),y=te(null),S=te(null),T=te(null),I=te({from:0,to:0}),E=U(()=>r.tableColspan!==void 0?r.tableColspan:100);i===void 0&&(i=U(()=>r.virtualScrollItemSize));const P=U(()=>i.value+";"+r.virtualScrollHorizontal),R=U(()=>P.value+";"+r.virtualScrollSliceRatioBefore+";"+r.virtualScrollSliceRatioAfter);le(R,()=>{K()}),le(P,w);function w(){Y(c,!0)}function b(k){Y(k===void 0?c:k)}function x(k,L){const q=e();if(q==null||q.nodeType===8)return;const ee=nn(q,t(),y.value,S.value,r.virtualScrollHorizontal,l.lang.rtl,r.virtualScrollStickySizeStart,r.virtualScrollStickySizeEnd);d!==ee.scrollViewSize&&K(ee.scrollViewSize),C(q,ee,Math.min(s.value-1,Math.max(0,parseInt(k,10)||0)),0,Ug.indexOf(L)!==-1?L:c!==-1&&k>c?"end":"start")}function D(){const k=e();if(k==null||k.nodeType===8)return;const L=nn(k,t(),y.value,S.value,r.virtualScrollHorizontal,l.lang.rtl,r.virtualScrollStickySizeStart,r.virtualScrollStickySizeEnd),q=s.value-1,ee=L.scrollMaxSize-L.offsetStart-L.offsetEnd-p.value;if(u===L.scrollStart)return;if(L.scrollMaxSize<=0){C(k,L,0,0);return}d!==L.scrollViewSize&&K(L.scrollViewSize),M(I.value.from);const ie=Math.floor(L.scrollMaxSize-Math.max(L.scrollViewSize,L.offsetEnd)-Math.min(h[q],L.scrollViewSize/2));if(ie>0&&Math.ceil(L.scrollStart)>=ie){C(k,L,q,L.scrollMaxSize-L.offsetEnd-f.reduce(Qt,0));return}let H=0,j=L.scrollStart-L.offsetStart,ue=j;if(j<=ee&&j+L.scrollViewSize>=g.value)j-=g.value,H=I.value.from,ue=j;else for(let X=0;j>=f[X]&&H<q;X++)j-=f[X],H+=qe;for(;j>0&&H<q;)j-=h[H],j>-L.scrollViewSize?(H++,ue=j):ue=h[H]+j;C(k,L,H,ue)}function C(k,L,q,ee,ie){const H=typeof ie=="string"&&ie.indexOf("-force")!==-1,j=H===!0?ie.replace("-force",""):ie,ue=j!==void 0?j:"start";let X=Math.max(0,q-m.value[ue]),ve=X+m.value.total;ve>s.value&&(ve=s.value,X=Math.max(0,ve-m.value.total)),u=L.scrollStart;const Ye=X!==I.value.from||ve!==I.value.to;if(Ye===!1&&j===void 0){V(q);return}const{activeElement:Ot}=document,at=T.value;Ye===!0&&at!==null&&at!==Ot&&at.contains(Ot)===!0&&(at.addEventListener("focusout",N),setTimeout(()=>{at?.removeEventListener("focusout",N)})),$g(at,q-X);const Si=j!==void 0?h.slice(X,q).reduce(Qt,0):0;if(Ye===!0){const mt=ve>=I.value.from&&X<=I.value.to?I.value.to:ve;I.value={from:X,to:mt},g.value=li(f,h,0,X),p.value=li(f,h,ve,s.value),requestAnimationFrame(()=>{I.value.to!==ve&&u===L.scrollStart&&(I.value={from:I.value.from,to:ve},p.value=li(f,h,ve,s.value))})}requestAnimationFrame(()=>{if(u!==L.scrollStart)return;Ye===!0&&M(X);const mt=h.slice(X,q).reduce(Qt,0),pt=mt+L.offsetStart+g.value,Ti=pt+h[q];let ii=pt+ee;if(j!==void 0){const xs=mt-Si,si=L.scrollStart+xs;ii=H!==!0&&si<pt&&Ti<si+L.scrollViewSize?si:j==="end"?Ti-L.scrollViewSize:pt-(j==="start"?0:Math.round((L.scrollViewSize-h[q])/2))}u=ii,Pa(k,ii,r.virtualScrollHorizontal,l.lang.rtl),V(q)})}function M(k){const L=T.value;if(L){const q=Cl.call(L.children,X=>X.classList&&X.classList.contains("q-virtual-scroll--skip")===!1),ee=q.length,ie=r.virtualScrollHorizontal===!0?X=>X.getBoundingClientRect().width:X=>X.offsetHeight;let H=k,j,ue;for(let X=0;X<ee;){for(j=ie(q[X]),X++;X<ee&&q[X].classList.contains("q-virtual-scroll--with-prev")===!0;)j+=ie(q[X]),X++;ue=j-h[H],ue!==0&&(h[H]+=ue,f[Math.floor(H/qe)]+=ue),H++}}}function N(){T.value?.focus()}function Y(k,L){const q=1*i.value;(L===!0||Array.isArray(h)===!1)&&(h=[]);const ee=h.length;h.length=s.value;for(let H=s.value-1;H>=ee;H--)h[H]=q;const ie=Math.floor((s.value-1)/qe);f=[];for(let H=0;H<=ie;H++){let j=0;const ue=Math.min((H+1)*qe,s.value);for(let X=H*qe;X<ue;X++)j+=h[X];f.push(j)}c=-1,u=void 0,g.value=li(f,h,0,I.value.from),p.value=li(f,h,I.value.to,s.value),k>=0?(M(I.value.from),Se(()=>{x(k)})):B()}function K(k){if(k===void 0&&typeof window<"u"){const j=e();j!=null&&j.nodeType!==8&&(k=nn(j,t(),y.value,S.value,r.virtualScrollHorizontal,l.lang.rtl,r.virtualScrollStickySizeStart,r.virtualScrollStickySizeEnd).scrollViewSize)}d=k;const L=parseFloat(r.virtualScrollSliceRatioBefore)||0,q=parseFloat(r.virtualScrollSliceRatioAfter)||0,ee=1+L+q,ie=k===void 0||k<=0?1:Math.ceil(k/i.value),H=Math.max(1,ie,Math.ceil((r.virtualScrollSliceSize>0?r.virtualScrollSliceSize:10)/ee));m.value={total:Math.ceil(H*ee),start:Math.ceil(H*L),center:Math.ceil(H*(.5+L)),end:Math.ceil(H*(1+L)),view:ie}}function z(k,L){const q=r.virtualScrollHorizontal===!0?"width":"height",ee={["--q-virtual-scroll-item-"+q]:i.value+"px"};return[k==="tbody"?W(k,{class:"q-virtual-scroll__padding",key:"before",ref:y},[W("tr",[W("td",{style:{[q]:`${g.value}px`,...ee},colspan:E.value})])]):W(k,{class:"q-virtual-scroll__padding",key:"before",ref:y,style:{[q]:`${g.value}px`,...ee}}),W(k,{class:"q-virtual-scroll__content",key:"content",ref:T,tabindex:-1},L.flat()),k==="tbody"?W(k,{class:"q-virtual-scroll__padding",key:"after",ref:S},[W("tr",[W("td",{style:{[q]:`${p.value}px`,...ee},colspan:E.value})])]):W(k,{class:"q-virtual-scroll__padding",key:"after",ref:S,style:{[q]:`${p.value}px`,...ee}})]}function V(k){c!==k&&(r.onVirtualScroll!==void 0&&a("virtualScroll",{index:k,from:I.value.from,to:I.value.to-1,direction:k<c?"decrease":"increase",ref:o}),c=k)}K();const B=Va(D,l.platform.is.ios===!0?120:35);nu(()=>{K()});let $=!1;return ps(()=>{$=!0}),Ka(()=>{if($!==!0)return;const k=e();u!==void 0&&k!==void 0&&k!==null&&k.nodeType!==8?Pa(k,u,r.virtualScrollHorizontal,l.lang.rtl):x(c)}),Be(()=>{B.cancel()}),Object.assign(o,{scrollTo:x,reset:w,refresh:b}),{virtualScrollSliceRange:I,virtualScrollSliceSizeComputed:m,setVirtualScrollSize:K,onVirtualScrollEvt:B,localResetVirtualScroll:Y,padVirtualScroll:z,scrollTo:x,reset:w,refresh:b}}const zn={name:String};function Kg(s={}){return(e,t,i)=>{e[t](W("input",{class:"hidden"+(i||""),...s.value}))}}function Dl(s){return U(()=>s.name||s.for)}const Hg=/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/,qg=/[\u4e00-\u9fff\u3400-\u4dbf\u{20000}-\u{2a6df}\u{2a700}-\u{2b73f}\u{2b740}-\u{2b81f}\u{2b820}-\u{2ceaf}\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u{2f800}-\u{2fa1f}]/u,Wg=/[\u3131-\u314e\u314f-\u3163\uac00-\ud7a3]/,Yg=/[a-z0-9_ -]$/i;function wl(s){return function(t){if(t.type==="compositionend"||t.type==="change"){if(t.target.qComposing!==!0)return;t.target.qComposing=!1,s(t)}else t.type==="compositionupdate"&&t.target.qComposing!==!0&&typeof t.data=="string"&&(tt.is.firefox===!0?Yg.test(t.data)===!1:Hg.test(t.data)===!0||qg.test(t.data)===!0||Wg.test(t.data)===!0)===!0&&(t.target.qComposing=!0)}}function Oa(s,e,t){if(t<=e)return e;const i=t-e+1;let n=e+(s-e)%i;return n<e&&(n=i+n),n===0?0:n}const Ma=s=>["add","add-unique","toggle"].includes(s),zg=".*+?^${}()|[]\\",jg=Object.keys(Ts);function rn(s,e){if(typeof s=="function")return s;const t=s!==void 0?s:e;return i=>i!==null&&typeof i=="object"&&t in i?i[t]:i}const Na=ke({name:"QSelect",inheritAttrs:!1,props:{...Fa,...zn,...Ts,modelValue:{required:!0},multiple:Boolean,displayValue:[String,Number],displayValueHtml:Boolean,dropdownIcon:String,options:{type:Array,default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],hideSelected:Boolean,hideDropdownIcon:Boolean,fillInput:Boolean,maxValues:[Number,String],optionsDense:Boolean,optionsDark:{type:Boolean,default:null},optionsSelectedClass:String,optionsHtml:Boolean,optionsCover:Boolean,menuShrink:Boolean,menuAnchor:String,menuSelf:String,menuOffset:Array,popupContentClass:String,popupContentStyle:[String,Array,Object],popupNoRouteDismiss:Boolean,useInput:Boolean,useChips:Boolean,newValueMode:{type:String,validator:Ma},mapOptions:Boolean,emitValue:Boolean,disableTabSelection:Boolean,inputDebounce:{type:[Number,String],default:500},inputClass:[Array,String,Object],inputStyle:[Array,String,Object],tabindex:{type:[String,Number],default:0},autocomplete:String,transitionShow:{},transitionHide:{},transitionDuration:{},behavior:{type:String,validator:s=>["default","menu","dialog"].includes(s),default:"default"},virtualScrollItemSize:Fa.virtualScrollItemSize.type,onNewValue:Function,onFilter:Function},emits:[...qn,"add","remove","inputValue","keyup","keypress","keydown","popupShow","popupHide","filterAbort"],setup(s,{slots:e,emit:t}){const{proxy:i}=Ee(),{$q:n}=i,r=te(!1),a=te(!1),o=te(-1),l=te(""),u=te(!1),c=te(!1);let d=null,f=null,h,g,p,m=null,y,S,T,I;const E=te(null),P=te(null),R=te(null),w=te(null),b=te(null),x=Dl(s),D=wl(nr),C=U(()=>Array.isArray(s.options)?s.options.length:0),M=U(()=>s.virtualScrollItemSize===void 0?s.optionsDense===!0?24:48:s.virtualScrollItemSize),{virtualScrollSliceRange:N,virtualScrollSliceSizeComputed:Y,localResetVirtualScroll:K,padVirtualScroll:z,onVirtualScrollEvt:V,scrollTo:B,setVirtualScrollSize:$}=Vg({virtualScrollLength:C,getVirtualScrollTarget:Ol,getVirtualScrollEl:ir,virtualScrollItemSizeComputed:M}),k=Wn(),L=U(()=>{const A=s.mapOptions===!0&&s.multiple!==!0,Q=s.modelValue!==void 0&&(s.modelValue!==null||A===!0)?s.multiple===!0&&Array.isArray(s.modelValue)?s.modelValue:[s.modelValue]:[];if(s.mapOptions===!0&&Array.isArray(s.options)===!0){const Z=s.mapOptions===!0&&h!==void 0?h:[],oe=Q.map(ge=>Fl(ge,Z));return s.modelValue===null&&A===!0?oe.filter(ge=>ge!==null):oe}return Q}),q=U(()=>{const A={};return jg.forEach(Q=>{const Z=s[Q];Z!==void 0&&(A[Q]=Z)}),A}),ee=U(()=>s.optionsDark===null?k.isDark.value:s.optionsDark),ie=U(()=>vi(L.value)),H=U(()=>{let A="q-field__input q-placeholder col";return s.hideSelected===!0||L.value.length===0?[A,s.inputClass]:(A+=" q-field__input--padding",s.inputClass===void 0?A:[A,s.inputClass])}),j=U(()=>(s.virtualScrollHorizontal===!0?"q-virtual-scroll--horizontal":"")+(s.popupContentClass?" "+s.popupContentClass:"")),ue=U(()=>C.value===0),X=U(()=>L.value.map(A=>Oe.value(A)).join(", ")),ve=U(()=>s.displayValue!==void 0?s.displayValue:X.value),Ye=U(()=>s.optionsHtml===!0?()=>!0:A=>A?.html===!0),Ot=U(()=>s.displayValueHtml===!0||s.displayValue===void 0&&(s.optionsHtml===!0||L.value.some(Ye.value))),at=U(()=>k.focused.value===!0?s.tabindex:-1),Si=U(()=>{const A={tabindex:s.tabindex,role:"combobox","aria-label":s.label,"aria-readonly":s.readonly===!0?"true":"false","aria-autocomplete":s.useInput===!0?"list":"none","aria-expanded":r.value===!0?"true":"false","aria-controls":`${k.targetUid.value}_lb`};return o.value>=0&&(A["aria-activedescendant"]=`${k.targetUid.value}_${o.value}`),A}),mt=U(()=>({id:`${k.targetUid.value}_lb`,role:"listbox","aria-multiselectable":s.multiple===!0?"true":"false"})),pt=U(()=>L.value.map((A,Q)=>({index:Q,opt:A,html:Ye.value(A),selected:!0,removeAtIndex:Pl,toggleOption:vt,tabindex:at.value}))),Ti=U(()=>{if(C.value===0)return[];const{from:A,to:Q}=N.value;return s.options.slice(A,Q).map((Z,oe)=>{const ge=Mt.value(Z)===!0,he=As(Z)===!0,Ie=A+oe,ye={clickable:!0,active:he,activeClass:si.value,manualFocus:!0,focused:!1,disable:ge,tabindex:-1,dense:s.optionsDense,dark:ee.value,role:"option","aria-selected":he===!0?"true":"false",id:`${k.targetUid.value}_${Ie}`,onClick:()=>{vt(Z)}};return ge!==!0&&(o.value===Ie&&(ye.focused=!0),n.platform.is.desktop===!0&&(ye.onMousemove=()=>{r.value===!0&&Nt(Ie)})),{index:Ie,opt:Z,html:Ye.value(Z),label:Oe.value(Z),selected:ye.active,focused:ye.focused,toggleOption:vt,setOptionIndex:Nt,itemProps:ye}})}),ii=U(()=>s.dropdownIcon!==void 0?s.dropdownIcon:n.iconSet.arrow.dropdown),xs=U(()=>s.optionsCover===!1&&s.outlined!==!0&&s.standout!==!0&&s.borderless!==!0&&s.rounded!==!0),si=U(()=>s.optionsSelectedClass!==void 0?s.optionsSelectedClass:s.color!==void 0?`text-${s.color}`:""),ze=U(()=>rn(s.optionValue,"value")),Oe=U(()=>rn(s.optionLabel,"label")),Mt=U(()=>rn(s.optionDisable,"disable")),xi=U(()=>L.value.map(ze.value)),_l=U(()=>{const A={onInput:nr,onChange:D,onKeydown:tr,onKeyup:Jn,onKeypress:er,onFocus:Qn,onClick(Q){g===!0&&ft(Q)}};return A.onCompositionstart=A.onCompositionupdate=A.onCompositionend=D,A});le(L,A=>{h=A,s.useInput===!0&&s.fillInput===!0&&s.multiple!==!0&&k.innerLoading.value!==!0&&(a.value!==!0&&r.value!==!0||ie.value!==!0)&&(p!==!0&&Gt(),(a.value===!0||r.value===!0)&&Bt(""))},{immediate:!0}),le(()=>s.fillInput,Gt),le(r,bs),le(C,Yl);function jn(A){return s.emitValue===!0?ze.value(A):A}function Es(A){if(A!==-1&&A<L.value.length)if(s.multiple===!0){const Q=s.modelValue.slice();t("remove",{index:A,value:Q.splice(A,1)[0]}),t("update:modelValue",Q)}else t("update:modelValue",null)}function Pl(A){Es(A),k.focus()}function Xn(A,Q){const Z=jn(A);if(s.multiple!==!0){s.fillInput===!0&&ni(Oe.value(A),!0,!0),t("update:modelValue",Z);return}if(L.value.length===0){t("add",{index:0,value:Z}),t("update:modelValue",s.multiple===!0?[Z]:Z);return}if(Q===!0&&As(A)===!0||s.maxValues!==void 0&&s.modelValue.length>=s.maxValues)return;const oe=s.modelValue.slice();t("add",{index:oe.length,value:Z}),oe.push(Z),t("update:modelValue",oe)}function vt(A,Q){if(k.editable.value!==!0||A===void 0||Mt.value(A)===!0)return;const Z=ze.value(A);if(s.multiple!==!0){Q!==!0&&(ni(s.fillInput===!0?Oe.value(A):"",!0,!0),yt()),P.value?.focus(),(L.value.length===0||ri(ze.value(L.value[0]),Z)!==!0)&&t("update:modelValue",s.emitValue===!0?Z:A);return}if((g!==!0||u.value===!0)&&k.focus(),Qn(),L.value.length===0){const he=s.emitValue===!0?Z:A;t("add",{index:0,value:he}),t("update:modelValue",s.multiple===!0?[he]:he);return}const oe=s.modelValue.slice(),ge=xi.value.findIndex(he=>ri(he,Z));if(ge!==-1)t("remove",{index:ge,value:oe.splice(ge,1)[0]});else{if(s.maxValues!==void 0&&oe.length>=s.maxValues)return;const he=s.emitValue===!0?Z:A;t("add",{index:oe.length,value:he}),oe.push(he)}t("update:modelValue",oe)}function Nt(A){if(n.platform.is.desktop!==!0)return;const Q=A!==-1&&A<C.value?A:-1;o.value!==Q&&(o.value=Q)}function Ei(A=1,Q){if(r.value===!0){let Z=o.value;do Z=Oa(Z+A,-1,C.value-1);while(Z!==-1&&Z!==o.value&&Mt.value(s.options[Z])===!0);o.value!==Z&&(Nt(Z),B(Z),Q!==!0&&s.useInput===!0&&s.fillInput===!0&&Ai(Z>=0?Oe.value(s.options[Z]):y,!0))}}function Fl(A,Q){const Z=oe=>ri(ze.value(oe),A);return s.options.find(Z)||Q.find(Z)||A}function As(A){const Q=ze.value(A);return xi.value.find(Z=>ri(Z,Q))!==void 0}function Qn(A){s.useInput===!0&&P.value!==null&&(A===void 0||P.value===A.target&&A.target.value===X.value)&&P.value.select()}function Zn(A){yi(A,27)===!0&&r.value===!0&&(ft(A),yt(),Gt()),t("keyup",A)}function Jn(A){const{value:Q}=A.target;if(A.keyCode!==void 0){Zn(A);return}if(A.target.value="",d!==null&&(clearTimeout(d),d=null),f!==null&&(clearTimeout(f),f=null),Gt(),typeof Q=="string"&&Q.length!==0){const Z=Q.toLocaleLowerCase(),oe=he=>{const Ie=s.options.find(ye=>String(he.value(ye)).toLocaleLowerCase()===Z);return Ie===void 0?!1:(L.value.indexOf(Ie)===-1?vt(Ie):yt(),!0)},ge=he=>{oe(ze)!==!0&&he!==!0&&oe(Oe)!==!0&&Bt(Q,!0,()=>ge(!0))};ge()}else k.clearValue(A)}function er(A){t("keypress",A)}function tr(A){if(t("keydown",A),qa(A)===!0)return;const Q=l.value.length!==0&&(s.newValueMode!==void 0||s.onNewValue!==void 0),Z=A.shiftKey!==!0&&s.disableTabSelection!==!0&&s.multiple!==!0&&(o.value!==-1||Q===!0);if(A.keyCode===27){et(A);return}if(A.keyCode===9&&Z===!1){Ut();return}if(A.target===void 0||A.target.id!==k.targetUid.value||k.editable.value!==!0)return;if(A.keyCode===40&&k.innerLoading.value!==!0&&r.value===!1){Ne(A),$t();return}if(A.keyCode===8&&(s.useChips===!0||s.clearable===!0)&&s.hideSelected!==!0&&l.value.length===0){s.multiple===!0&&Array.isArray(s.modelValue)===!0?Es(s.modelValue.length-1):s.multiple!==!0&&s.modelValue!==null&&t("update:modelValue",null);return}(A.keyCode===35||A.keyCode===36)&&(typeof l.value!="string"||l.value.length===0)&&(Ne(A),o.value=-1,Ei(A.keyCode===36?1:-1,s.multiple)),(A.keyCode===33||A.keyCode===34)&&Y.value!==void 0&&(Ne(A),o.value=Math.max(-1,Math.min(C.value,o.value+(A.keyCode===33?-1:1)*Y.value.view)),Ei(A.keyCode===33?1:-1,s.multiple)),(A.keyCode===38||A.keyCode===40)&&(Ne(A),Ei(A.keyCode===38?-1:1,s.multiple));const oe=C.value;if((T===void 0||I<Date.now())&&(T=""),oe>0&&s.useInput!==!0&&A.key!==void 0&&A.key.length===1&&A.altKey===!1&&A.ctrlKey===!1&&A.metaKey===!1&&(A.keyCode!==32||T.length!==0)){r.value!==!0&&$t(A);const ge=A.key.toLocaleLowerCase(),he=T.length===1&&T[0]===ge;I=Date.now()+1500,he===!1&&(Ne(A),T+=ge);const Ie=new RegExp("^"+T.split("").map(Ls=>zg.indexOf(Ls)!==-1?"\\"+Ls:Ls).join(".*"),"i");let ye=o.value;if(he===!0||ye<0||Ie.test(Oe.value(s.options[ye]))!==!0)do ye=Oa(ye+1,-1,oe-1);while(ye!==o.value&&(Mt.value(s.options[ye])===!0||Ie.test(Oe.value(s.options[ye]))!==!0));o.value!==ye&&Se(()=>{Nt(ye),B(ye),ye>=0&&s.useInput===!0&&s.fillInput===!0&&Ai(Oe.value(s.options[ye]),!0)});return}if(!(A.keyCode!==13&&(A.keyCode!==32||s.useInput===!0||T!=="")&&(A.keyCode!==9||Z===!1))){if(A.keyCode!==9&&Ne(A),o.value!==-1&&o.value<oe){vt(s.options[o.value]);return}if(Q===!0){const ge=(he,Ie)=>{if(Ie){if(Ma(Ie)!==!0)return}else Ie=s.newValueMode;if(ni("",s.multiple!==!0,!0),he==null)return;(Ie==="toggle"?vt:Xn)(he,Ie==="add-unique"),s.multiple!==!0&&(P.value?.focus(),yt())};if(s.onNewValue!==void 0?t("newValue",l.value,ge):ge(l.value),s.multiple!==!0)return}r.value===!0?Ut():k.innerLoading.value!==!0&&$t()}}function ir(){return g===!0?b.value:R.value!==null&&R.value.contentEl!==null?R.value.contentEl:void 0}function Ol(){return ir()}function Ml(){return s.hideSelected===!0?[]:e["selected-item"]!==void 0?pt.value.map(A=>e["selected-item"](A)).slice():e.selected!==void 0?[].concat(e.selected()):s.useChips===!0?pt.value.map((A,Q)=>W(ug,{key:"option-"+Q,removable:k.editable.value===!0&&Mt.value(A.opt)!==!0,dense:!0,textColor:s.color,tabindex:at.value,onRemove(){A.removeAtIndex(Q)}},()=>W("span",{class:"ellipsis",[A.html===!0?"innerHTML":"textContent"]:Oe.value(A.opt)}))):[W("span",{class:"ellipsis",[Ot.value===!0?"innerHTML":"textContent"]:ve.value})]}function sr(){if(ue.value===!0)return e["no-option"]!==void 0?e["no-option"]({inputValue:l.value}):void 0;const A=e.option!==void 0?e.option:Z=>W(dl,{key:Z.index,...Z.itemProps},()=>W(fl,()=>W(cg,()=>W("span",{[Z.html===!0?"innerHTML":"textContent"]:Z.label}))));let Q=z("div",Ti.value.map(A));return e["before-options"]!==void 0&&(Q=e["before-options"]().concat(Q)),ja(e["after-options"],Q)}function Nl(A,Q){const Z=Q===!0?{...Si.value,...k.splitAttrs.attributes.value}:void 0,oe={ref:Q===!0?P:void 0,key:"i_t",class:H.value,style:s.inputStyle,value:l.value!==void 0?l.value:"",type:"search",...Z,id:Q===!0?k.targetUid.value:void 0,maxlength:s.maxlength,autocomplete:s.autocomplete,"data-autofocus":A===!0||s.autofocus===!0||void 0,disabled:s.disable===!0,readonly:s.readonly===!0,..._l.value};return A!==!0&&g===!0&&(Array.isArray(oe.class)===!0?oe.class=[...oe.class,"no-pointer-events"]:oe.class+=" no-pointer-events"),W("input",oe)}function nr(A){d!==null&&(clearTimeout(d),d=null),f!==null&&(clearTimeout(f),f=null),!(A&&A.target&&A.target.qComposing===!0)&&(Ai(A.target.value||""),p=!0,y=l.value,k.focused.value!==!0&&(g!==!0||u.value===!0)&&k.focus(),s.onFilter!==void 0&&(d=setTimeout(()=>{d=null,Bt(l.value)},s.inputDebounce)))}function Ai(A,Q){l.value!==A&&(l.value=A,Q===!0||s.inputDebounce===0||s.inputDebounce==="0"?t("inputValue",A):f=setTimeout(()=>{f=null,t("inputValue",A)},s.inputDebounce))}function ni(A,Q,Z){p=Z!==!0,s.useInput===!0&&(Ai(A,!0),(Q===!0||Z!==!0)&&(y=A),Q!==!0&&Bt(A))}function Bt(A,Q,Z){if(s.onFilter===void 0||Q!==!0&&k.focused.value!==!0)return;k.innerLoading.value===!0?t("filterAbort"):(k.innerLoading.value=!0,c.value=!0),A!==""&&s.multiple!==!0&&L.value.length!==0&&p!==!0&&A===Oe.value(L.value[0])&&(A="");const oe=setTimeout(()=>{r.value===!0&&(r.value=!1)},10);m!==null&&clearTimeout(m),m=oe,t("filter",A,(ge,he)=>{(Q===!0||k.focused.value===!0)&&m===oe&&(clearTimeout(m),typeof ge=="function"&&ge(),c.value=!1,Se(()=>{k.innerLoading.value=!1,k.editable.value===!0&&(Q===!0?r.value===!0&&yt():r.value===!0?bs(!0):r.value=!0),typeof he=="function"&&Se(()=>{he(i)}),typeof Z=="function"&&Se(()=>{Z(i)})}))},()=>{k.focused.value===!0&&m===oe&&(clearTimeout(m),k.innerLoading.value=!1,c.value=!1),r.value===!0&&(r.value=!1)})}function Bl(){return W(_g,{ref:R,class:j.value,style:s.popupContentStyle,modelValue:r.value,fit:s.menuShrink!==!0,cover:s.optionsCover===!0&&ue.value!==!0&&s.useInput!==!0,anchor:s.menuAnchor,self:s.menuSelf,offset:s.menuOffset,dark:ee.value,noParentEvent:!0,noRefocus:!0,noFocus:!0,noRouteDismiss:s.popupNoRouteDismiss,square:xs.value,transitionShow:s.transitionShow,transitionHide:s.transitionHide,transitionDuration:s.transitionDuration,separateClosePopup:!0,...mt.value,onScrollPassive:V,onBeforeShow:ar,onBeforeHide:Ul,onShow:$l},sr)}function Ul(A){or(A),Ut()}function $l(){$()}function Gl(A){ft(A),P.value?.focus(),u.value=!0,window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,0)}function Vl(A){ft(A),Se(()=>{u.value=!1})}function Kl(){const A=[W(og,{class:`col-auto ${k.fieldClass.value}`,...q.value,for:k.targetUid.value,dark:ee.value,square:!0,loading:c.value,itemAligned:!1,filled:!0,stackLabel:l.value.length!==0,...k.splitAttrs.listeners.value,onFocus:Gl,onBlur:Vl},{...e,rawControl:()=>k.getControl(!0),before:void 0,after:void 0})];return r.value===!0&&A.push(W("div",{ref:b,class:j.value+" scroll",style:s.popupContentStyle,...mt.value,onClick:et,onScrollPassive:V},sr())),W(Il,{ref:w,modelValue:a.value,position:s.useInput===!0?"top":void 0,transitionShow:S,transitionHide:s.transitionHide,transitionDuration:s.transitionDuration,noRouteDismiss:s.popupNoRouteDismiss,onBeforeShow:ar,onBeforeHide:Hl,onHide:ql,onShow:Wl},()=>W("div",{class:"q-select__dialog"+(ee.value===!0?" q-select__dialog--dark q-dark":"")+(u.value===!0?" q-select__dialog--focused":"")},A))}function Hl(A){or(A),w.value!==null&&w.value.__updateRefocusTarget(k.rootRef.value.querySelector(".q-field__native > [tabindex]:last-child")),k.focused.value=!1}function ql(A){yt(),k.focused.value===!1&&t("blur",A),Gt()}function Wl(){const A=document.activeElement;(A===null||A.id!==k.targetUid.value)&&P.value!==null&&P.value!==A&&P.value.focus(),$()}function Ut(){a.value!==!0&&(o.value=-1,r.value===!0&&(r.value=!1),k.focused.value===!1&&(m!==null&&(clearTimeout(m),m=null),k.innerLoading.value===!0&&(t("filterAbort"),k.innerLoading.value=!1,c.value=!1)))}function $t(A){k.editable.value===!0&&(g===!0?(k.onControlFocusin(A),a.value=!0,Se(()=>{k.focus()})):k.focus(),s.onFilter!==void 0?Bt(l.value):(ue.value!==!0||e["no-option"]!==void 0)&&(r.value=!0))}function yt(){a.value=!1,Ut()}function Gt(){s.useInput===!0&&ni(s.multiple!==!0&&s.fillInput===!0&&L.value.length!==0&&Oe.value(L.value[0])||"",!0,!0)}function bs(A){let Q=-1;if(A===!0){if(L.value.length!==0){const Z=ze.value(L.value[0]);Q=s.options.findIndex(oe=>ri(ze.value(oe),Z))}K(Q)}Nt(Q)}function Yl(A,Q){r.value===!0&&k.innerLoading.value===!1&&(K(-1,!0),Se(()=>{r.value===!0&&k.innerLoading.value===!1&&(A>Q?K():bs(!0))}))}function rr(){a.value===!1&&R.value!==null&&R.value.updatePosition()}function ar(A){A!==void 0&&ft(A),t("popupShow",A),k.hasPopupOpen=!0,k.onControlFocusin(A)}function or(A){A!==void 0&&ft(A),t("popupHide",A),k.hasPopupOpen=!1,k.onControlFocusout(A)}function lr(){g=n.platform.is.mobile!==!0&&s.behavior!=="dialog"?!1:s.behavior!=="menu"&&(s.useInput===!0?e["no-option"]!==void 0||s.onFilter!==void 0||ue.value===!1:!0),S=n.platform.is.ios===!0&&g===!0&&s.useInput===!0?"fade":s.transitionShow}return Ga(lr),ru(rr),lr(),Be(()=>{d!==null&&clearTimeout(d),f!==null&&clearTimeout(f)}),Object.assign(i,{showPopup:$t,hidePopup:yt,removeAtIndex:Es,add:Xn,toggleOption:vt,getOptionIndex:()=>o.value,setOptionIndex:Nt,moveOptionSelection:Ei,filter:Bt,updateMenuPosition:rr,updateInputValue:ni,isOptionSelected:As,getEmittingOptionValue:jn,isOptionDisabled:(...A)=>Mt.value.apply(null,A)===!0,getOptionValue:(...A)=>ze.value.apply(null,A),getOptionLabel:(...A)=>Oe.value.apply(null,A)}),Object.assign(k,{innerValue:L,fieldClass:U(()=>`q-select q-field--auto-height q-select--with${s.useInput!==!0?"out":""}-input q-select--with${s.useChips!==!0?"out":""}-chips q-select--${s.multiple===!0?"multiple":"single"}`),inputRef:E,targetRef:P,hasValue:ie,showPopup:$t,floatingLabel:U(()=>s.hideSelected!==!0&&ie.value===!0||typeof l.value=="number"||l.value.length!==0||vi(s.displayValue)),getControlChild:()=>{if(k.editable.value!==!1&&(a.value===!0||ue.value!==!0||e["no-option"]!==void 0))return g===!0?Kl():Bl();k.hasPopupOpen===!0&&(k.hasPopupOpen=!1)},controlEvents:{onFocusin(A){k.onControlFocusin(A)},onFocusout(A){k.onControlFocusout(A,()=>{Gt(),Ut()})},onClick(A){if(et(A),g!==!0&&r.value===!0){Ut(),P.value?.focus();return}$t(A)}},getControl:A=>{const Q=Ml(),Z=A===!0||a.value!==!0||g!==!0;if(s.useInput===!0)Q.push(Nl(A,Z));else if(k.editable.value===!0){const ge=Z===!0?Si.value:void 0;Q.push(W("input",{ref:Z===!0?P:void 0,key:"d_t",class:"q-select__focus-target",id:Z===!0?k.targetUid.value:void 0,value:ve.value,readonly:!0,"data-autofocus":A===!0||s.autofocus===!0||void 0,...ge,onKeydown:tr,onKeyup:Zn,onKeypress:er})),Z===!0&&typeof s.autocomplete=="string"&&s.autocomplete.length!==0&&Q.push(W("input",{class:"q-select__autocomplete-input",autocomplete:s.autocomplete,tabindex:-1,onKeyup:Jn}))}if(x.value!==void 0&&s.disable!==!0&&xi.value.length!==0){const ge=xi.value.map(he=>W("option",{value:he,selected:!0}));Q.push(W("select",{class:"hidden",name:x.value,multiple:s.multiple},ge))}const oe=s.useInput===!0||Z!==!0?void 0:k.splitAttrs.attributes.value;return W("div",{class:"q-field__native row items-center",...oe,...k.splitAttrs.listeners.value},Q)},getInnerAppend:()=>s.loading!==!0&&c.value!==!0&&s.hideDropdownIcon!==!0?[W(Rt,{class:"q-select__dropdown-icon"+(r.value===!0?" rotate-180":""),name:ii.value})]:null}),Yn(k)}}),Ba={date:"####/##/##",datetime:"####/##/## ##:##",time:"##:##",fulltime:"##:##:##",phone:"(###) ### - ####",card:"#### #### #### ####"},ms={"#":{pattern:"[\\d]",negate:"[^\\d]"},S:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]"},N:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]"},A:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:s=>s.toLocaleUpperCase()},a:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:s=>s.toLocaleLowerCase()},X:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:s=>s.toLocaleUpperCase()},x:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:s=>s.toLocaleLowerCase()}},kl=Object.keys(ms);kl.forEach(s=>{ms[s].regex=new RegExp(ms[s].pattern)});const Xg=new RegExp("\\\\([^.*+?^${}()|([\\]])|([.*+?^${}()|[\\]])|(["+kl.join("")+"])|(.)","g"),Ua=/[.*+?^${}()|[\]\\]/g,Te="",Qg={mask:String,reverseFillMask:Boolean,fillMask:[Boolean,String],unmaskedValue:Boolean};function Zg(s,e,t,i){let n,r,a,o,l,u;const c=te(null),d=te(h());function f(){return s.autogrow===!0||["textarea","text","search","url","tel","password"].includes(s.type)}le(()=>s.type+s.autogrow,p),le(()=>s.mask,b=>{if(b!==void 0)m(d.value,!0);else{const x=R(d.value);p(),s.modelValue!==x&&e("update:modelValue",x)}}),le(()=>s.fillMask+s.reverseFillMask,()=>{c.value===!0&&m(d.value,!0)}),le(()=>s.unmaskedValue,()=>{c.value===!0&&m(d.value)});function h(){if(p(),c.value===!0){const b=E(R(s.modelValue));return s.fillMask!==!1?w(b):b}return s.modelValue}function g(b){if(b<n.length)return n.slice(-b);let x="",D=n;const C=D.indexOf(Te);if(C!==-1){for(let M=b-D.length;M>0;M--)x+=Te;D=D.slice(0,C)+x+D.slice(C)}return D}function p(){if(c.value=s.mask!==void 0&&s.mask.length!==0&&f(),c.value===!1){o=void 0,n="",r="";return}const b=Ba[s.mask]===void 0?s.mask:Ba[s.mask],x=typeof s.fillMask=="string"&&s.fillMask.length!==0?s.fillMask.slice(0,1):"_",D=x.replace(Ua,"\\$&"),C=[],M=[],N=[];let Y=s.reverseFillMask===!0,K="",z="";b.replace(Xg,(k,L,q,ee,ie)=>{if(ee!==void 0){const H=ms[ee];N.push(H),z=H.negate,Y===!0&&(M.push("(?:"+z+"+)?("+H.pattern+"+)?(?:"+z+"+)?("+H.pattern+"+)?"),Y=!1),M.push("(?:"+z+"+)?("+H.pattern+")?")}else if(q!==void 0)K="\\"+(q==="\\"?"":q),N.push(q),C.push("([^"+K+"]+)?"+K+"?");else{const H=L!==void 0?L:ie;K=H==="\\"?"\\\\\\\\":H.replace(Ua,"\\\\$&"),N.push(H),C.push("([^"+K+"]+)?"+K+"?")}});const V=new RegExp("^"+C.join("")+"("+(K===""?".":"[^"+K+"]")+"+)?"+(K===""?"":"["+K+"]*")+"$"),B=M.length-1,$=M.map((k,L)=>L===0&&s.reverseFillMask===!0?new RegExp("^"+D+"*"+k):L===B?new RegExp("^"+k+"("+(z===""?".":z)+"+)?"+(s.reverseFillMask===!0?"$":D+"*")):new RegExp("^"+k));a=N,o=k=>{const L=V.exec(s.reverseFillMask===!0?k:k.slice(0,N.length+1));L!==null&&(k=L.slice(1).join(""));const q=[],ee=$.length;for(let ie=0,H=k;ie<ee;ie++){const j=$[ie].exec(H);if(j===null)break;H=H.slice(j.shift().length),q.push(...j)}return q.length!==0?q.join(""):k},n=N.map(k=>typeof k=="string"?k:Te).join(""),r=n.split(Te).join(x)}function m(b,x,D){const C=i.value,M=C.selectionEnd,N=C.value.length-M,Y=R(b);x===!0&&p();const K=E(Y),z=s.fillMask!==!1?w(K):K,V=d.value!==z;C.value!==z&&(C.value=z),V===!0&&(d.value=z),document.activeElement===C&&Se(()=>{if(z===r){const $=s.reverseFillMask===!0?r.length:0;C.setSelectionRange($,$,"forward");return}if(D==="insertFromPaste"&&s.reverseFillMask!==!0){const $=C.selectionEnd;let k=M-1;for(let L=l;L<=k&&L<$;L++)n[L]!==Te&&k++;S.right(C,k);return}if(["deleteContentBackward","deleteContentForward"].indexOf(D)!==-1){const $=s.reverseFillMask===!0?M===0?z.length>K.length?1:0:Math.max(0,z.length-(z===r?0:Math.min(K.length,N)+1))+1:M;C.setSelectionRange($,$,"forward");return}if(s.reverseFillMask===!0)if(V===!0){const $=Math.max(0,z.length-(z===r?0:Math.min(K.length,N+1)));$===1&&M===1?C.setSelectionRange($,$,"forward"):S.rightReverse(C,$)}else{const $=z.length-N;C.setSelectionRange($,$,"backward")}else if(V===!0){const $=Math.max(0,n.indexOf(Te),Math.min(K.length,M)-1);S.right(C,$)}else{const $=M-1;S.right(C,$)}});const B=s.unmaskedValue===!0?R(z):z;String(s.modelValue)!==B&&(s.modelValue!==null||B!=="")&&t(B,!0)}function y(b,x,D){const C=E(R(b.value));x=Math.max(0,n.indexOf(Te),Math.min(C.length,x)),l=x,b.setSelectionRange(x,D,"forward")}const S={left(b,x){const D=n.slice(x-1).indexOf(Te)===-1;let C=Math.max(0,x-1);for(;C>=0;C--)if(n[C]===Te){x=C,D===!0&&x++;break}if(C<0&&n[x]!==void 0&&n[x]!==Te)return S.right(b,0);x>=0&&b.setSelectionRange(x,x,"backward")},right(b,x){const D=b.value.length;let C=Math.min(D,x+1);for(;C<=D;C++)if(n[C]===Te){x=C;break}else n[C-1]===Te&&(x=C);if(C>D&&n[x-1]!==void 0&&n[x-1]!==Te)return S.left(b,D);b.setSelectionRange(x,x,"forward")},leftReverse(b,x){const D=g(b.value.length);let C=Math.max(0,x-1);for(;C>=0;C--)if(D[C-1]===Te){x=C;break}else if(D[C]===Te&&(x=C,C===0))break;if(C<0&&D[x]!==void 0&&D[x]!==Te)return S.rightReverse(b,0);x>=0&&b.setSelectionRange(x,x,"backward")},rightReverse(b,x){const D=b.value.length,C=g(D),M=C.slice(0,x+1).indexOf(Te)===-1;let N=Math.min(D,x+1);for(;N<=D;N++)if(C[N-1]===Te){x=N,x>0&&M===!0&&x--;break}if(N>D&&C[x-1]!==void 0&&C[x-1]!==Te)return S.leftReverse(b,D);b.setSelectionRange(x,x,"forward")}};function T(b){e("click",b),u=void 0}function I(b){if(e("keydown",b),qa(b)===!0||b.altKey===!0)return;const x=i.value,D=x.selectionStart,C=x.selectionEnd;if(b.shiftKey||(u=void 0),b.keyCode===37||b.keyCode===39){b.shiftKey&&u===void 0&&(u=x.selectionDirection==="forward"?D:C);const M=S[(b.keyCode===39?"right":"left")+(s.reverseFillMask===!0?"Reverse":"")];if(b.preventDefault(),M(x,u===D?C:D),b.shiftKey){const N=x.selectionStart;x.setSelectionRange(Math.min(u,N),Math.max(u,N),"forward")}}else b.keyCode===8&&s.reverseFillMask!==!0&&D===C?(S.left(x,D),x.setSelectionRange(x.selectionStart,C,"backward")):b.keyCode===46&&s.reverseFillMask===!0&&D===C&&(S.rightReverse(x,C),x.setSelectionRange(D,x.selectionEnd,"forward"))}function E(b){if(b==null||b==="")return"";if(s.reverseFillMask===!0)return P(b);const x=a;let D=0,C="";for(let M=0;M<x.length;M++){const N=b[D],Y=x[M];if(typeof Y=="string")C+=Y,N===Y&&D++;else if(N!==void 0&&Y.regex.test(N))C+=Y.transform!==void 0?Y.transform(N):N,D++;else return C}return C}function P(b){const x=a,D=n.indexOf(Te);let C=b.length-1,M="";for(let N=x.length-1;N>=0&&C!==-1;N--){const Y=x[N];let K=b[C];if(typeof Y=="string")M=Y+M,K===Y&&C--;else if(K!==void 0&&Y.regex.test(K))do M=(Y.transform!==void 0?Y.transform(K):K)+M,C--,K=b[C];while(D===N&&K!==void 0&&Y.regex.test(K));else return M}return M}function R(b){return typeof b!="string"||o===void 0?typeof b=="number"?o(""+b):b:o(b)}function w(b){return r.length-b.length<=0?b:s.reverseFillMask===!0&&b.length!==0?r.slice(0,-b.length)+b:b+r.slice(b.length)}return{innerValue:d,hasMask:c,moveCursorForPaste:y,updateMaskValue:m,onMaskedKeydown:I,onMaskedClick:T}}function Jg(s,e){function t(){const i=s.modelValue;try{const n="DataTransfer"in window?new DataTransfer:"ClipboardEvent"in window?new ClipboardEvent("").clipboardData:void 0;return Object(i)===i&&("length"in i?Array.from(i):[i]).forEach(r=>{n.items.add(r)}),{files:n.files}}catch{return{files:void 0}}}return U(()=>{if(s.type==="file")return t()})}const em=ke({name:"QInput",inheritAttrs:!1,props:{...Ts,...Qg,...zn,modelValue:[String,Number,FileList],shadowText:String,type:{type:String,default:"text"},debounce:[String,Number],autogrow:Boolean,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...qn,"paste","change","keydown","click","animationend"],setup(s,{emit:e,attrs:t}){const{proxy:i}=Ee(),{$q:n}=i,r={};let a=NaN,o,l,u=null,c;const d=te(null),f=Dl(s),{innerValue:h,hasMask:g,moveCursorForPaste:p,updateMaskValue:m,onMaskedKeydown:y,onMaskedClick:S}=Zg(s,e,K,d),T=Jg(s),I=U(()=>vi(h.value)),E=wl(N),P=Wn({changeEvent:!0}),R=U(()=>s.type==="textarea"||s.autogrow===!0),w=U(()=>R.value===!0||["text","search","url","tel","password"].includes(s.type)),b=U(()=>{const L={...P.splitAttrs.listeners.value,onInput:N,onPaste:M,onChange:V,onBlur:B,onFocus:ft};return L.onCompositionstart=L.onCompositionupdate=L.onCompositionend=E,g.value===!0&&(L.onKeydown=y,L.onClick=S),s.autogrow===!0&&(L.onAnimationend=Y),L}),x=U(()=>{const L={tabindex:0,"data-autofocus":s.autofocus===!0||void 0,rows:s.type==="textarea"?6:void 0,"aria-label":s.label,name:f.value,...P.splitAttrs.attributes.value,id:P.targetUid.value,maxlength:s.maxlength,disabled:s.disable===!0,readonly:s.readonly===!0};return R.value===!1&&(L.type=s.type),s.autogrow===!0&&(L.rows=1),L});le(()=>s.type,()=>{d.value&&(d.value.value=s.modelValue)}),le(()=>s.modelValue,L=>{if(g.value===!0){if(l===!0&&(l=!1,String(L)===a))return;m(L)}else h.value!==L&&(h.value=L,s.type==="number"&&r.hasOwnProperty("value")===!0&&(o===!0?o=!1:delete r.value));s.autogrow===!0&&Se(z)}),le(()=>s.autogrow,L=>{L===!0?Se(z):d.value!==null&&t.rows>0&&(d.value.style.height="auto")}),le(()=>s.dense,()=>{s.autogrow===!0&&Se(z)});function D(){Ss(()=>{const L=document.activeElement;d.value!==null&&d.value!==L&&(L===null||L.id!==P.targetUid.value)&&d.value.focus({preventScroll:!0})})}function C(){d.value?.select()}function M(L){if(g.value===!0&&s.reverseFillMask!==!0){const q=L.target;p(q,q.selectionStart,q.selectionEnd)}e("paste",L)}function N(L){if(!L||!L.target)return;if(s.type==="file"){e("update:modelValue",L.target.files);return}const q=L.target.value;if(L.target.qComposing===!0){r.value=q;return}if(g.value===!0)m(q,!1,L.inputType);else if(K(q),w.value===!0&&L.target===document.activeElement){const{selectionStart:ee,selectionEnd:ie}=L.target;ee!==void 0&&ie!==void 0&&Se(()=>{L.target===document.activeElement&&q.indexOf(L.target.value)===0&&L.target.setSelectionRange(ee,ie)})}s.autogrow===!0&&z()}function Y(L){e("animationend",L),z()}function K(L,q){c=()=>{u=null,s.type!=="number"&&r.hasOwnProperty("value")===!0&&delete r.value,s.modelValue!==L&&a!==L&&(a=L,q===!0&&(l=!0),e("update:modelValue",L),Se(()=>{a===L&&(a=NaN)})),c=void 0},s.type==="number"&&(o=!0,r.value=L),s.debounce!==void 0?(u!==null&&clearTimeout(u),r.value=L,u=setTimeout(c,s.debounce)):c()}function z(){requestAnimationFrame(()=>{const L=d.value;if(L!==null){const q=L.parentNode.style,{scrollTop:ee}=L,{overflowY:ie,maxHeight:H}=n.platform.is.firefox===!0?{}:window.getComputedStyle(L),j=ie!==void 0&&ie!=="scroll";j===!0&&(L.style.overflowY="hidden"),q.marginBottom=L.scrollHeight-1+"px",L.style.height="1px",L.style.height=L.scrollHeight+"px",j===!0&&(L.style.overflowY=parseInt(H,10)<L.scrollHeight?"auto":"hidden"),q.marginBottom="",L.scrollTop=ee}})}function V(L){E(L),u!==null&&(clearTimeout(u),u=null),c?.(),e("change",L.target.value)}function B(L){L!==void 0&&ft(L),u!==null&&(clearTimeout(u),u=null),c?.(),o=!1,l=!1,delete r.value,s.type!=="file"&&setTimeout(()=>{d.value!==null&&(d.value.value=h.value!==void 0?h.value:"")})}function $(){return r.hasOwnProperty("value")===!0?r.value:h.value!==void 0?h.value:""}Be(()=>{B()}),gt(()=>{s.autogrow===!0&&z()}),Object.assign(P,{innerValue:h,fieldClass:U(()=>`q-${R.value===!0?"textarea":"input"}`+(s.autogrow===!0?" q-textarea--autogrow":"")),hasShadow:U(()=>s.type!=="file"&&typeof s.shadowText=="string"&&s.shadowText.length!==0),inputRef:d,emitValue:K,hasValue:I,floatingLabel:U(()=>I.value===!0&&(s.type!=="number"||isNaN(h.value)===!1)||vi(s.displayValue)),getControl:()=>W(R.value===!0?"textarea":"input",{ref:d,class:["q-field__native q-placeholder",s.inputClass],style:s.inputStyle,...x.value,...b.value,...s.type!=="file"?{value:$()}:T.value}),getShadowControl:()=>W("div",{class:"q-field__native q-field__shadow absolute-bottom no-pointer-events"+(R.value===!0?"":" text-no-wrap")},[W("span",{class:"invisible"},$()),W("span",s.shadowText)])});const k=Yn(P);return Object.assign(i,{focus:D,select:C,getNativeElement:()=>d.value}),Rn(i,"nativeEl",()=>d.value),k}});function tm(s,e){const t=te(null),i=U(()=>s.disable===!0?null:W("span",{ref:t,class:"no-outline",tabindex:-1}));function n(r){const a=e.value;r?.qAvoidFocus!==!0&&(r?.type.indexOf("key")===0?document.activeElement!==a&&a?.contains(document.activeElement)===!0&&a.focus():t.value!==null&&(r===void 0||a?.contains(r.target)===!0)&&t.value.focus())}return{refocusTargetEl:i,refocusTarget:n}}const im={xs:30,sm:35,md:40,lg:50,xl:60},sm={...Pt,...Xa,...zn,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:s=>s==="tf"||s==="ft"},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},nm=["update:modelValue"];function rm(s,e){const{props:t,slots:i,emit:n,proxy:r}=Ee(),{$q:a}=r,o=Ft(t,a),l=te(null),{refocusTargetEl:u,refocusTarget:c}=tm(t,l),d=Qa(t,im),f=U(()=>t.val!==void 0&&Array.isArray(t.modelValue)),h=U(()=>{const C=Kt(t.val);return f.value===!0?t.modelValue.findIndex(M=>Kt(M)===C):-1}),g=U(()=>f.value===!0?h.value!==-1:Kt(t.modelValue)===Kt(t.trueValue)),p=U(()=>f.value===!0?h.value===-1:Kt(t.modelValue)===Kt(t.falseValue)),m=U(()=>g.value===!1&&p.value===!1),y=U(()=>t.disable===!0?-1:t.tabindex||0),S=U(()=>`q-${s} cursor-pointer no-outline row inline no-wrap items-center`+(t.disable===!0?" disabled":"")+(o.value===!0?` q-${s}--dark`:"")+(t.dense===!0?` q-${s}--dense`:"")+(t.leftLabel===!0?" reverse":"")),T=U(()=>{const C=g.value===!0?"truthy":p.value===!0?"falsy":"indet",M=t.color!==void 0&&(t.keepColor===!0||g.value===!0)?` text-${t.color}`:"";return`q-${s}__inner relative-position non-selectable q-${s}__inner--${C}${M}`}),I=U(()=>{const C={type:"checkbox"};return t.name!==void 0&&Object.assign(C,{".checked":g.value,"^checked":g.value===!0?"checked":void 0,name:t.name,value:f.value===!0?t.val:t.trueValue}),C}),E=Kg(I),P=U(()=>{const C={tabindex:y.value,role:"switch","aria-label":t.label,"aria-checked":m.value===!0?"mixed":g.value===!0?"true":"false"};return t.disable===!0&&(C["aria-disabled"]="true"),C});function R(C){C!==void 0&&(Ne(C),c(C)),t.disable!==!0&&n("update:modelValue",w(),C)}function w(){if(f.value===!0){if(g.value===!0){const C=t.modelValue.slice();return C.splice(h.value,1),C}return t.modelValue.concat([t.val])}if(g.value===!0){if(t.toggleOrder!=="ft"||t.toggleIndeterminate===!1)return t.falseValue}else if(p.value===!0){if(t.toggleOrder==="ft"||t.toggleIndeterminate===!1)return t.trueValue}else return t.toggleOrder!=="ft"?t.trueValue:t.falseValue;return t.indeterminateValue}function b(C){(C.keyCode===13||C.keyCode===32)&&Ne(C)}function x(C){(C.keyCode===13||C.keyCode===32)&&R(C)}const D=e(g,m);return Object.assign(r,{toggle:R}),()=>{const C=D();t.disable!==!0&&E(C,"unshift",` q-${s}__native absolute q-ma-none q-pa-none`);const M=[W("div",{class:T.value,style:d.value,"aria-hidden":"true"},C)];u.value!==null&&M.push(u.value);const N=t.label!==void 0?ja(i.default,[t.label]):Fe(i.default);return N!==void 0&&M.push(W("div",{class:`q-${s}__label q-anchor--skip`},N)),W("div",{ref:l,class:S.value,...P.value,onClick:R,onKeydown:b,onKeyup:x},M)}}const am=ke({name:"QToggle",props:{...sm,icon:String,iconColor:String},emits:nm,setup(s){function e(t,i){const n=U(()=>(t.value===!0?s.checkedIcon:i.value===!0?s.indeterminateIcon:s.uncheckedIcon)||s.icon),r=U(()=>t.value===!0?s.iconColor:null);return()=>[W("div",{class:"q-toggle__track"}),W("div",{class:"q-toggle__thumb absolute flex flex-center no-wrap"},n.value!==void 0?[W(Rt,{name:n.value,color:r.value})]:void 0)]}return rm("toggle",e)}}),om=["ul","ol"],lm=ke({name:"QList",props:{...Pt,bordered:Boolean,dense:Boolean,separator:Boolean,padding:Boolean,tag:{type:String,default:"div"}},setup(s,{slots:e}){const t=Ee(),i=Ft(s,t.proxy.$q),n=U(()=>om.includes(s.tag)?null:"list"),r=U(()=>"q-list"+(s.bordered===!0?" q-list--bordered":"")+(s.dense===!0?" q-list--dense":"")+(s.separator===!0?" q-list--separator":"")+(i.value===!0?" q-list--dark":"")+(s.padding===!0?" q-list--padding":""));return()=>W(s.tag,{class:r.value,role:n.value},Fe(e.default))}}),um=ke({name:"QCardActions",props:{...Su,vertical:Boolean},setup(s,{slots:e}){const t=Tu(s),i=U(()=>`q-card__actions ${t.value} q-card__actions--${s.vertical===!0?"vert column":"horiz row"}`);return()=>W("div",{class:i.value},Fe(e.default))}}),cm=ke({name:"QCard",props:{...Pt,tag:{type:String,default:"div"},square:Boolean,flat:Boolean,bordered:Boolean},setup(s,{slots:e}){const{proxy:{$q:t}}=Ee(),i=Ft(s,t),n=U(()=>"q-card"+(i.value===!0?" q-card--dark q-dark":"")+(s.bordered===!0?" q-card--bordered":"")+(s.square===!0?" q-card--square no-border-radius":"")+(s.flat===!0?" q-card--flat no-shadow":""));return()=>W(s.tag,{class:n.value},Fe(e.default))}});function $a(s){if(s===!1)return 0;if(s===!0||s===void 0)return 1;const e=parseInt(s,10);return isNaN(e)?0:e}const dm=au({name:"close-popup",beforeMount(s,{value:e}){const t={depth:$a(e),handler(i){t.depth!==0&&setTimeout(()=>{const n=Tg(s);n!==void 0&&xg(n,i,t.depth)})},handlerKey(i){yi(i,13)===!0&&t.handler(i)}};s.__qclosepopup=t,s.addEventListener("click",t.handler),s.addEventListener("keyup",t.handlerKey)},updated(s,{value:e,oldValue:t}){e!==t&&(s.__qclosepopup.depth=$a(e))},beforeUnmount(s){const e=s.__qclosepopup;s.removeEventListener("click",e.handler),s.removeEventListener("keyup",e.handlerKey),delete s.__qclosepopup}}),fm={name:"ConfigMenu",props:{modelValue:{type:Boolean,default:!1},currentStreams:{type:Array,default:()=>[],validator:s=>Array.isArray(s)},currentMutedState:{type:Array,default:()=>[],validator:s=>Array.isArray(s)}},emits:["update:modelValue","updateConfig"],setup(s,{emit:e}){const t=te(s.modelValue),i=te([1,2,4,5,6,9]),n=te(Math.max(1,s.currentStreams.length||1)),r=te([]),a=te([]),o=te([]),l=te([]),u=te([]),c="https://trleahy.github.io/API-Endpoints/visorChannels/channels.json",d={label:"Custom URL",value:"custom"},f=async()=>{try{const m=await fetch(c);if(!m.ok)throw new Error(`HTTP error! status: ${m.status}`);const y=await m.json();r.value=Object.keys(y).map(S=>({label:y[S].name,value:y[S].url})),a.value=[...r.value,d]}catch(m){console.error("Failed to load channels:",m),a.value=[d]}};gt(async()=>{await f(),h()});const h=()=>{const m=n.value,y=a.value[0]?.value||"custom";o.value=[],l.value=[],u.value=[];for(let S=0;S<m;S++){const T=s.currentStreams[S],I=a.value.find(E=>E.value===T);I?(o.value.push(I.value),l.value.push("")):T?(o.value.push("custom"),l.value.push(T)):(o.value.push(y),l.value.push("")),u.value.push(s.currentMutedState[S]??!0)}};return le(n,(m,y)=>{const S=a.value[0]?.value||"custom";if(m>y)for(let T=y;T<m;T++)o.value.push(S),l.value.push(""),u.value.push(!0);else m<y&&(o.value.splice(m),l.value.splice(m),u.value.splice(m))}),le(()=>s.modelValue,m=>{t.value=m,m&&h()}),le(t,m=>{e("update:modelValue",m)}),{show:t,streamCountOptions:i,streamCount:n,streamOptions:a,selectedStreams:o,customUrls:l,mutedStreams:u,updateStream:m=>{o.value[m]!=="custom"&&(l.value[m]="")},applyConfig:()=>{const m=o.value.map((S,T)=>S==="custom"?l.value[T]?.trim()||"":S);if(m.filter(S=>S&&S.length>0).length===0){console.warn("No valid streams configured");return}e("updateConfig",{streams:m,mutedState:[...u.value]}),t.value=!1}}}},hm={class:"q-mt-sm"};function gm(s,e,t,i,n,r){return At(),Ki(Il,{modelValue:i.show,"onUpdate:modelValue":e[1]||(e[1]=a=>i.show=a)},{default:Xe(()=>[Me(cm,{class:"config-menu"},{default:Xe(()=>[Me(Xs,null,{default:Xe(()=>e[2]||(e[2]=[ci("div",{class:"text-h6"},"Configuration",-1)])),_:1}),Me(Xs,null,{default:Xe(()=>[Me(Na,{modelValue:i.streamCount,"onUpdate:modelValue":e[0]||(e[0]=a=>i.streamCount=a),options:i.streamCountOptions,label:"Number of Streams",dense:"",outlined:""},null,8,["modelValue","options"])]),_:1}),Me(Xs,null,{default:Xe(()=>[Me(lm,null,{default:Xe(()=>[(At(!0),on(Wa,null,Ya(i.streamCount,(a,o)=>(At(),Ki(dl,{key:o},{default:Xe(()=>[Me(fl,null,{default:Xe(()=>[Me(Na,{modelValue:i.selectedStreams[o],"onUpdate:modelValue":[l=>i.selectedStreams[o]=l,l=>i.updateStream(o)],options:i.streamOptions,label:"Select Channel for Stream "+(o+1),"emit-value":"","map-options":"","option-label":"label","option-value":"value",dense:"",outlined:""},null,8,["modelValue","onUpdate:modelValue","options","label"]),i.selectedStreams[o]==="custom"?(At(),Ki(em,{key:0,modelValue:i.customUrls[o],"onUpdate:modelValue":l=>i.customUrls[o]=l,label:"Enter Custom URL",dense:"",filled:""},null,8,["modelValue","onUpdate:modelValue"])):ou("",!0),ci("div",hm,[Me(am,{modelValue:i.mutedStreams[o],"onUpdate:modelValue":l=>i.mutedStreams[o]=l,label:"Mute",color:"primary"},null,8,["modelValue","onUpdate:modelValue"])])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1}),Me(um,{align:"right"},{default:Xe(()=>[lu(Me(hr,{label:"Cancel",color:"grey"},null,512),[[dm]]),Me(hr,{label:"Apply",color:"primary",onClick:i.applyConfig},null,8,["onClick"])]),_:1})]),_:1})]),_:1},8,["modelValue"])}const mm=Ja(fm,[["render",gm],["__scopeId","data-v-343d0be0"]]),pm={name:"IndexPage",components:{ConfigMenu:mm},setup(){const s="https://vs-hls-push-ww-live.akamaized.net/x=4/i=urn:bbc:pips:service:bbc_news_channel_hd/t=3840/v=pv10/b=1604032/main.m3u8",e=bi(Array(2).fill(s)),t=bi(Array(2).fill(!0)),i=te(!1),n=bi([]),r=bi({}),a=new Map,o=te(!1),l=Rs({1:{columns:"1fr",rows:"1fr"},2:{columns:"repeat(2, 1fr)",rows:"1fr"},4:{columns:"repeat(2, 1fr)",rows:"repeat(2, 1fr)"},5:{columns:"repeat(3, 1fr)",rows:"repeat(4, 1fr)"},6:{columns:"repeat(3, 1fr)",rows:"repeat(3, 1fr)"},9:{columns:"repeat(3, 1fr)",rows:"repeat(3, 1fr)"}}),u=Rs({display:"grid",gap:"0px",width:"100vw",height:"100vh"}),c=U(()=>{const w=e.value.length,b=l[w];if(b)return{...u,gridTemplateColumns:b.columns,gridTemplateRows:b.rows};const x=Math.ceil(Math.sqrt(w)),D=Math.ceil(w/x);return{...u,gridTemplateColumns:`repeat(${x}, 1fr)`,gridTemplateRows:`repeat(${D}, 1fr)`}}),d=Rs({1:Object.freeze(["1 / 1 / 2 / 2"]),2:Object.freeze(["1 / 1 / 2 / 2","1 / 2 / 2 / 3"]),4:Object.freeze(["1 / 1 / 2 / 2","1 / 2 / 2 / 3","2 / 1 / 3 / 2","2 / 2 / 3 / 3"]),5:Object.freeze(["1 / 1 / 5 / 3","1 / 3 / 2 / 4","2 / 3 / 3 / 4","3 / 3 / 4 / 4","4 / 3 / 5 / 4"]),6:Object.freeze(["1 / 1 / 3 / 3","3 / 1 / 4 / 2","3 / 2 / 4 / 3","3 / 3 / 4 / 4","2 / 3 / 3 / 4","1 / 3 / 2 / 4"]),9:Object.freeze(["1 / 1 / 2 / 2","1 / 2 / 2 / 3","1 / 3 / 2 / 4","2 / 1 / 3 / 2","2 / 2 / 3 / 3","2 / 3 / 3 / 4","3 / 1 / 4 / 2","3 / 2 / 4 / 3","3 / 3 / 4 / 4"])}),f=new Map,h=w=>{const b=e.value.length,x=`${b}-${w}`;if(f.has(x))return f.get(x);const D=d[b];let C={};return D&&D[w]&&(C=Object.freeze({gridArea:D[w]})),f.set(x,C),C},g="https://trleahy.github.io/API-Endpoints/visorChannels/channels.json",p=async(w=0)=>{if(!o.value)try{const b=new AbortController,x=setTimeout(()=>b.abort(),1e4),D=await fetch(g,{signal:b.signal,cache:"force-cache"});if(clearTimeout(x),!D.ok)throw new Error(`HTTP error! status: ${D.status}`);const C=await D.json(),M=Object.create(null);for(const N in C)C[N]?.url&&C[N]?.name&&(M[C[N].url]=C[N].name);r.value=M,o.value=!0,a.clear()}catch(b){console.error("Failed to load channel data:",b),w<2&&(b.name==="AbortError"||b.name==="TypeError")?(console.log(`Retrying channel data load (attempt ${w+1})`),setTimeout(()=>p(w+1),2e3*(w+1))):(r.value=Object.create(null),o.value=!0)}},m=w=>{const b=e.value[w];if(!b)return`Stream ${w+1}`;if(a.has(b))return a.get(b);let x;const D=r.value[b];if(D)x=D;else if(b!==s)try{const N=new URL(b).hostname.replace(/^(?:www\.|m\.|mobile\.)/,"").replace(/\.(?:com|org|net|tv|co\.uk)$/,"");N&&N!=="localhost"?x=N.charAt(0).toUpperCase()+N.slice(1):x="Custom"}catch{x="Custom"}else x=`Stream ${w+1}`;return a.set(b,x),x},y=()=>{n.value.forEach(w=>{w&&w.destroy()}),n.value=[]},S=()=>{y(),requestAnimationFrame(()=>{const w=document.querySelectorAll(".video-player");Array.from(w).forEach((x,D)=>{const C=e.value[D]||s;try{if(We.isSupported()){const N=new We({enableWorker:!1,lowLatencyMode:!0,maxBufferLength:30,maxMaxBufferLength:60,startLevel:-1,capLevelToPlayerSize:!0});let Y;N.on(We.Events.ERROR,(K,z)=>{clearTimeout(Y),Y=setTimeout(()=>{if(console.error(`HLS Error for stream ${D}:`,z),z.fatal)switch(z.type){case We.ErrorTypes.NETWORK_ERROR:N.startLoad();break;case We.ErrorTypes.MEDIA_ERROR:N.recoverMediaError();break;default:N.destroy(),n.value[D]=null;break}},100)}),N.loadSource(C),N.attachMedia(x),n.value[D]=N}else x.canPlayType("application/vnd.apple.mpegurl")||console.warn(`HLS not supported for stream ${D}`),x.src=C;const M=t.value[D]??!0;x.muted!==M&&(x.muted=M)}catch(M){console.error(`Error attaching stream ${D}:`,M)}})})},T=w=>{w.key==="F2"&&(i.value=!0)},I=()=>{requestAnimationFrame(()=>{const w=document.querySelectorAll(".video-player");for(let b=0;b<w.length;b++){const x=w[b],D=t.value[b];x&&D!==void 0&&x.muted!==D&&(x.muted=D)}})},E=w=>{w.streams&&Array.isArray(w.streams)&&(e.value=w.streams),w.mutedState&&Array.isArray(w.mutedState)&&(t.value=w.mutedState)};gt(async()=>{await p(),Se(()=>{S()}),window.addEventListener("keydown",T)}),Ha(()=>{window.removeEventListener("keydown",T),y(),clearTimeout(P),clearTimeout(R),a.clear(),f.clear()});let P;le(e,()=>{clearTimeout(P),P=setTimeout(()=>{Se(()=>{S(),a.clear(),f.clear()})},100)},{flush:"post"});let R;return le(t,()=>{clearTimeout(R),R=setTimeout(()=>{I()},50)},{flush:"post"}),le(r,()=>{a.clear()},{flush:"post"}),{streamUrls:e,mutedState:t,showConfigMenu:i,containerStyle:c,getItemStyle:h,getChannelName:m,updateConfig:E}}},vm=["muted"],ym={class:"channel-label"};function Sm(s,e,t,i,n,r){const a=uu("ConfigMenu");return At(),Ki(Iu,{class:"flex flex-center"},{default:Xe(()=>[ci("div",{class:"grid-container",style:fr(i.containerStyle)},[(At(!0),on(Wa,null,Ya(i.streamUrls,(o,l)=>(At(),on("div",{key:l,class:"video-wrapper",style:fr(i.getItemStyle(l))},[ci("video",{class:"video-player",autoplay:"",muted:i.mutedState[l]},null,8,vm),ci("div",ym,cu(i.getChannelName(l)),1)],4))),128))],4),Me(a,{modelValue:i.showConfigMenu,"onUpdate:modelValue":e[0]||(e[0]=o=>i.showConfigMenu=o),currentStreams:i.streamUrls,currentMutedState:i.mutedState,onUpdateConfig:i.updateConfig},null,8,["modelValue","currentStreams","currentMutedState","onUpdateConfig"])]),_:1})}const Rm=Ja(pm,[["render",Sm],["__scopeId","data-v-a2e555b0"]]);export{Rm as default};

{"name": "visor", "version": "0.0.1", "description": "Visor - HLS stream multiviewer.", "productName": "Visor", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "module", "private": true, "dependencies": {"@electron/remote": "2.1.2", "hls.js": "1.5.20", "quasar": "2.18.1", "vue": "3.5.13", "vue-router": "4.5.0"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}, "main": "./electron-main.js"}
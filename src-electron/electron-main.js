import { app, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron'
import path from 'node:path'
import os from 'node:os'
import fs from 'node:fs/promises'
import { fileURLToPath } from 'node:url'

// needed in case process is undefined under Linux
const platform = process.platform || os.platform()

const currentDir = fileURLToPath(new URL('.', import.meta.url))

let mainWindow

async function createWindow() {
  /**
   * Initial window options
   */
  const width = 1000;
  const height = Math.round(width / 1.78);

  mainWindow = new BrowserWindow({
    icon: path.resolve(currentDir, 'icons/icon.png'), // tray icon
    width: width,
    height: height,
    useContentSize: true,
    // frame: false,
    // fullscreen: true,
    webPreferences: {
      sandbox: false,
      contextIsolation: true,
      preload: path.resolve(
        currentDir,
        path.join(
          process.env.QUASAR_ELECTRON_PRELOAD_FOLDER,
          'electron-preload' + process.env.QUASAR_ELECTRON_PRELOAD_EXTENSION,
        ),
      ),
    },
  });

  // Set the aspect ratio to 16:9
  mainWindow.setAspectRatio(16 / 9);

  if (process.env.DEV) {
    await mainWindow.loadURL(process.env.APP_URL);
  } else {
    await mainWindow.loadFile('index.html');
  }

  if (process.env.DEBUGGING) {
    // if on DEV or Production with debug enabled
    mainWindow.webContents.openDevTools();
  } else {
    // we're on production; no access to devtools pls
    mainWindow.webContents.on('devtools-opened', () => {
      mainWindow.webContents.closeDevTools();
    });
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Get the default preset directory
const getPresetDirectory = () => {
  const homeDir = os.homedir()
  return path.join(homeDir, 'Documents', 'Visor')
}

// Ensure the preset directory exists
const ensurePresetDirectory = async () => {
  const presetDir = getPresetDirectory()
  try {
    await fs.access(presetDir)
  } catch (error) {
    if (error.code === 'ENOENT') {
      await fs.mkdir(presetDir, { recursive: true })
    } else {
      throw error
    }
  }
  return presetDir
}

// IPC handlers for file operations
ipcMain.handle('save-preset', async (event, presetData) => {
  try {
    const presetDir = await ensurePresetDirectory()

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
    const filename = `visor-preset-${timestamp}.json`
    const filePath = path.join(presetDir, filename)

    // Save the preset data as JSON
    await fs.writeFile(filePath, JSON.stringify(presetData, null, 2), 'utf8')

    return {
      success: true,
      filePath,
      filename
    }
  } catch (error) {
    console.error('Error saving preset:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

ipcMain.handle('load-preset', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: 'Load Visor Preset',
      defaultPath: getPresetDirectory(),
      filters: [
        { name: 'Visor Presets', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      properties: ['openFile']
    })

    if (result.canceled || !result.filePaths.length) {
      return { success: false, canceled: true }
    }

    const filePath = result.filePaths[0]
    const fileContent = await fs.readFile(filePath, 'utf8')
    const presetData = JSON.parse(fileContent)

    return {
      success: true,
      data: presetData,
      filePath
    }
  } catch (error) {
    console.error('Error loading preset:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

ipcMain.handle('show-save-dialog', async (event, options) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'Save Visor Preset',
      defaultPath: path.join(getPresetDirectory(), 'visor-preset.json'),
      filters: [
        { name: 'Visor Presets', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      ...options
    })

    return result
  } catch (error) {
    console.error('Error showing save dialog:', error)
    return { canceled: true, error: error.message }
  }
})

ipcMain.handle('show-open-dialog', async (event, options) => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: 'Load Visor Preset',
      defaultPath: getPresetDirectory(),
      filters: [
        { name: 'Visor Presets', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      properties: ['openFile'],
      ...options
    })

    return result
  } catch (error) {
    console.error('Error showing open dialog:', error)
    return { canceled: true, error: error.message }
  }
})

app.setAboutPanelOptions({
  applicationName: 'Visor',
  applicationVersion: 'v1.0.0-alpha',
  version: 'Build v1.0.1',
  copyright: '© 2025. All rights reserved.',
})

app.whenReady().then(() => {
  createWindow()
})

app.on('window-all-closed', () => {
  if (platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow()
  }
})
